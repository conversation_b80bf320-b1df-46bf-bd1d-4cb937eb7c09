 DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchPayrollUploadBatchHeaders$$

CREATE PROCEDURE proc_getSchPayrollUploadBatchHeaders ()  

BEGIN



  

    create temporary table tmp
   
        SELECT  a.Id AS PayrollBatchNumber,
                                        cast(a.Id as unsigned) as PayrollBatchNumberSort,
                                        DATE_FORMAT( BatchDate, '%m-%d-%Y' ) AS BatchDate, 
                                        BatchCount,
                                        CONCAT( trim( b.FirstName) , ' ', trim( b.LastName)) as UserName ,  
                                        DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
                                    FROM    SchPayrollBatchHeader a, 
                                            Users b
                                    WHERE   a.UserId = b.UserId     
                                    AND     a.Id != '0'
                                    and DATEDIFF( curdate( ),a.BatchDate ) < 60
                                    ORDER BY PayrollBatchNumberSort DESC  
                                    ;

  

    create temporary table tmp1
    SELECT distinct PayrollBatchNumber
      FROM WeeklyServices b
        WHERE b.PayrollBatchNumber != ''
            and abs(DATEDIFF(   curdate( ),b.TransDate )) < 60 ;

  SELECT * from tmp
    where exists (

        SELECT  1 FROM tmp1 b 
             WHERE tmp.PayrollBatchNumber = b.PayrollBatchNumber
 

        )
    ORDER BY PayrollBatchNumberSort DESC 
  ;         

  drop temporary table if exists tmp;
  drop temporary table if exists tmp1;
   
  
END $$

DELIMITER ;  
 
 
  