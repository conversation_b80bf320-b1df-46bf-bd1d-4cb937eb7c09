<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();


	$AssignmentId = $_GET['AssignmentId'];
	$IncludeSundayFL = $_GET['IncludeSundayFL'];
 
	if ($IncludeSundayFL != '1') {

		$DaysList = "5"  ;

	} else {

		$DaysList = "6"  ;


	} 
  
    
	if ($DaysList == '5') {

    $query = "	SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE   
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE  
											WHEN a.EndTime THEN a.EndTime 
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE   
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )  
										END AS StartTimeFrm,
										
                        				CASE   
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE   
											WHEN a.TotalHours THEN a.TotalHours 
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchStudentAssignmentDetails a

						RIGHT JOIN DaysOfWeek5 b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = '{$AssignmentId}'
						AND b.WeekDayId NOT IN (0,6)


						LEFT JOIN Users c 
						ON a.Userid = c.Userid	
               ";

   } else {

   $query = "	SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE   
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE  
											WHEN a.EndTime THEN a.EndTime 
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE   
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )  
										END AS StartTimeFrm,
										
                        				CASE   
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' ) 
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE   
											WHEN a.TotalHours THEN a.TotalHours 
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchStudentAssignmentDetails a

						RIGHT JOIN DaysOfWeek6 b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = '{$AssignmentId}'
						-- AND b.WeekDayId NOT IN (0,6)


						LEFT JOIN Users c 
						ON a.Userid = c.Userid	
               ";


    }           

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	// echo $query;


	// require "ewDataHandler.php";  	  
	// $rcr_transaction = new dataHandler(); 


	// $AssignmentId = $_GET['AssignmentId'];
	// $IncludeSundayFL = $_GET['IncludeSundayFL'];
 
	// if ($IncludeSundayFL != '1') {

	// 	$DaysList = "5"  ;

	// } else {

	// 	$DaysList = "6"  ;


	// }

	// $result = $rcr_transaction->getSchStudentAssignmentDetails($AssignmentId, $DaysList);


	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";
  
?>
