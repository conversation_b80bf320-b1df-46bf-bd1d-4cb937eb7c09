<?php


    // error_reporting(E_ALL);
    // ini_set('display_errors', TRUE);
    // ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
     
  

  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  $ServiceTypeId = $_GET['ServiceTypeId'];
  
  $DistrictId = $_GET['DistrictId'];
  $SchoolId = $_GET['SchoolId'];
  $TransTypeId = $_GET['TransTypeId'];
  $RnId = $_GET['RnId'];
  $RNLiaisonId = $_GET['RNLiaisonId'];
  $PostingStatusId = $_GET['PostingStatusId'];
  $ConfirmationNumber = $_GET['ConfirmationNumber'];

   
    $charset = 'utf8mb4';


    // $ServiceTypeId = '';

    // switch ($TransTypeId) {
    //  case 'd':
    //    $ServiceTypeIdList =  '39,40,41,42';
    //    break;
    //  case 'w':
       $ServiceTypeIdList =  '43,44,45';
  //      break;
  //    case 'b':
  //      $ServiceTypeIdList =  '39,40,41,42,43,44,45';
  //      break;
  // } 

    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNSchoolReportsTransactions(?,?,?,?,?,?,?,?,?,?)");
    $stmt->execute([ $FromDate, 
                     $ToDate,
                     $ServiceTypeIdList, 
                     $DistrictId,
                     $SchoolId,
                     $ServiceTypeId,
                     $RnId,
                     $RNLiaisonId,
                     $PostingStatusId,
                     $ConfirmationNumber


                  ]);

  $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


 
    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A1:O1")->getFont()->setBold( true );

  
    // $objPHPExcel->getActiveSheet()->setCellValue('A1', 'Date');
    // $objPHPExcel->getActiveSheet()->setCellValue('B1', 'District');
    // $objPHPExcel->getActiveSheet()->setCellValue('C1', "School");
    // $objPHPExcel->getActiveSheet()->setCellValue('D1', 'School ID');
    // $objPHPExcel->getActiveSheet()->setCellValue('E1', "Today's Nurse");
    // $objPHPExcel->getActiveSheet()->setCellValue('F1', 'Long Term Nurse');
    // $objPHPExcel->getActiveSheet()->setCellValue('G1', 'Liaison');
    // $objPHPExcel->getActiveSheet()->setCellValue('H1', 'Student Name');
    // $objPHPExcel->getActiveSheet()->setCellValue('I1', 'OSIS #');
    // $objPHPExcel->getActiveSheet()->setCellValue('J1', 'Trip Location & Time');
    // $objPHPExcel->getActiveSheet()->setCellValue('K1', 'Confirmation #');
    // $objPHPExcel->getActiveSheet()->setCellValue('L1', 'Placement');
    // $objPHPExcel->getActiveSheet()->setCellValue('M1', 'AssignmentType');
    // $objPHPExcel->getActiveSheet()->setCellValue('N1', 'Posting Status');
    // $objPHPExcel->getActiveSheet()->setCellValue('O1', 'Posting Comments');
 
    $headers = ['Date', 'District', 'School', 'School ID', "Today's Nurse", 'Long Term Nurse', 'Liaison', 'Student Name', 'OSIS #', 'Trip Location & Time', 'Confirmation #', 'Placement', 'AssignmentType', 'Posting Status', 'Posting Comments'];

    // Set headers and initialize max lengths array
    $maxColumnWidths = [];
    foreach ($headers as $column => $header) {
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, 1, $header);
        $maxColumnWidths[$column] = strlen($header); // Initialize with header width
    }

    // Set results
    $row = 2;
    foreach ($results as $result) {

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $result['ServiceDate']); 
       $maxColumnWidths[0] = strlen($result['ServiceDate']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['DistrictName']);  
       $maxColumnWidths[1] = strlen($result['DistrictName']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['SchoolName']); 
       $maxColumnWidths[2] = strlen($result['SchoolName']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['SesisSchoolId']);
       $maxColumnWidths[3] = strlen($result['SesisSchoolId']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['CallInStatus']); 
       $maxColumnWidths[4] = strlen($result['CallInStatus']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['LongTermRnName']); 
       $maxColumnWidths[5] = strlen($result['LongTermRnName']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['LiaisonName']); 
       $maxColumnWidths[6] = strlen($result['LiaisonName']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['StudentName']); 
       $maxColumnWidths[7] = strlen($result['StudentName']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['StudentOsisNumber']); 
       $maxColumnWidths[8] = strlen($result['StudentOsisNumber']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['ScheduleDesc']); 
       $maxColumnWidths[9] = strlen($result['ScheduleDesc']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['ConfirmationNumber']); 
       $maxColumnWidths[10] = strlen($result['ConfirmationNumber']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(11, $row, $result['Placement']); 
       $maxColumnWidths[11] = strlen($result['Placement']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['AssignmentType']); 
       $maxColumnWidths[12] = strlen($result['AssignmentType']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(13, $row, $result['PostingStatusDesc']); 
       $maxColumnWidths[13] = strlen($result['PostingStatusDesc']);  

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(14, $row, $result['PostingStatusComments']); 
       $maxColumnWidths[14] = strlen($result['PostingStatusComments']);  
   


        $row++;
    }  
 
   
    // Adjust column widths based on maximum length found
    foreach ($maxColumnWidths as $column => $maxWidth) {
        $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn($column)->setWidth($maxWidth * 1.2); // Multiply by 1.2 for a little extra space
    }

     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_weely_report.xlsx";

     $objWriter->save($out_File);

   $DownloadedFileName = 'rn_weely_report -'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;

?>
