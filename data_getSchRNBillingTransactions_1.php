<?php 
	
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


  require_once("db_GetSetData.php");

  $conn = getCon();

  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  
  $BillingIssueCategoryId = $_GET['BillingIssueCategoryId'];
  $SchoolId = $_GET['SchoolId'];
  $ServiceTypeId = $_GET['ServiceTypeId'];
  $RnId = $_GET['RnId'];
  $RNLiaisonId = $_GET['RNLiaisonId'];
  $PostingStatusId = $_GET['PostingStatusId'];
  $ConfirmationNumber = $_GET['ConfirmationNumber'];
  $StudentName = $_GET['StudentName'];
 
  if (!$ConfirmationNumber) {

    $ConfirmationNumber = '';

  }


   
  if (!$BillingIssueCategoryId) {

    $BillingIssueCategoryId = '0';

  }

 
  if (!$BillingIssueCategoryId) {

    $BillingIssueCategoryId = '0';

  }
 
  if (!$SchoolId) {

    $SchoolId = '0';

  }
 
  if (!$ServiceTypeId) {

    $ServiceTypeId = '0';

  }
 
  if (!$RnId) {

    $RnId = '0';

  }

 
  // if (!$PostingStatusId) {

  //   $PostingStatusId = '';

  // }
 
  $query = "call proc_getSchRNBillingTransactions ( '{$FromDate}', 
                                                    '{$ToDate}', 
                                                    '{$BillingIssueCategoryId}',
                                                    '{$SchoolId}',
                                                    '{$ServiceTypeId}',
                                                    '{$RnId}',
                                                    '{$RNLiaisonId}',
                                                    '{$PostingStatusId}',
                                                    '{$ConfirmationNumber}',
                                                    '{$StudentName}' 

                                                 ) "; 



  $ret = getData ($conn, $query);
  setDisConn($conn);

  // echo $ret;
 echo $query;
    


  
  
?>

