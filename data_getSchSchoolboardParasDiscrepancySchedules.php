<?php 
	

    require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
   $ParaDiscrTypeId = $_GET['ParaDiscrTypeId'];
   $ServiceTypeIdList = $_GET['ServiceTypeIdList'];
   $RegistrantId = $_GET['RegistrantId'];
   $StudentName = $_GET['StudentName'];

     

   $query = "call proc_getSchSchoolboardParasDiscrepancySchedules (  '{$FromDate}', 
                                                                  '{$ToDate}', 
                                                                  '{$ParaDiscrTypeId}',
                                                                  '{$ServiceTypeIdList}',
                                                                  '{$RegistrantId}',
                                                                  '{$StudentName}'  

                                                             ) "; 

  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  // echo $query;
 
?>
