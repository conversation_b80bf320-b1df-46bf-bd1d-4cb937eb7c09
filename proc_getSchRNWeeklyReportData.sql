/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchRNWeeklyReportData$$

	CREATE PROCEDURE proc_getSchRNWeeklyReportData (IN   p_incl_schedules varchar(50000) )
														 	 

	BEGIN

	DECLARE v_LongTermNurseId INT ; 

	 

	 		create temporary table tmp engine=memory
			
				 SELECT 
			a.ServiceTypeId,
			COALESCE(CAST(d.CallInComments AS CHAR (16)), '') AS CoverageType,
			CAST('' AS CHAR (10)) AS LongTermRnId,
			CAST('' AS CHAR (512)) AS LongTermRnName,
			DATE_FORMAT(a.ServiceDate, '%m-%d-%Y') AS ServiceDate,
			DAYOFWEEK(a.ServiceDate) AS DOW,
			a.Id AS ScheduleId,
			CONCAT(DATE_FORMAT(a.StartTime, '%l:%i %p'),
					'-',
					(DATE_FORMAT(a.EndTime, '%l:%i %p')),
					' (',
					a.TotalHours,
					' Hrs)') AS ScheduleDesc,
			a.RegistrantId AS ScheduledRNId,
			CONCAT(TRIM(b.LastName),
					', ',
					TRIM(b.FirstName)) AS ScheduledRNName,
			a.StudentId,
			COALESCE(CONCAT(TRIM(c.LastName),
							', ',
							TRIM(c.FirstName)),
					'All') AS StudentName,
			c.FirstName,
			c.LastName,
			COALESCE(c.ExtId, '') AS StudentOsisNumber,
			
			CASE
				WHEN CallInStatus IS NULL THEN 'Unconfirmed'
				ELSE CallInStatus
			END AS CallInStatus,
			d.Id AS CallInTimeId,
			COALESCE(DATE_FORMAT(d.CallInTime, '%l:%i %p'),
					'') AS CallInTime,
			COALESCE(CAST(d.CallInComments AS CHAR (512)), '') AS CallInComments,
			h.ServiceTypeDesc,
			a.ConfirmationNumber,
			a.AssignmentId
		FROM
			WeeklyServices a
				JOIN
			Registrants b ON a.RegistrantId = b.Id
				JOIN
			RegistrantTypes f ON b.TypeId = f.Id
				LEFT JOIN
			SchStudents c ON a.StudentId = c.Id
				JOIN
			SchServiceTypes h ON a.ServiceTypeId = h.Id
				JOIN
			SchSchools i ON a.SchoolId = i.Id
				JOIN
			SchDistricts j ON i.DistrictId = j.Id
				LEFT JOIN
			SchRNDailyCallInTimes d ON a.RegistrantId = d.RegistrantId
				AND a.ServiceDate = d.ServiceDate
		WHERE FIND_IN_SET(a.Id, p_incl_schedules)
	  ;

 
	/* Get long Term Nurse Id - Student*/
	/*=================================*/

	update tmp a
	   set a.LongTermRnId = (
	   
	   		Select b.RegistrantId
	   		  FROM SchStudentAssignmentDetails b
	   		  WHERE a.AssignmentId = b.AssignmentId
	   		  LIMIT 1	
	   		   
	   )
	WHERE a.AssignmentId != 0;
 

	/* Get long Term Nurse Name*/
	/*=================================*/

	update tmp a,
		   Registrants b
	 set a.LongTermRnName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) 	   
	   
	WHERE a.AssignmentId != 0
	AND   a.LongTermRnId = b.Id
	 ;  	 

	/* Set  long Term Nurse Name*/
	/*=================================*/

	update tmp a,
		   Registrants b
	 set a.LongTermRnName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) 	   
	   
	WHERE a.AssignmentId != 0
	AND   a.LongTermRnId = b.Id
	 ;  	 

	/*========================*/

	create temporary table tmp1 engine=memory
	select  

			ServiceDate,
			DOW,
			LastName,
			FirstName,
			StudentOsisNumber,
			LongTermRnName,
			ConfirmationNumber,
 			CallInTime,
			CallInStatus,
			ScheduledRNName,
			
			CallInComments,
			StudentId,

			count(StudentId) as CoverageTypeId 

 
	from tmp 
       GROUP BY ServiceDate, StudentId
	;	

	select  ServiceDate,
			DOW, 
			LastName,
			FirstName,
			StudentOsisNumber,
			LongTermRnName,
			ConfirmationNumber,

			CASE
				WHEN CoverageTypeId = 3 THEN 'both'
				ELSE '1:1'
			END AS 'CoverageType',
			CASE
				WHEN CallInTime != '' THEN 'Yes'
				ELSE 'No'
			END AS 'ServiceProvided',
			CASE
				WHEN CallInTime != '' THEN ''
				ELSE CallInStatus
			END AS 'ServiceNotProvided', 
			CASE
				WHEN LongTermRnName != ScheduledRNName THEN ScheduledRNName
				ELSE ''
			END AS 'SubRNName' 



	from  tmp1
	ORDER BY DOW,LastName,FirstName  

	;

	drop temporary table if exists tmp;
	drop temporary table if exists tmp1;



	END $$

	DELIMITER ;		

