<?php 

   require_once("db_GetSetData.php");

	$conn = getCon();

	$ServiceTypeId = $_GET['ServiceTypeId'];  
	$SchoolId = $_GET['SchoolId'];  
	$TransmitDate = $_GET['TransmitDate'];  


       $query = " SELECT   a.Id as MandateId,
                        a.Id,
                        a.StudentId, 
                        b.ExtId,
                        CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
                        
                        DATE_FORMAT( a.StartDate, '%m-%d-%Y' ) as ServiceStartDate, 

                        CONCAT( `SessionFrequency` , ' X ', `SessionLength` , ' X ', `SessionGrpSize` ) AS MandateDesc,
                        CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
                        a.UserId , 
                        DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as TransDate 
                         

                  FROM  SchStudentMandates a 
                            LEFT JOIN  
                        SchStudents b ON  a.StudentId = b.Id
                            LEFT JOIN
                        Users e ON a.UserId = e.UserId
                  WHERE a.StatusId = 1
                  AND a.RegistrantId = 0 
                  AND a.ServiceTypeId = '{$ServiceTypeId}'
                  AND a.SchoolId = '{$SchoolId}'
                  AND date(a.TransDate) = '{$TransmitDate}'


                  ORDER BY StudentName
						";		
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	  
	  

?>
    