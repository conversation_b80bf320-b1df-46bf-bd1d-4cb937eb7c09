	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchDistrictServiceDetails$$

	CREATE PROCEDURE proc_getSchDistrictServiceDetails (IN 	p_district_id INT, 
															p_billing_contract_id INT
														)  

	BEGIN

		create temporary table tmp

		SELECT * FROM SchDistrictServiceDetails
			WHERE DistrictId = p_district_id
		    AND BillingContractId = p_billing_contract_id;		
 


			SELECT a.Id,
			b.Id as ServiceTypeId, 
			b.ServiceTypeDesc,   
			
			CASE b.ServiceCategoryId
	        	WHEN '0' THEN 'School'  
	        	ELSE 'Student'
	        END AS 'ServiceCategoryDesc', 
	        
	        CASE b.RegistrantTypeId
	        	WHEN '12' THEN 'RN'
	        	WHEN '23' THEN 'Para'
	        	WHEN '7'  THEN 'PT'
	        	WHEN '17'  THEN 'OT'
	        	WHEN '16'  THEN 'ST'
	        	WHEN '20'  THEN 'ST'
	        	WHEN '36'  THEN 'CS'
	        	WHEN '37'  THEN 'S.E.'

			END AS 'RegistrantTypeDesc',
				
			COALESCE(BillingContractId,'1') as BillingContractId,
			COALESCE(PrimaryVendorFL,'0') as PrimaryVendorFL,
			COALESCE(PayRate, '0.00') as PayRate,
			COALESCE(BillRate, '0.00') as BillRate,
			COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName )),'') as UserName,
			COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ),'') as TransDate 

		FROM tmp a   
	        
			RIGHT JOIN SchServiceTypes b
				ON a.ServiceTypeId = b.Id
				and a.DistrictId = p_district_id 
			LEFT JOIN Users c 
				ON a.Userid = c.Userid	
		ORDER BY ServiceCategoryId, RegistrantTypeId ;

		drop temporary table if exists tmp;
		 
		
	END $$

	DELIMITER ;		
	 