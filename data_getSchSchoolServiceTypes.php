<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();


	$ServiceCategoryId = $_GET['ServiceCategoryId'];
 
	if ($ServiceCategoryId == 'S') {

		    $query = "	SELECT 	a.Id as id,
						        a.Id as ServiceTypeId,
						        a.RegistrantTypeId,
						       CONCAT(ServiceTypeDesc, ' (', RegistrantTypeDesc, ') ') as ServiceTypeDesc
						FROM SchServiceTypes a, RegistrantTypes b
						WHERE a.Id  in (43,46,47)  
						AND   b.Id  = a.RegistrantTypeId
						ORDER BY a.RegistrantTypeId
               ";

	} else if ($ServiceCategoryId == 'R') {

	    $query = "	SELECT 	a.Id as id,
						        a.Id as ServiceTypeId,
						        a.RegistrantTypeId,
						       CONCAT(ServiceTypeDesc, ' (', RegistrantTypeDesc, ') ') as ServiceTypeDesc
						FROM SchServiceTypes a, RegistrantTypes b
						WHERE a.Id  in (43,44,45)  
						AND   b.Id  = a.RegistrantTypeId
						ORDER BY a.RegistrantTypeId
               ";

	} else {

	    $query = "	SELECT 	a.Id as id,
					        a.Id as ServiceTypeId,
					        a.RegistrantTypeId,
					       CONCAT(ServiceTypeDesc, ' (', RegistrantTypeDesc, ') ') as ServiceTypeDesc
					FROM SchServiceTypes a, RegistrantTypes b
					WHERE ServiceCategoryId =  '{$ServiceCategoryId}' 
					AND   b.Id  = a.RegistrantTypeId
					ORDER BY a.RegistrantTypeId
               ";

	}


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	// echo $query;


?>
