<?php 



     require_once("db_GetSetData.php");

	$conn = getCon();

	$SelectType = $_GET['SelectType'];
	if ($SelectType == 'ActiveOnly') {

		$Statuses = '(1)';

	} else {

		$Statuses = '(1,2)';
	}

      $query = "SELECT  Id as id,  
						CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', ExtId,')' ) as StudentName,
 						SubSchoolTypeId,				
						SearchId
				FROM SchStudents
				WHERE StatusID in {$Statuses}
				order by LastName, FirstName ";	 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;


/*
	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$SelectType = $_GET['SelectType'];

	

	if ($SelectType == 'ActiveOnly') {

		$Statuses = '(1)';

	} else {

		$Statuses = '(1,2)';
	}
	
	
	
	$result = $rcr_transaction->getSchStudents($Statuses);
	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/	  

?>
