<?php 

 


    require_once("db_GetSetData.php");  

    $conn = getCon();

    
    $ScheduleIdList = $_POST['ScheduleIdList'];
    $BillingCommentsId = $_POST['BillingCommentsId'];  
    $BillingIssueCategoryId = $_POST['BillingIssueCategoryId'];  
    $BillingComments = $_POST['BillingComments'];  
    $IssueAmount = $_POST['IssueAmount'];  
    $WriteOffAmount = $_POST['WriteOffAmount'];  
    $AdditionalComments = $_POST['AdditionalComments'];  
    $UnBilledHours = $_POST['UnBilledHours'];  
    
    $UserId = $_POST['UserId'];  


      $BillingComments = mysqli_real_escape_string($conn, $BillingComments); 


      if($BillingCommentsId != ''  ) {  


      $query ="UPDATE SchRNBilling
                 SET
                  BillingIssueCategoryId = '{$BillingIssueCategoryId}',
                  BillingComments = '{$BillingComments}',
                  IssueAmount = '{$IssueAmount}',
                  WriteOffAmount = '{$WriteOffAmount}',
                  AdditionalComments = '{$AdditionalComments}',
                  UnBilledHours = '{$UnBilledHours}',
                  UserId = '{$UserId}' 
              WHERE BillingCommentsId = '{$BillingCommentsId}';



          ";     

   
                        
      } else {

       $query ="INSERT INTO  SchRNBilling
            ( 
              BillingCommentsId,
              BillingIssueCategoryId,
              BillingComments,
              IssueAmount,
              WriteOffAmount,
              AdditionalComments,
              UnBilledHours,
              UserId 
            )
            VALUES
            
            ( '{$ScheduleIdList}',
              '{$BillingIssueCategoryId}',
              '{$BillingComments}',
              '{$IssueAmount}',
              '{$WriteOffAmount}',
              '{$AdditionalComments}',
              '{$UnBilledHours}',
              '{$UserId}'
            );
   

        ; 


          ";      

      }




    $ret = setData ($conn, $query);
    setDisConn($conn);

     


    // echo $ret;
    echo $query;
  
 

?>
