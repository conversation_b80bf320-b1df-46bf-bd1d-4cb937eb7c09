<?php 


	require_once("db_GetSetData.php");



	$CurrPassword = $_POST['CurrPassword'];
	$NewPassword = $_POST['NewPassword'];
	$UserId = $_POST['UserId'];

 
    // Verify the 'NewPassword' was not used before
    //===============================================
	$conn1 = getCon();

    $query1 = "SELECT 
			    *
			FROM
			    UsersHistory
			WHERE
			    UserId = '{$UserId}'
			    AND Password = '{$NewPassword}'
	";

	$result1 =  mysqli_query($conn1, $query1);
	

	$err_fl = '0';
	if (mysqli_num_rows($result1) == "1") {

		$err_fl = '1';
	}	
	
	setDisConn($conn1);

	if ($err_fl == '1') {

		http_response_code(401); // Unauthorized status code
	    echo "Password was previosly used!";
	    return;


	}

	//===============================================



	// Set New Password
	//================
	$conn = getCon();


     $UserId = $_POST['UserId'];  
     if (!$UserId) {

	     $UserId = '0';

     }
     

 
	     $query =" UPDATE Users 
					Set Password = '{$NewPassword}',
					    NextResetDate = DATE_ADD(CURDATE(), INTERVAL 90 day), 
						ResetFL = 0
					WHERE 	UserId = '{$UserId}' 
							AND Password = '{$CurrPassword}' 

	        ";      
							        

	$ret = setData ($conn, $query);
	setDisConn($conn);

	// Save New Password in UsersHistory table
	//================
	$conn1 = getCon();

	     $query1 =" INSERT INTO  `UsersHistory`
						( 
						UserId,
						Password)
						VALUES
						( 
						'{$UserId}',
						'{$NewPassword}' 
						) 

	        ";      
							        

	$ret1 = setData ($conn1, $query1);
	setDisConn($conn1);

	//=================


	echo $ret;
 	


 
?>
