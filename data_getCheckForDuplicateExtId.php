<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$ExtId = $_GET['ExtId'];
	$ExtIdType = $_GET['ExtIdType'];
	$InternalId = $_GET['InternalId'];



	if ($ExtIdType == 'Registrant') {


		$result = $rcr_transaction->getCheckDuplicateExtIdRegistrant(	$ExtId,
																		$InternalId ); 

		
		//echo $result;
		$rcr_transaction->disconnectDB (); 
		echo  "{ success: true,  data: ".json_encode($result)."}";

	}	


	$rcr_transaction->disconnectDB (); 

	/*

	$dup_fl = 'no'; 

	echo  "{ success: true, DuplicateFL: '{$dup_fl}'}";
	*/


?>
