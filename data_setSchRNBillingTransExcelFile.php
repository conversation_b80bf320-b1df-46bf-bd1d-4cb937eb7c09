<?php


    // error_reporting(E_ALL);
    // ini_set('display_errors', TRUE);
    // ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
   
   $FromDate = $_GET['FromDate'];
   $ToDate = $_GET['ToDate'];
   $FilterTypeId = $_GET['FilterTypeId'];
   $FilterValueInt = $_GET['FilterValueInt'];
   $FilterValueChar = $_GET['FilterValueChar'];

   // $FromDate = '2023-10-1';
   // $ToDate = '2023-11-8' ;
   // $FilterTypeId = '4';
   // $FilterValueInt = '8681';
   // $FilterValueChar = '';

    $charset = 'utf8mb4';


    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNBillingTransactions(?,?,?,?,?)");
    $stmt->execute([$FromDate, $ToDate,$FilterTypeId,$FilterValueInt,$FilterValueChar]);

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


 
    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A1:T1")->getFont()->setBold( true );

  
    $objPHPExcel->getActiveSheet()->setCellValue('A1', 'Date');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', 'District');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', 'School');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', 'School ID');
    $objPHPExcel->getActiveSheet()->setCellValue('E1', 'Time of Arrival/ Call in time');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', "Today's Nurse");
    $objPHPExcel->getActiveSheet()->setCellValue('G1', 'Long Term Nurse');
    $objPHPExcel->getActiveSheet()->setCellValue('H1', 'Student');
    $objPHPExcel->getActiveSheet()->setCellValue('I1', 'OSIS');
    $objPHPExcel->getActiveSheet()->setCellValue('J1', 'Trip Location & Time');
    $objPHPExcel->getActiveSheet()->setCellValue('K1', 'Confirmation #');
    $objPHPExcel->getActiveSheet()->setCellValue('L1', 'Placement');
    $objPHPExcel->getActiveSheet()->setCellValue('M1', 'Term');
    $objPHPExcel->getActiveSheet()->setCellValue('N1', 'Comments');
    $objPHPExcel->getActiveSheet()->setCellValue('O1', 'Liaison');
    $objPHPExcel->getActiveSheet()->setCellValue('P1', 'Billing Issue Category');
    $objPHPExcel->getActiveSheet()->setCellValue('Q1', 'Billing Comment');
    $objPHPExcel->getActiveSheet()->setCellValue('R1', 'Issue Amount ');
    $objPHPExcel->getActiveSheet()->setCellValue('S1', 'Write-Off Amount');
    $objPHPExcel->getActiveSheet()->setCellValue('T1', 'Additional Comments ');



    // Set results
    $row = 2;
    foreach ($results as $result) {

       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $result['ServiceDateFrm']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['DistrictName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['SchoolName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['SesisSchoolId']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['CallInTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['ScheduledRNName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['LongTermRnName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['StudentName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['StudentOsisNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['ScheduleDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['ConfirmationNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(11, $row, $result['Placement']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['AssignmentType']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(13, $row, $result['CallInComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(14, $row, $result['LiaisonName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(15, $row, $result['BillingIssueCategoryDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(16, $row, $result['BillingComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(17, $row, money_format($result['IssueAmount'])); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(18, $row, money_format($result['WriteOffAmount'])); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(19, $row, $result['AdditionalComments']); 



        $row++;
    }  
 
     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_billing_report.xlsx";

     $objWriter->save($out_File);

   $DownloadedFileName = 'rn_billing_report -'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;

?>
