
<?php 
	

 require_once("db_GetSetData.php");

  $conn = getCon();

	$StudentId = $_GET['StudentId'];
 
    $query = "	SELECT 	a.Id as id,
												a.Id as MandateId,
												StudentId, 
												CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
												a.StatusId,
												CASE a.SchoolId 
													WHEN '0' THEN ''
												ELSE a.SchoolId
												END AS SchoolId,
                                                a.SchoolId as OrigSchoolId,

												(SELECT m.SchoolName from SchSchools c,
												                        SchSubSchools m
												   WHERE a.SchoolId = c.Id 
												   AND   a.SchoolId = m.SchoolId
												   AND   b.SubSchoolTypeId = m.SubSchoolTypeId

												) as SchoolName,  
												
												CASE ParaTransportPerc 
													WHEN '' THEN ServiceTypeDesc
												ELSE CONCAT(ServiceTypeDesc,' (',ParaTransportPerc,'%)' )
												END AS ServiceTypeDesc,												
												ServiceTypeId,
												a.StatusId , 
												SECMandateStatus,
												DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
												DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,

												CASE a.RegistrantId 
													WHEN '0' THEN ''
												ELSE a.RegistrantId
												END AS RegistrantId,
												a.RegistrantId as OrigRegistrantId,
												
												a.BillingContractId ,
												BillingContractDesc, 

												CallInDate,
												DOEAssignmentDate,
												PlaceOfService,
												SubSchoolTypeId,
												SECMandateStatus,
												DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
												CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize ) AS MandateDesc,
												Language,
												CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName,
												CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
												DOESchoolName,												
												SESISParaServiceTypeId,
												ParaTransportPerc, 


												DATE_FORMAT( ParaStartTimeMon1, '%l:%i %p' ) as ParaStartTimeMon1,
												DATE_FORMAT( ParaEndTimeMon1, '%l:%i %p' ) as ParaEndTimeMon1,
												ParaTotalHoursMon1,
												
											 	
												case ParaStartTimeMon2
												     WHEN '00:00:00' THEN ParaStartTimeMon2
												     ELSE DATE_FORMAT( ParaStartTimeMon2, '%l:%i %p' )
												end as ParaStartTimeMon2,
											 
												case ParaEndTimeMon2
												     WHEN '00:00:00' THEN ParaEndTimeMon2
												     ELSE DATE_FORMAT( ParaEndTimeMon2, '%l:%i %p' )
												end as ParaEndTimeMon2,
												

												ParaTotalHoursMon2,

									            DATE_FORMAT( ParaStartTimeTue1, '%l:%i %p' ) as ParaStartTimeTue1,
									            DATE_FORMAT( ParaEndTimeTue1, '%l:%i %p' ) as ParaEndTimeTue1,
									            ParaTotalHoursTue1,

 												case ParaStartTimeTue2
												     WHEN '00:00:00' THEN ParaStartTimeTue2
												     ELSE DATE_FORMAT( ParaStartTimeTue2, '%l:%i %p' )
												end as ParaStartTimeTue2,
											 
												case ParaEndTimeTue2
												     WHEN '00:00:00' THEN ParaEndTimeTue2
												     ELSE DATE_FORMAT( ParaEndTimeTue2, '%l:%i %p' )
												end as ParaEndTimeTue2,

									            ParaTotalHoursTue2,

									            DATE_FORMAT( ParaStartTimeWed1, '%l:%i %p' ) as ParaStartTimeWed1,
									            DATE_FORMAT( ParaEndTimeWed1, '%l:%i %p' ) as ParaEndTimeWed1,
									            ParaTotalHoursWed1,
									            
 												case ParaStartTimeWed2
												     WHEN '00:00:00' THEN ParaStartTimeWed2
												     ELSE DATE_FORMAT( ParaStartTimeWed2, '%l:%i %p' )
												end as ParaStartTimeWed2,
											 
												case ParaEndTimeWed2
												     WHEN '00:00:00' THEN ParaEndTimeWed2
												     ELSE DATE_FORMAT( ParaEndTimeWed2, '%l:%i %p' )
												end as ParaEndTimeWed2,
									            
									            ParaTotalHoursWed2,

								                DATE_FORMAT( ParaStartTimeThu1, '%l:%i %p' ) as ParaStartTimeThu1,
								                DATE_FORMAT( ParaEndTimeThu1, '%l:%i %p' ) as ParaEndTimeThu1,
						   		                ParaTotalHoursThu1,
							                    

 												case ParaStartTimeThu2
												     WHEN '00:00:00' THEN ParaStartTimeThu2
												     ELSE DATE_FORMAT( ParaStartTimeThu2, '%l:%i %p' )
												end as ParaStartTimeThu2,
											 
												case ParaEndTimeThu2
												     WHEN '00:00:00' THEN ParaEndTimeThu2
												     ELSE DATE_FORMAT( ParaEndTimeThu2, '%l:%i %p' )
												end as ParaEndTimeThu2,

							                    ParaTotalHoursThu2,

							                    DATE_FORMAT( ParaStartTimeFri1, '%l:%i %p' ) as ParaStartTimeFri1,
							                    DATE_FORMAT( ParaEndTimeFri1, '%l:%i %p' ) as ParaEndTimeFri1,
							                    ParaTotalHoursFri1,
							                    

 												case ParaStartTimeFri2
												     WHEN '00:00:00' THEN ParaStartTimeFri2
												     ELSE DATE_FORMAT( ParaStartTimeFri2, '%l:%i %p' )
												end as ParaStartTimeFri2,
											 
												case ParaEndTimeFri2
												     WHEN '00:00:00' THEN ParaEndTimeFri2
												     ELSE DATE_FORMAT( ParaEndTimeFri2, '%l:%i %p' )
												end as ParaEndTimeFri2,
							                    
							                    ParaTotalHoursFri2,
							                    ParaExtdHoursAuthFL,

												AssignmentGeneratedFL,
												a.UserId , 
												a.TransDate
			
										FROM 	SchStudentMandates a, 	
												SchStudents b,
												SchBillingContracts c, 
												SchServiceTypes f,
											    Users e
											WHERE a.StudentId = '{$StudentId}'
											AND a.StudentId = b.Id	
											AND a.ServiceTypeId = f.Id
											AND a.BillingContractId = c.Id
											AND a.UserId = e.UserId
											AND YEAR(StartDate) > 2017
										ORDER BY ServiceTypeId,  a.StatusId,  a.EndDate DESC     ";

	$ret = getData ($conn, $query);
	setDisConn($conn);	

	echo $ret;

/*

	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$StudentId = $_GET['StudentId'];

	$result = $rcr_transaction->getSchStudentMandatesList($StudentId);


	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/
  
?>
