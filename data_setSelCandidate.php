<?php 

 


	require_once("db_GetSetData.php"); 	

	$conn = getCon();

	
	$Id = $_POST['Id'];  
	$SearchId = $_POST['SearchId'];  
	$StatusId = $_POST['StatusId'];  
	$TypeId = $_POST['TypeId'];  
	$LastName = $_POST['LastName'];  
	$FirstName = $_POST['FirstName'];  
	$StreetAddress1 = $_POST['StreetAddress1'];  
	$StreetAddress2 = $_POST['StreetAddress2'];  
	
	$City = $_POST['City'];  
	$Zipcode = $_POST['Zipcode'];  
	$MobilePhone = $_POST['MobilePhone'];  
	$HomePhone = $_POST['HomePhone'];  
	$State = $_POST['State'];  
	$ZipCode = $_POST['ZipCode'];  
	$RecruitorId = $_POST['RecruitorId'];  

	$Email = $_POST['Email'];  
	$UserId = $_POST['UserId'];  

  	if(is_numeric($Id) ) { 	


    $query ="UPDATE Candidates
			SET
			 
			StatusId = '{$StatusId}' ,
			TypeId = '{$TypeId}' ,
			FirstName = '{$FirstName}' ,
			LastName = '{$LastName}' ,
			MiddleInitial = '{$MiddleInitial}' ,
			StreetAddress1 = '{$StreetAddress1}' ,
			StreetAddress2 = '{$StreetAddress2}' ,
			City = '{$City}' ,
			State = '{$State}' ,
			ZipCode = '{$ZipCode}' ,
			MobilePhone = '{$MobilePhone}' ,
			HomePhone = '{$HomePhone}' ,
			Email = '{$Email}' ,
			RecruitorId = '{$RecruitorId}' ,
			UserId = '{$UserId}' ,
			TransDate = now() 
			WHERE Id = '{$Id}'; 


        ";     

 
							        
    } else {

     $query ="INSERT INTO  Candidates
					( 
						StatusId,
						SearchId,
						TypeId,
						FirstName,
						LastName,
						StreetAddress1,
						StreetAddress2,
						City,
						State,
						ZipCode,
						MobilePhone,
						HomePhone,
						Email,
						RecruitorId,
						UserId,
						TransDate
					)
					VALUES
					
					( 
						 '1' ,
						'{$SearchId}',
						'{$TypeId}',
						'{$FirstName}',
						'{$LastName}',
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}',
						'{$State}',
						'{$ZipCode}',
						'{$MobilePhone}',
						'{$HomePhone}',
						'{$RecruitorId}',
						'{$Email}',
						'{$UserId}',
						 now()
					);
 

			; 


        ";      

    }




	$ret = setData ($conn, $query);
	setDisConn($conn);

	// // Create SendHub Contact for "new" Candidate
	// //==========================


	//  	if(!is_numeric($Id) ) { 


 //   		    $url_qb = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchUploadCandidatesToSendHub.php";
   

 //    		$curl = curl_init();
       

         
	//         curl_setopt($curl, CURLOPT_URL, $url_qb);
	//         curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);  

 //        	$str = curl_exec($curl);

        
	//         curl_close($curl);
     

	//  	}	
 





	// echo $ret;
	echo $query;
	
 

?>
