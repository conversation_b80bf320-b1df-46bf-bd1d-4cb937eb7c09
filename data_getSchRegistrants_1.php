<?php 


	  require_once("db_GetSetData.php");

	  $conn = getCon();
		
	$SelectType = $_GET['SelectType'];
	$SearchId = $_GET['SearchId'];

	$SearchId = trim($SearchId);	

	if ($SelectType == 'ActiveOnly') {

		$Statuses = '(1)';

	} else {

		$Statuses = '(1,2,3)';
	}
	
	if(!$SearchId) {

		$SearchId = '%%';
	}

	$query = "SELECT a.Id as id,  
											a.TypeId,
											a.ExtId,
											a.SearchId,			
											CONCAT( trim( a.LastName) , ', ', trim(a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
										FROM Registrants a, RegistrantTypes b
										WHERE Typeid  = b.Id 
										AND StatusID in {$Statuses}
										AND SearchId like '{$SearchId}'
										
										ORDER BY LastName, FirstName ";

  	$ret = getData ($conn, $query);
  	setDisConn($conn);

    echo $ret;  
  	// echo $query;

	// require "ewDataHandler.php";  
	  
	// $rcr_transaction = new dataHandler(); 

	// $SelectType = $_GET['SelectType'];
	// $SearchId = $_GET['SearchId'];

	

	// if ($SelectType == 'ActiveOnly') {

	// 	$Statuses = '(1)';

	// } else {

	// 	$Statuses = '(1,2,3)';
	// }
	
	// if(!$SearchId) {

	// 	$SearchId = '%%';
	// }
	
	
	// $result = $rcr_transaction->getSchRegistrants($Statuses, $SearchId);
	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";
	  

?>
	