<?php 

 	ini_set("memory_limit","-1");

/* 
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/  

	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');

	include('../../phpexcel-1-8/Classes/PHPExcel.php');
 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


 
	// $FileExt = $_POST['FileExt'];
	// $UploadFileName = $_POST['UploadFileName'];
	// $UserId = $_POST['UserId'];
 
 
 
	$FileExt = 'xlsx';
	$UploadFileName = 'RCM_Employees.xlsx';
	$UserId = '1' ;
 


    if ($FileExt == 'xlsx') {

		$inputFileType = 'Excel2007';
	} else {

		$inputFileType = 'Excel5';

	}	

 

	$inputFileName =  '../uploads/'.$UploadFileName;


	echo '$inputFileName: '.$inputFileName.'</br>';

/*    
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);

		
	} else {



		//print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: File Name - ".$inputFileName."}";
	    Return ; 

	}
     
*/	
	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	$objReader->setReadDataOnly(true);
	$objPHPExcel = $objReader->load($inputFileName);
	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);

	$cnt = 0;	
	$linecount = 0;	
 

	foreach ($sheetData as &$row) { // foreach - start

		$cnt++;

		//External ID
		//==========================
		$ext_id = str_replace('-','',$row["D"]);
		$ext_id = trim($ext_id);




		if	(is_numeric($ext_id)) { 

		echo '$ext_id: '.$ext_id.'</br>'; 
			


			$ext_id = str_pad($ext_id, 9,'0',STR_PAD_LEFT);


			//First Name
			//==========================
			$registrant_first_name = $row["B"] ;
			$registrant_first_name = $connection->escapeSimple($registrant_first_name); 


			//First Last Name
			//==========================
			$registrant_last_name = $row["C"] ;
			$registrant_last_name = $connection->escapeSimple($registrant_last_name); 

			//External ID
			//==========================
			//echo   '#: '.$cnt.' ExtID: '.$ext_id.' Length: '.strlen($ext_id);

			//Registrant Type
			//==========================
			$registrant_type = $row["E"];


				$query_reg_type = "SELECT Id as RegistrantTypeId 
									FROM   RegistrantTypes
									where RegistrantTypeDesc = '{$registrant_type}'";
				
				$result_reg_type = $connection->query ($query_reg_type);
				if (DB::isError($result_reg_type)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_reg_type));
			    }

				while ($row_reg_type =& $result_reg_type->fetchRow (DB_FETCHMODE_ASSOC)) {
					$registrant_type_id = $row_reg_type['RegistrantTypeId'];
				}	



			//echo ' Reg Type: '.$registrant_type. ' Type ID:'. $registrant_type_id;	


			//HR Type
			//==========================
			$hr_type_desc = $row["F"];

		 	
			if ($hr_type_desc == 'Subcontrator') {

				$hr_type_id = '2';				

			} else {


				$hr_type_id = '1';				

			}
		

			//echo ' HR Type: '.$hr_type_desc.' HR Type ID: '.$hr_type_id;	

		 	
  
			//HR ID
			//==========================
			$hr_id = $row["A"];
			//echo ' HR ID: '.$hr_id.'</br>';	


			//================================ 
			//  Check if Registrant Exists  
			//================================ 
 			
			$query5 = "SELECT 1 
							FROM Registrants 
						WHERE ExtId = trim('{$ext_id}') ";
						
				
						
			$result5 = $connection->query ($query5);

			if (DB::isError($result5)){
				die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
			}			

				
			//=======================
			// Add New Registrants
			//=======================
	 		
	  		
			if ($result5->numRows() == 0) {  // Start 1 - New Employee 
	 			
				$linecount++;	



				$query1 = "INSERT INTO Registrants
				(
				  ExtId,
				  SearchId,
				  StatusId,
				  TypeId,
				  FirstName,
				  LastName,
				  HrId,
				  HrTypeId,
				  EntryDate,
				  UserId,
				  TransDate
				 )

			VALUES ( 		

			 		'{$ext_id}',
			        RAND(),
			        '1',
			        '{$registrant_type_id}',
					'{$registrant_first_name}',
					'{$registrant_last_name}',
					'{$hr_id}',
					'{$hr_type_id}',
					NOW(),
					'{$UserId}',
					NOW() 
			  ) ";		

					
				$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
		
				if (DB::isError($result1)){
							die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
				}
			
			//==============================================
			// Set Automatic Approval for New Registrants
			//==============================================

                       $query = " INSERT INTO ClientApprovedRegistrants ( 
										SearchId,
										Status, 
										ClientId, 
										ClientUnitId,
										RegistrantId, 
										UserId,		
										TransDate )
							


							SELECT RAND(),
									'1',
									a.Id,	
									(SELECT b.Id 
										FROM ClientUnits b
										WHERE b.ClientId = a.Id
										LIMIT 1
									),	

									f.Id,
									'{$UserId}',

									 NOW() 

									FROM Clients a, Registrants f
									WHERE SchoolFL = '1'  	
									AND f.ExtId = '{$ext_id}'								 
 
 								";
                                                      
				$result = $connection->getAll($query, DB_FETCHMODE_ASSOC);
		
				if (DB::isError($result)){
						die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
				}

		 

			} // End 1	

			else { // Start 2 - Existing Employee 

				$query2 = "UPDATE Registrants
							SET HrId = '{$hr_id}'
						   WHERE ExtId = '{$ext_id}'	
				" ;

				$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);
		
				if (DB::isError($result2)){
							die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
				}


			} // End 2 - Existing Employee 


		 

		}	


	} // for foreach - end	

 
 	 

	$connection->disconnect();

	//unlink($inputFileName);


	echo  "{ success: true, transactions: '{$linecount}'}";


?>