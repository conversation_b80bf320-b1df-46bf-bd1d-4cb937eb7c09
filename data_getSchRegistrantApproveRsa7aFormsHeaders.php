<?php 

 	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];  
	$FirstPeriodDate = $_GET['FirstPeriodDate'];  
	$SecondPeriodDate = $_GET['SecondPeriodDate'];  
	$ToDate = $_GET['ToDate'];  



	$query = "call proc_getSchRegistrantApproveRsa7aFormsHeaders (	'{$RegistrantId}', 
																	'{$FromDate}', 
																	'{$FirstPeriodDate}',
																	'{$SecondPeriodDate}',
																	'{$ToDate}' 
																) ";	


	$ret = getData ($conn, $query);
	
	setDisConn($conn);

	echo $ret;
	// echo $query;
	

?>
 