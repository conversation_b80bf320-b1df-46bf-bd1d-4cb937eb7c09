<?php 

 
   // error_reporting(E_ALL);
   //  ini_set('display_errors', TRUE);
   //  ini_set('display_startup_errors', TRUE);

  require_once("db_GetSetData.php");

  $conn = getCon();

  $RegistrantId = $_GET['RegistrantId'];  
  $RegistrantTypeId = $_GET['RegistrantTypeId'];  
  $PayrollWeek = $_GET['PayrollWeek'];
 

  if ($RegistrantTypeId == '23') {

               $query = "  SELECT 
                                DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                                a.ServiceDate AS ServiceDateSort,
                                a.WeekDay,
                                DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                                DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                                TotalHours,
                                CONCAT(b.FirstName, ' ', b.LastName) as StudentName,
                                a.Id as ScheduleId,
                                
                                COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                                  WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                                
                                '1'  as SessionGrpSize,

                                a.RegistrantId,
                                CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                                (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                              where a.Id = c.id LIMIT 1  
                                                      ) as ScheduleStatusId,
                                                      ScheduleStatusDesc,
                                /*SchoolName,*/  
                                -- CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolName,
                                CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ',d.ExtId ) as SchoolName,
                                -- CONCAT(TRIM(d.SchoolName),' (',DistrictName,') ') as SchoolNameDBN,
                                CONCAT(TRIM(d.SchoolName),' (',DistrictName,') ',d.ExtId ) as SchoolNameDBN,
                                
                                /*d.SchoolName as SchoolNameDBN,*/
                                ServiceTypeDesc,
                               '' as DockedHours,
                               '' as SetupHours,
                               '' as DockedHoursComments 


                            FROM  WeeklyServices a, 
                                  SchStudents b, 
                                  Registrants e,
                                  ScheduleStatuses g, 
                                  SchSchools d,
                                  SchServiceTypes h,
                                  SchDistricts f,
                                  SchSubSchools i 

                                  WHERE a.RegistrantId = '{$RegistrantId}' 
                                  AND   a.RegistrantId = e.Id 
                                  AND   a.PayrollWeek = '{$PayrollWeek}'  
                                 AND   a.ScheduleStatusId = 7   
                                   AND   b.Id = a.StudentId
                                  AND   a.ScheduleStatusId = g.Id
                                  AND   a.SchoolId = d.Id
                                  AND   d.DistrictId = f.Id 
                                  AND   a.ServiceTypeId = h.Id
                                  AND   a.SchoolId = i.SchoolId
                                  AND   b.SubSchoolTypeId = i.SubSchoolTypeId
                            ORDER BY    a.ServiceDate, a.StartTime, a.EndTime, a.TotalHours" ;           

              } elseif ($RegistrantTypeId == '12') { 
 
   $query = "      SELECT 
                      DATE_FORMAT(a.ServiceDate, '%m-%d-%Y') AS ServiceDate,
                      a.ServiceDate AS ServiceDateSort,
                      a.WeekDay,
                      DATE_FORMAT(a.StartTime, '%l:%i %p') AS StartTime,
                      DATE_FORMAT(a.EndTime, '%l:%i %p') EndTime,
                      TotalHours,
                      COALESCE(CONCAT(b.FirstName, ' ', b.LastName)  ,'') AS StudentName,
                        
                      a.Id  AS ScheduleId,
                       
                      COALESCE((SELECT 
                              SessionFrequency
                            FROM
                              SchStudentMandates i
                            WHERE
                              a.MandateId = i.Id),
                          '5') AS SessionFrequency,
                      CASE SessionGrpSize
                        WHEN '0' THEN '1'
                        ELSE SessionGrpSize
                      END AS SessionGrpSize,
                      a.RegistrantId,
                      CONCAT(TRIM(e.LastName),
                          ', ',
                          TRIM(e.FirstName)) AS RegistrantName,
                      (SELECT 
                          a.ScheduleStatusId
                        FROM
                          WeeklyServices c
                        WHERE
                          a.Id = c.id
                        LIMIT 1) AS ScheduleStatusId,
                      ScheduleStatusDesc,
                      CONCAT(TRIM(i.SchoolName),
                          ' (',
                          DistrictName,
                          ') ',
                          d.ExtId) AS SchoolName,
                      d.SchoolName AS SchoolNameDBN,
                      ServiceTypeDesc,
                      COALESCE(l.DockedHours,'') as DockedHours,
                      COALESCE(l.SetupHours,'') as SetupHours,
                      COALESCE(l.DockedHoursComments,'') as DockedHoursComments 

                    FROM
                      WeeklyServices a
                        LEFT JOIN
                      SchStudents b ON b.Id = a.StudentId
                        JOIN
                      Registrants e ON a.RegistrantId = e.Id
                        JOIN
                      ScheduleStatuses g ON a.ScheduleStatusId = g.Id
                        JOIN
                      SchSchools d ON a.SchoolId = d.Id
                        JOIN
                      SchServiceTypes h ON a.ServiceTypeId = h.Id
                        JOIN
                      SchDistricts f ON d.DistrictId = f.Id
                        LEFT JOIN
                      SchSubSchools i ON a.SchoolId = i.SchoolId
                        AND 0 = i.SubSchoolTypeId
                       LEFT JOIN
                      SchRNDockedHours l ON l.ConfirmationNumber = a.ConfirmationNumber
                                      and l.SchoolId = a.SchoolId
                                      and l.ServiceDate = a.ServiceDate 
                                      and l.ServiceTypeId = a.ServiceTypeId     



                    WHERE
                      a.RegistrantId = '{$RegistrantId}' 
                        AND a.PayrollWeek = '{$PayrollWeek}' 
                        AND a.ScheduleStatusId = 7 " ; 
             } else {

              $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                                    a.ServiceDate AS ServiceDateSort,
                                    a.WeekDay,
                                    DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                                DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                                /*FORMAT((a.TotalHours * 60), 0) as TotalHours,*/
                                TotalHours,
                                group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                                group_concat( a.Id    SEPARATOR ',' ) as ScheduleId,
                                
                                COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                                  WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                                CASE  SessionGrpSize 
                                  WHEN '0' THEN '1'
                                ELSE SessionGrpSize
                                END AS SessionGrpSize,

                                a.RegistrantId,
                                CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                                (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                              where a.Id = c.id LIMIT 1  
                                                      ) as ScheduleStatusId,
                                                      ScheduleStatusDesc,
                                /* SchoolName,*/
                                -- CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolName,
                                CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ',d.ExtId ) as SchoolName,
                                
                                d.SchoolName as SchoolNameDBN,
                                ServiceTypeDesc,
                               '' as DockedHours,
                               '' as SetupHours,
                               '' as  DockedHoursComments 


                            FROM  WeeklyServices a, 
                                  SchStudents b, 
                                  Registrants e,
                                  ScheduleStatuses g, 
                                  SchSchools d,
                                  SchServiceTypes h,
                                  SchDistricts f,
                                  SchSubSchools i   

                                  WHERE a.RegistrantId = '{$RegistrantId}' 
                                  AND   a.RegistrantId = e.Id 
                                  AND   a.PayrollWeek = '{$PayrollWeek}'  
                                 AND   a.ScheduleStatusId = 7   
                                   AND   b.Id = a.StudentId
                                   AND   d.DistrictId = f.Id 
                                  AND   a.ScheduleStatusId = g.Id
                                  AND   a.SchoolId = d.Id
                                  AND   a.ServiceTypeId = h.Id
                                  AND   a.SchoolId = i.SchoolId
                                  AND   b.SubSchoolTypeId = i.SubSchoolTypeId
                            GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours  ";


              }
   

  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  //echo $query;

  


/*
  require "ewDataHandler.php";  
    
  $rcr_transaction = new dataHandler(); 

  $RegistrantId = $_GET['RegistrantId'];  
  $RegistrantTypeId = $_GET['RegistrantTypeId'];  
  $PayrollWeek = $_GET['PayrollWeek'];

  $result = $rcr_transaction->getSchRegistrantTimeCardVerification( $RegistrantId, 
                                                                    $RegistrantTypeId, 
                                                                    $PayrollWeek);


  $rcr_transaction->disconnectDB (); 

  echo  "{ success: true,  data: ".json_encode($result)."}";
*/	
  
?>

