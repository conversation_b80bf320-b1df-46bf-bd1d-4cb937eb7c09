<?php 

	require_once("db_login.php");
  
 	

	$login = ($_POST["login"]); 
	$password = ($_POST["password"]); 

	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }

 

	$login = mysqli_real_escape_string($conn, $login); 
	$password = mysqli_real_escape_string($conn, $password); 

    $query = "SELECT 	a.UserId, 
						a.FirstName,
						a.LastName,
						a.UserGroupId,
						a.ExtId,
						CompanyName,
						TimeCardTabFL,
						BilingTabFL,
						PayrollTabFL,
						AdminTabFL,
						a.ResetFL,
						a.Email,
						a.ProviderMaintFL 
				FROM Users a, Company b, UserGroups c
					WHERE Login = '{$login}'
					AND Password = '{$password}' 
					AND a.UserGroupId = c.Id ";

	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));
	

	if (mysqli_num_rows($result) == "1") {

		$rows = array();
		while ($row = $result->fetch_assoc()) {
		    $rows[] = $row;
		}


        $UserName =  $rows[0]['FirstName']." ". $rows[0]['LastName']; 
        $arr = Array(	"UserId" => $rows[0]['UserId'], 
					 	"UserName" => $UserName, 
						"UserGroup" => $rows[0]['UserGroupId'],
						"ExtId" => $rows[0]['ExtId'],
						"CompanyName" => $rows[0]['CompanyName'],
						"TimeCardTabFL" => $rows[0]['TimeCardTabFL'],
						"BilingTabFL" => $rows[0]['BilingTabFL'],
						"PayrollTabFL" => $rows[0]['PayrollTabFL'],
						"AdminTabFL" => $rows[0]['AdminTabFL'],
						"ResetFL" => $rows[0]['ResetFL'],
						"Email" => $rows[0]['Email'],	
						"ProviderMaintFL" => $rows[0]['ProviderMaintFL']	
					);


        session_start();
        $_SESSION["UserId"] = $rows[0]['UserId'];        
        $_SESSION["UserName"] = $UserName;
        $_SESSION["UserGroup"] = $rows[0]['UserGroupId'];
        $_SESSION["ExtId"] = $rows[0]['ExtId'];
        $_SESSION["Email"] = $rows[0]['Email'];


	} else {

        $invalid = "invalid";
        $arr = Array("UserId" => $invalid, "UserName" => $invalid, "Email" => $invalid);  



	}	

  	
	if ($arr["UserId"] == "invalid") {
		echo  "{ success: false}";
	} else {
		echo  "{ success: true,  data: ".json_encode($arr)."}";
	}
 
 

    mysqli_free_result($result);
	mysqli_close($conn);


?>
