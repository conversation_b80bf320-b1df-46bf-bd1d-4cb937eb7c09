<?php 


 require_once("db_GetSetData.php");

  $conn = getCon();

  $RegistrantId = $_GET['RegistrantId'];

                       $query ="SELECT  Id as id, 
					                    Id,
										StatusId,
										SearchId,
										ExtId,
										TerminationType,
										TerminationReason,
										DATE_FORMAT( TerminationDate, '%m-%d-%Y' ) as TerminationDate,
										DATE_FORMAT( BirthDate, '%m-%d-%Y' ) as BirthDate,
										DATE_FORMAT( HireDate, '%m-%d-%Y' ) as HireDate,
										TypeId,
										TypeId as OrigTypeId,
										FirstName,
										LastName,
										CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										Fax,
										Email,
										CASE NextDayPay 
											WHEN '0' THEN 'No'
										ELSE 'Yes'
										END AS NextDayPayFlag,
										
										COALESCE(Gender, '') as Gender,
										COALESCE(Race, '') as Race,
										COALESCE(CheckType, '') as CheckType,
									 	COALESCE(W4Status, '') as W4Status,
										Exemptions, 
										

										Availability,
										HospitalExp,
										Shifts,
										DATE_FORMAT( LastPayDate, '%m-%d-%Y' ) as LastPayDate,
                                        CASE COALESCE(LastPayDate, 0)  
                                            WHEN '0' THEN ''
                                            ELSE DATE_FORMAT( LastPayDate, '%m-%d-%Y' )
                                        END as LastPayDate,
										PerDiem,
										ThrnWeekContract,
										NewGraduate,
										ForeignTrained,
										NextDayPay,
										VendorNumber,
										HrId,
										HrTypeId,
										EmplPictureFile,
										CASE BoroughId   
                                            WHEN '0' THEN ''
                                            ELSE BoroughId
                                        END as BoroughId,
										CASE RecruitorId   
                                            WHEN '0' THEN ''
                                            ELSE RecruitorId
                                        END as RecruitorId,
                                        Comments,
										RNLicenseNumber,
										UserId,
										TransDate,

												
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END as SpecialtiesList
                                            FROM RegistrantAttchedSpecialties a, Specialties b
                                            where b.Id = a.SpecialtyId
                                              and  a.RegistrantId = Registrants.Id) as SpecialtiesList,
										(Select RegistrantTypeDesc from RegistrantTypes
											where Typeid  = RegistrantTypes.Id) as  RegistrantTypeDesc,
										(Select  	RegistrantGroupId from RegistrantTypes
											where Typeid  = RegistrantTypes.Id) as  RegistrantGroupId,
										COALESCE((Select  	CONCAT(StoredName, '.pdf' ) from RegistrantDocuments a
											where a.RegistrantId = Registrants.Id
										    AND DocumentTypeId = '1'),'') as  RegistrantResumeName
												
									FROM Registrants 
								  where Id = '{$RegistrantId}' ";

  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;



// require "ewDataHandler.php"; 

// $rcr_transaction = new dataHandler(); 

// $RegistrantId = $_GET['RegistrantId'];

// $result = $rcr_transaction->getSelRegistrant($RegistrantId);


// $rcr_transaction->disconnectDB (); 

// //echo  '{ success: true,  data: '.json_encode($result).'}';
// $json_data = '{ success: true,  data: '.json_encode($result).'}';   
// $json_data = str_replace("[", "", $json_data);
// $json_data = str_replace("]", "", $json_data);

// echo $json_data;

  
?>
