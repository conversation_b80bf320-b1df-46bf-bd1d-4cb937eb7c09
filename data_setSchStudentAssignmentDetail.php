<?php 

 	require_once("db_GetSetData.php");
    $conn = getCon();

	$AssignmentId = $_POST['AssignmentId'];
	$AssignmentDetailId = $_POST['AssignmentDetailId'];
	$WeekDayId = $_POST['WeekDayId'];
	$StartTime = $_POST['StartTime'];	
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$RegistrantId = $_POST['RegistrantId'];
	$ApplyAllDays = $_POST['ApplyAllDays'];
	$UserId = $_POST['UserId'];


	if ($ApplyAllDays == '1') {

		$query ="DELETE FROM  SchStudentAssignmentDetails
					Where AssignmentId = '{$AssignmentId}'";
	    $ret =  setData ($conn, $query);        

		$query1 ="INSERT into SchStudentAssignmentDetails 
										(AssignmentId, 
										WeekDayId,
										StartTime,
										EndTime,
										TotalHours,
										RegistrantId,
										UserId,
										TransDate )
			SELECT  '{$AssignmentId}',
				    WeekDayId,
					'{$StartTime}',
					'{$EndTime}',
					'{$TotalHours}',
					'{$RegistrantId}',  
					'{$UserId}',
					NOW()  						       	
			FROM     DaysOfWeek	";	

	   $ret1 =  setData ($conn, $query1);        


 	} else {

		if(is_numeric($AssignmentDetailId) ) { 	
	
                $query2 ="UPDATE SchStudentAssignmentDetails
						SET AssignmentId =  '{$AssignmentId}', 
						StartTime =  '{$StartTime}',
						EndTime =  '{$EndTime}',
						TotalHours =  '{$TotalHours}',
						RegistrantId =  '{$RegistrantId}',

						UserId =  '{$UserId}',
						TransDate = NOW()
					WHERE Id = '{$AssignmentDetailId}' ";
        } else {
               $query2 ="INSERT into SchStudentAssignmentDetails 
						(AssignmentId, 
						WeekDayId,
						StartTime,
						EndTime,
						TotalHours,
						RegistrantId,
						UserId,
						TransDate )
		VALUES 	('{$AssignmentId}',  
				'{$WeekDayId}',
				'{$StartTime}',
				'{$EndTime}',
				'{$TotalHours}',
				'{$RegistrantId}',  
				'{$UserId}',
				NOW()  ) ";
						            

		}		

	   $ret2 =  setData ($conn, $query2);        


	}	


     
  	setDisConn($conn);
  	echo $query1;

	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $AssignmentId = $_POST['AssignmentId'];
	// $AssignmentDetailId = $_POST['AssignmentDetailId'];
	// $WeekDayId = $_POST['WeekDayId'];
	// $StartTime = $_POST['StartTime'];	
	// $EndTime = $_POST['EndTime'];
	// $TotalHours = $_POST['TotalHours'];
	// $RegistrantId = $_POST['RegistrantId'];
	// $ApplyAllDays = $_POST['ApplyAllDays'];
	// $UserId = $_POST['UserId'];


	// if ($ApplyAllDays == '1') {

	// 	$result = $rcr_transaction->setSchStudentAssignmentDetailAll($AssignmentId,	
	// 																$StartTime,
	// 																$EndTime,
	// 																$TotalHours,
	// 																$RegistrantId,
	// 																$UserId ); 


	// } else {

	// 	$result = $rcr_transaction->setSchStudentAssignmentDetail(   $AssignmentId,
	// 																$AssignmentDetailId,	
	// 																$WeekDayId,
	// 																$StartTime,
	// 																$EndTime,
	// 																$TotalHours,
	// 																$RegistrantId,
	// 																$UserId ); 

	// }



	// $rcr_transaction->disconnectDB (); 

	// //echo  '{ success: true };
	// echo $result;

?>
