<?php 


  // error_reporting(E_ALL);
  // ini_set('display_errors', TRUE);
  // ini_set('display_startup_errors', TRUE);


	
	require_once("db_GetSetData.php");

	$conn = getCon();

 
	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);

	  	foreach ($Data as $TransData) {

	    $BillingCommentsId = $TransData['BillingCommentsId'];
	  
 
		  $query ="DELETE FROM SchRNBilling
			                  
			              WHERE BillingCommentsId = '{$BillingCommentsId}';



	          ";     



	     $ret = setData ($conn, $query);
 
  }

	setDisConn($conn);
	echo $ret;

 

?>

