<?php

	require_once('DB.php');
	require('fpdf/fpdf.php');

	$db_host="localhost";
	$db_database="eWebStaffingDB_NS_Test";
	$db_username="root";
	$db_password="nediam75";

	$GLOBALS['Company'] = 'Gotham';
	
	/* Get Company Information
	==============================*/
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	
 
	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "SELECT 	CompanyName, 
						CONCAT( StreetAddress1, ' - ', StreetAddress2 ) AS StreetAddress, 
						CONCAT( City, ', ', State, '', ZipCode ) AS CityStateZip,
						PhoneNumber, 
						TIN
				FROM Company ";
				
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['Company'] = $row['CompanyName'];
		$GLOBALS['StreetAddress'] = $row['StreetAddress'];
		$GLOBALS['CityStateZip'] = $row['CityStateZip'];
		$GLOBALS['PhoneNumber'] = $row['PhoneNumber'];
		$GLOBALS['TIN'] = $row['TIN'];
		
	}	

	
	class PDF extends FPDF
	{
	//Page header
	function Header()
	{	
		$this->SetLeftMargin(5);
		//Logo
		$this->Image('logo.jpg',5,5,30);
		//Arial bold 15
		$this->SetFont('Arial','B',10);
		//Move to the right
		//Line break
		$this->Ln(4);
		//$this->Cell(80);
		//Title
		$this->Cell(30,5,'Please Remit To:',0,0,'L');
		$this->Cell(40);
		
		$this->Cell(30,5,$GLOBALS['Company'],0,0,'L');
	
		$this->Cell(30);
		$this->Cell(40,5,'Invoice',1,1,'C');	
		
		/*=====================================================*/
		//$this->Ln(5);
		$this->Cell(70);
		$this->Cell(30,5,$GLOBALS['StreetAddress'],0,0,'L');
		$this->Cell(30);
		$this->Cell(20,5,'Date',1,0,'C');	
		$this->Cell(20,5,'Number',1,1,'C');	
		
		/*=====================================================*/
		
		//$this->Ln(6);
		$this->Cell(70);
		$this->Cell(30,5,$GLOBALS['CityStateZip'],0,0,'L');
		$this->Cell(30);
		$this->Cell(20,5,'01/24/2014',1,0,'C');	
		$this->Cell(20,5,'50656651',1,1,'C');	
		
		/*=====================================================*/

		//$this->Ln(7);
		$this->Cell(70);
		$this->Cell(30,5,'Tel #: '.$GLOBALS['PhoneNumber'],0,0,'L');
		$this->Cell(30);
		$this->Cell(40,5,'Period',1,1,'C');	
		
		//$this->Ln(8);
		$this->Cell(70);
		$this->Cell(30,5,'TIN #: '.$GLOBALS['TIN'],0,0,'L');
		$this->Cell(30);
		$this->Cell(20,5,'01/06/2014',1,0,'C');	
		$this->Cell(20,5,'01/12/2014',1,1,'C');	

		//Line break
		$this->Ln(10);

		//Set Table Header
		$this->SetFont('Arial','B',10);
		$this->Cell(10,4,'LN #',1,0,'C');
		$this->Cell(20,4,'Date',1,0,'C');
		$this->Cell(15,4,'Shift',1,0,'C');
		$this->Cell(15,4,'WD/WE',1,0,'C');
		$this->Cell(20,4,'Area',1,0,'C');
		$this->Cell(40,4,'Service Provider',1,0,'C');
		$this->Cell(18,4,'License #',1,0,'C');
		$this->Cell(12,4,'Status',1,0,'C');
		$this->Cell(12,4,'Rate',1,0,'C');
		$this->Cell(15,4,'Units',1,0,'C');
		$this->Cell(15,4,'Amount',1,1,'C');

		
		//Line break
		//$this->Ln(20);
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Arial','I',8);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
	}




//Instanciation of inherited class
$pdf=new PDF();
$pdf->AliasNbPages();
$pdf->AddPage();
//$pdf->SetFont('Times','',12);
$pdf->SetFont('Arial','',10);

for($i=1;$i<=40;$i++)  
	
	//$pdf->Cell(0,10,'Printing line number '.$i,0,1);
	 
	{
	$pdf->Cell(10,4,$i,1,0,'C');
	$pdf->Cell(20,4,'12/22/2014',1,0,'C');
	$pdf->Cell(15,4,'Eve.',1,0,'C');
	$pdf->Cell(15,4,'WD',1,0,'C');
	$pdf->Cell(20,4,'MICU',1,0,'C');
	$pdf->Cell(40,4,'Joseph, Wislene',1,0,'C');
	$pdf->Cell(18,4,'1221123',1,0,'C');
	$pdf->Cell(12,4,'PCT',1,0,'C');
	$pdf->Cell(12,4,'12.50',1,0,'R');
	$pdf->Cell(15,4,'5.00',1,0,'R');
	$pdf->Cell(15,4,'62.00',1,1,'R');	
	} 
	
	$pdf->Output();
	
 	
?>
