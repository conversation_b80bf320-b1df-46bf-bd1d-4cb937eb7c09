<?php

  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


   require ("db_login.php");
     // require_once("db_GetSetData.php");



   $Filename = $_GET['Filename'];

    $charset = 'utf8mb4';

    // echo "Filename: $Filename<br>";


    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);


    // The path to the CSV file
    // $filePath = '/path/to/your/RCM_Healthcare_Docked_Hours_01_17_2024.csv';
    $filePath = '../rn_reports/'.$Filename;

    // Open the file for reading
    if (($handle = fopen($filePath, "r")) !== FALSE) {
        
        // Read the header row
        $header = fgetcsv($handle);
        
        // Check if the first column of the header contains the word "Conf #"
        if (stripos($header[0], "Conf #") === FALSE) {
            // The word "Vendor" was not found in the first column of the header
            echo "Error: The header in Column 'A' does not contain the word 'Conf #'.";
            fclose($handle); // Close the file
            exit; // Exit the script
        }        
        
        
        $x = 0;
 
        // Loop through each line in the file
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            // $data is an array of the column values for this row
           
            ++$x;

            $conf_number =  $data[0];
 
            $liaison_name =  $data[1];
 

            $service_date =  $data[2];
            $service_date_frm =  date('Y-m-d', strtotime($service_date));

            $school_dbn =  $data[3];
            $school_name =  $data[4];

            $start_date =  $data[6];
            $start_date =  date('Y-m-d', strtotime($start_date));
 
            $end_date =  $data[7];
            if ($end_date) {
                $end_date_frm =  date('Y-m-d', strtotime($end_date));

            } else {

                $end_date_frm = '';
            }

            $coverage_type =  $data[8];

            $coverage_reason =  $data[9];

 
            $total_hours =  $data[10];


            $nurse_name =  $data[11];
            
            $nurse_number =  $data[12];
  
            echo "conf_number: $conf_number liaison name: $liaison_name  school_id: $school_id nurse_name: $nurse_name";
            echo "  service_date: $service_date_frm total_hours: $total_hours<br>"; 
            echo "  start_date: $start_date end_date: $end_date end_date_frm: $end_date_frm<br>"; 

  
            // Insert into SchRNDockedHours table
            //===============================
            // $stmt = $pdo->prepare("INSERT INTO SchRNDailyPostingTransactions
            //              (
            //                   PostConfirmationNumber,
            //                   PostServiceDate,
            //                   PostLiaison,
            //                   PostDBNNumber,
            //                   PostSchoolName,
            //                   PostStartDateNeeded,
            //                   PostEndDateNeeded,
            //                   PostCoverageType,
            //                   PostCoverageReason,
            //                   PostHours,
            //                   PostAgencyNurse,
            //                   PostRNNumber       
            //             )
            //              VALUES
            //                 (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt = $pdo->prepare("
                INSERT INTO SchRNDailyPostingTransactions (
                    PostConfirmationNumber,
                    PostServiceDate,
                    PostLiaison,
                    PostDBNNumber,
                    PostSchoolName,
                    PostStartDateNeeded,
                    PostEndDateNeeded,
                    PostCoverageType,
                    PostCoverageReason,
                    PostHours,
                    PostAgencyNurse,
                    PostRNNumber
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    PostLiaison = VALUES(PostLiaison),
                    PostDBNNumber = VALUES(PostDBNNumber),
                    PostSchoolName = VALUES(PostSchoolName),
                    PostStartDateNeeded = VALUES(PostStartDateNeeded),
                    PostEndDateNeeded = VALUES(PostEndDateNeeded),
                    PostCoverageType = VALUES(PostCoverageType),
                    PostCoverageReason = VALUES(PostCoverageReason),
                    PostHours = VALUES(PostHours),
                    PostAgencyNurse = VALUES(PostAgencyNurse),
                    PostRNNumber = VALUES(PostRNNumber)
            ");
                    if (!$stmt) {
                        $error = $pdo->errorInfo();
                        echo "PDO Error: " . $error[2];
                        exit;
                    }

             $stmt->execute([$conf_number,
                            $service_date_frm,
                            $liaison_name,
                            $school_dbn,
                            $school_name,
                            $service_date_frm,
                            $end_date_frm,
                            $coverage_type,
                            $coverage_reason,
                            $total_hours,
                            $nurse_name,
                            $nurse_number
                          ]);
 
            
        }
        
        // Close the file
        fclose($handle);
    }

    unlink($filePath);

    echo "$x Transactions";    

?>
