  
<?php
    

    require_once("db_GetSetData.php");
     $conn = getCon();

 

    $Rsa7aFormSignatureStatus = $_POST['Rsa7aFormSignatureStatus'];
    $UserId = $_POST['UserId'];  

    $FormFromDate = $_POST['FormFromDate'];  
    $FormToDate = $_POST['FormToDate'];  

    $FormTypeId = $_POST['FormTypeId'];
    $BiMonthlyPeriodId = $_POST['BiMonthlyPeriodId'];


    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);
 


    if ($Rsa7aFormSignatureStatus == '4') {

      $ParentApprovalSentEmailDate = date("Y-m-d h:i:s");

    } else {

      $ParentApprovalSentEmailDate = ("0000-00-00 00:00:00");

    }

    foreach ($Data as $FormData) {

          $Rsa7aFormId = $FormData['Rsa7aFormId'];
          $MandateId = $FormData['MandateId'];
 
      
         if ($Rsa7aFormId) {

               if ($Rsa7aFormSignatureStatus == '0') {    

              $query = "DELETE FROM SchRsa7aFormSignatures       
                          WHERE  Id =     '{$Rsa7aFormId}'
                   ";


              } else {

              $query = "UPDATE SchRsa7aFormSignatures       
                   SET StatusId =  '{$Rsa7aFormSignatureStatus}',
                       ParentApprovalSentEmailDate = '{$ParentApprovalSentEmailDate}',
                       UserId = '{$UserId}',
                       TransDate = NOW()
                 WHERE  Id =     '{$Rsa7aFormId}'
                   ";

              }  

         } else {

            $query ="INSERT INTO  SchRsa7aFormSignatures
                (
                  StatusId,
                  MandateId,
                  FromDate,
                  ToDate,
                  FormTypeId,
                  BiMonthlyPeriodId,
                  ProviderSignatureTimeStamp,
                  UserId,
                  Transdate
                )
         
                VALUES
                (
                  '4',
                  '{$MandateId}',
                  '{$FormFromDate}',
                  '{$FormToDate}',
                  '{$FormTypeId}',
                  '{$BiMonthlyPeriodId}',
                   NOW(),
                  '{$UserId}',
                   NOW()

                )";

         }

    

        $ret =  setData ($conn, $query);        
  
  }   


    
  setDisConn($conn);
  echo $ret;
  // echo $query;
 
    


?>
 
