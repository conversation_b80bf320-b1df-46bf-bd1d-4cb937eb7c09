<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$DistrictId = $_POST['DistrictId'];
	$ServiceId = $_POST['ServiceId'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$PrimaryVendorFL = $_POST['PrimaryVendorFL'];
	$PayRate = $_POST['PayRate'];
	$BillRate = $_POST['BillRate'];
	$BillingContractId = $_POST['BillingContractId'];
	$UserId = $_POST['UserId'];

	
	
	$result = $rcr_transaction->setSchDistrictServiceDetails(	$DistrictId,
																$ServiceId,
																$ServiceTypeId,
																$PrimaryVendorFL,
																$PayRate,
																$BillRate,
																$BillingContractId,
																$UserId
															); 

	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
