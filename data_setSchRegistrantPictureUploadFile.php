<?php

	require_once('DB.php');
	include('db_login.php');

	
	
	$RegistrantId = $_POST['RegistrantId'];
	$DocumentName = $_POST['OrigDocumentName'];
	$FileExt = $_POST['FileExt'];	
	$UserId = $_POST['UserId'];

	
	
	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



	//==================================
	// Check if Registrant's Document already exists
	//==================================
	
	$query = "SELECT EmplPictureFile as GeneratedName 
				FROM Registrants			 
			WHERE Id = '{$RegistrantId}' ";
	
	$result = $connection->query ($query);

	
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }


	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$generated_file_name = $row['GeneratedName'];
	}	


 

	/* Generate Name for newaly uploaded file 
	 =============================================*/
	$random_string =  generateRandomString();
	$new_file_name =  $random_string.'.'.$FileExt;
	
	$new_file_path =  '../ep/'.$new_file_name;


	/* Upload New File 
	 =============================================*/
		
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $new_file_path);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($query1)."}";
	    Return ; 

	}


	
	
	
	
	/* If Document for selected Cred. Item was prev. uploaded - delete it
	   and update RegistrantDocuments table with "new" Dociment Name      
	 ===========================================================*/



		$orig_file_path =  '../ep/'.$generated_file_name;
	
		/* Delete "Old" file form disk
		 ================================ */
		
		unlink($orig_file_path);

		/* Update Student Session Notes table  
		 ====================================== */

		$query1 =	"UPDATE Registrants	
						SET EmplPictureFile = '{$new_file_name}',
						    UserId = '{$UserId}',
						    TransDate = NOW()			 
		WHERE Id = '{$RegistrantId}' ";		
		$result1 = $connection->query ($query1);

		
		if (DB::isError($result1)){
	                die("Could not query the database:<br />$query ".DB::errorMessage($result1));
	    }


 	
	
	//====================================================
	

	
	


	
	$connection->disconnect();
	
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";
	//echo  "{ success: true, transactions: '{$query1}'}";

	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	
	
?>