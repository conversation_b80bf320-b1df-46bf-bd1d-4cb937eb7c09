<?php 

	include('db_login.php');

	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }


	$conn1=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }


	$PrintFL = $_POST['PrintFL'];
	$UserId = $_POST['UserId'];


    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);


	foreach ($Data as $StudentData) {

	    $StudentId = $StudentData['StudentId'];
		$RegistrantId = $StudentData['RegistrantId'];
		$SchoolId = $StudentData['SchoolId'];
		$StartDate = $StudentData['StartDate'];
		$EndDate = $StudentData['EndDate'];


		$query = "SELECT count(*) from SchStudentParaTempCoverageReported  

                 WHERE StudentId = '{$StudentId}'
                 AND   RegistrantId = '{$RegistrantId}'
                 AND   SchoolId = '{$SchoolId}'
                 AND   StartDate = '{$StartDate}'
                 AND   EndDate = '{$EndDate}' LIMIT 1";

			$result =  mysqli_query($conn, $query) or die
			("Error in Selecting " . mysqli_error($conn));

	
			$rowcount=mysqli_num_rows($result);
			mysqli_free_result($result);


			if (($rowcount == 1) && ($PrintFL != 1)) { // Record exists, Flag - Non Printed

				$query1 = "DELETE FROM SchStudentParaTempCoverageReported  

		                 WHERE StudentId = '{$StudentId}'
		                 AND   RegistrantId = '{$RegistrantId}'
		                 AND   SchoolId = '{$SchoolId}'
		                 AND   StartDate = '{$StartDate}'
		                 AND   EndDate = '{$EndDate}' ";


			} else { // Record does not exists
 
			$query1 = "  INSERT into SchStudentParaTempCoverageReported
						( 
							StudentId,
							RegistrantId,
							SchoolId,
							StartDate,
							EndDate,
							UserId,
							TransDate
						)
						VALUES (
							'{$StudentId}',
							'{$RegistrantId}',
							'{$SchoolId}',
							'{$StartDate}',
							'{$EndDate}',
							'{$UserId}',
                             NOW()


						)  ";
				 
			}


			$result1 =  mysqli_query($conn1, $query1) or die
			("Error in Selecting " . mysqli_error($conn1));
			mysqli_free_result($result1);

	 }

	mysqli_close($conn);
	mysqli_close($conn1);


	return  $query1 ;

?>



