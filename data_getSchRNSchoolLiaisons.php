<?php 



    require_once("db_GetSetData.php");

     $conn = getCon();
        
    $query = "         SELECT 
              a.Id,
              a.Id as id, 
              a.Id as R<PERSON><PERSON>isonId,
              a.StatusId,
              CASE a.StatusId
                WHEN '1' THEN 'Active'
                ELSE 'Inactive'
              END AS StatusDesc,
              a.LastName,
              a.FirstName,
              CONCAT(a.FirstName, ' ', a.LastName) AS RNLiaisonName,

              a.Email,
              CONCAT(c.LastName, ', ', c.FirstName) AS UserName,
              DATE_FORMAT(a.TransDate, '%m-%d-%Y') AS TransDate
            FROM
              SchRNSchoolLiaisons a
                LEFT JOIN
              Users c ON a.UserId = c.UserId
            ORDER BY a.LastName , a.FirstName  
            ";    
 
    $ret = getData ($conn, $query);
    setDisConn($conn);

    echo $ret;
    // echo $query;
 

?>
