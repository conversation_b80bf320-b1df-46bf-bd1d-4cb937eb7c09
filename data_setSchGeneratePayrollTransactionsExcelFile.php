<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

// Retrieve the server's IP address dynamically
$serverIp = $_SERVER['SERVER_ADDR'] ?? getHostByName(getHostName());

$FromDate = $_POST['FromDate'] ?? '';
$ToDate = $_POST['ToDate'] ?? '';

if (empty($FromDate) || empty($ToDate)) {
    die("Error: Missing required parameters.");
}

// Construct the URL dynamically
$url = "http://{$serverIp}:5000/generate-and-download?FromDate=" . urlencode($FromDate) . "&ToDate=" . urlencode($ToDate);

// Start output buffering
ob_start();

// Execute cURL request
$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
$ret = curl_exec($curl);
curl_close($curl);

// Save the fetched content to a file
$out_File = "../payroll_reports/payroll_transactions.xlsx";
file_put_contents($out_File, $ret);

// Ensure the file exists and has content
if (!file_exists($out_File) || filesize($out_File) === 0) {
    ob_end_clean();
    die("Error: File was not saved or is empty.");
}

// Clear output buffer
ob_end_clean();

// Send the file to the client for download
// header('Content-Description: File Transfer');
// header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
// header('Content-Disposition: attachment; filename="payroll_transactions.xlsx"');
// header('Content-Length: ' . filesize($out_File));
// header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
// header('Expires: 0');
// header('Pragma: public');

// // Send file content
// readfile($out_File);

// Optionally delete the file after download
// unlink($out_File);

// exit();