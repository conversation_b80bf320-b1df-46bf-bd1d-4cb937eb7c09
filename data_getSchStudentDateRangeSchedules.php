<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

	$StudentId = $_GET['StudentId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];

	$query = "Call  proc_getSchStudentDateRangeSchedules ( 	'{$StudentId}',
															'{$FromDate}',
															'{$ToDate}'
														)  "; 
                    


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  

	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $StudentId = $_GET['StudentId'];  
	// $FromDate = $_GET['FromDate'];
	// $ToDate = $_GET['ToDate'];


	 
	// $result = $rcr_transaction->getSchStudentDateRangeSchedules($StudentId, 
	// 															$FromDate,
	// 															$ToDate
	// 															);

	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";

?>
