<?php 

/*
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
*/

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$SchoolId = $_POST['SchoolId'];
	$SchoolHoursId = $_POST['SchoolHoursId'];
	$WeekDayId = $_POST['WeekDayId'];
	$StartTime = $_POST['StartTime'];	
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$ApplyAllDays = $_POST['ApplyAllDays'];
	$SubSchoolTypeId = $_POST['SubSchoolTypeId'];
	$UserId = $_POST['UserId'];
  			
 	

	if ($ApplyAllDays == '1') {

	        $conn1 = getCon();
	        $query1 ="DELETE FROM  SchSchoolHours
							Where SchoolId = '{$SchoolId}'
							and   SubSchoolTypeId = '{$SubSchoolTypeId}'  ";

			$ret1 =  setData ($conn1, $query1);   			
			setDisConn($conn1);


			$query ="INSERT into SchSchoolHours 
										(	SchoolId, 
											WeekDayId,
											StartTime,
											EndTime,
											TotalHours,
											SubSchoolTypeId,
											UserId,
											TransDate )
				SELECT  '{$SchoolId}',
					    WeekDayId,
						'{$StartTime}',
						'{$EndTime}',
						'{$TotalHours}',
						'{$SubSchoolTypeId}',
						'{$UserId}',
						NOW()  						       	
				FROM     DaysOfWeek	";			




			} else {

 
				if(is_numeric($SchoolHoursId) ) { 	
			
                        $query ="UPDATE SchSchoolHours
								SET SchoolId =  '{$SchoolId}', 
								StartTime =  '{$StartTime}',
								EndTime =  '{$EndTime}',
								TotalHours =  '{$TotalHours}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							WHERE Id = '{$SchoolHoursId}'
							AND SubSchoolTypeId = '{$SubSchoolTypeId}' ";
                } else {
                       $query ="INSERT into SchSchoolHours 
								(SchoolId, 
								WeekDayId,
								StartTime,
								EndTime,
								TotalHours,
								SubSchoolTypeId,
								UserId,
								TransDate )
				VALUES 	('{$SchoolId}',  
						'{$WeekDayId}',
						'{$StartTime}',
						'{$EndTime}',
						'{$TotalHours}',
						'{$SubSchoolTypeId}',
						'{$UserId}',
						NOW()  ) ";

				}

	}	
	 
						

	$ret =  setData ($conn, $query);   			
 
	//echo $query;
	setDisConn($conn);


	// Update Mandates - 100%
	//===========================  

	$Perc = '100';

    $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchSyncShoolHoursWithMandateHours.php?SchoolId=".$SchoolId;
    $url = $url.'&SubSchoolTypeId='.$SubSchoolTypeId.'&WeekDayId='.$WeekDayId.'&AllDayFL='.$ApplyAllDays.'&Perc='.$Perc.'&UserId='.$UserId;
    
    echo "url 100%:  $url<br>";


    $output = file_get_contents($url);
	$curl = curl_init();
   
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);

    // execute and return string (this should be an empty string '')
    $str = curl_exec($curl);

    echo '$str 100 :'.$str.'<br><br>';
    
    curl_close($curl);

	// Update Mandates - 80%
	//===========================  

	$Perc = '80';

    $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchSyncShoolHoursWithMandateHours.php?SchoolId=".$SchoolId;
    $url = $url.'&SubSchoolTypeId='.$SubSchoolTypeId.'&WeekDayId='.$WeekDayId.'&AllDayFL='.$ApplyAllDays.'&Perc='.$Perc.'&UserId='.$UserId;
    
 

    $output = file_get_contents($url);
	$curl = curl_init();
   
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);

    // execute and return string (this should be an empty string '')
    $str = curl_exec($curl);

    echo '$str 80 :'.$str.'<br><br>';
    
    curl_close($curl);



	// Update Mandates - 100%
	//===========================  

	$Perc = '50';

    $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchSyncShoolHoursWithMandateHours.php?SchoolId=".$SchoolId;
    $url = $url.'&SubSchoolTypeId='.$SubSchoolTypeId.'&WeekDayId='.$WeekDayId.'&AllDayFL='.$ApplyAllDays.'&Perc='.$Perc.'&UserId='.$UserId;
    
 

    $output = file_get_contents($url);
	$curl = curl_init();
   
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);

    // execute and return string (this should be an empty string '')
    $str = curl_exec($curl);

    echo '$str 50 :'.$str.'<br><br>';
    
    curl_close($curl);



	// /* Sync Para Assignment  
	//  ===================================================*/


 
		include 'db_login.php';

		try {
		    // Enable buffered queries by adding PDO::MYSQL_ATTR_USE_BUFFERED_QUERY
		    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password, [
		        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
		        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
		    ]);

		    // Define the days of the week with their corresponding names and weekday IDs
		    $days = [
		        1 => ['name' => 'Mon', 'WeekDayId' => 1],
		        2 => ['name' => 'Tue', 'WeekDayId' => 2],
		        3 => ['name' => 'Wed', 'WeekDayId' => 3],
		        4 => ['name' => 'Thu', 'WeekDayId' => 4],
		        5 => ['name' => 'Fri', 'WeekDayId' => 5]
		    ];

		    // Iterate over each day to perform the updates
		    foreach ($days as $day) {
		        try {
		            // Prepare the SQL statement with placeholders for dynamic parts
		            

		            // SNync AM Hours
		            //==================
		            $updateStmtAM = $conn->prepare("UPDATE SchStudentMandates a
		                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
		                                            JOIN SchStudents d ON a.StudentId = d.Id
		                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1AMId
		                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
		                                            AND c.WeekDayId = :WeekDayId
		                                            SET 
		                                                c.StartTime = ParaStartTime" . $day['name'] . "1,
		                                                c.EndTime = ParaEndTime" . $day['name'] . "1,
		                                                c.TotalHours = ParaTotalHours" . $day['name'] . "1,
		                                                c.UserId = a.UserId,
		                                                c.TransDate = now()  
		                                            WHERE
		                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
		                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
		                                                AND a.SchoolId = :SchoolId
		                                                AND a.EndDate >= '2025-01-01'
		                                                AND ((c.StartTime != ParaStartTime" . $day['name'] . "1 ) || (c.EndTime != ParaEndTime" . $day['name'] . "1))
		                                                
		                                                ");

		            // Bind parameters
			        $updateStmtAM->bindParam(':SchoolId', $SchoolId, PDO::PARAM_INT);
		            $updateStmtAM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);
		            
		            
		  
		            // Execute the update statement
		            $updateStmtAM->execute();
		            

		            // SNync PM Hours
		            //==================
		            $updateStmtPM = $conn->prepare("UPDATE SchStudentMandates a
		                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
		                                            JOIN SchStudents d ON a.StudentId = d.Id
		                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1PMId
		                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
		                                            AND c.WeekDayId = :WeekDayId
		                                            SET 
		                                                c.StartTime = ParaStartTime" . $day['name'] . "2,
		                                                c.EndTime = ParaEndTime" . $day['name'] . "2,
		                                                c.TotalHours = ParaTotalHours" . $day['name'] . "2,
		                                                c.UserId = a.UserId,
		                                                c.TransDate = now()    
		                                            WHERE
		                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
		                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
		                                                AND a.SchoolId = :SchoolId
		                                                AND a.EndDate >= '2025-01-01'
		                                                AND ((c.StartTime != ParaStartTime" . $day['name'] . "2 ) || (c.EndTime != ParaEndTime" . $day['name'] . "2))

		                                                ");

		            // Bind parameters
		            
			        $updateStmtPM->bindParam(':SchoolId', $SchoolId, PDO::PARAM_INT);
		            $updateStmtPM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);

		   
		            
		            // Execute the update statement
		            $updateStmtPM->execute();

		            // echo "Processed " . $day['name'] . "<br>";
		        
		        } catch (PDOException $e) {
		            // Display error message for each day if it occurs
		            echo "Error processing " . $day['name'] . ": " . $e->getMessage() . "<br>";
		        }
		    }

		    // echo "Processing completed for all days.";

		} catch (PDOException $e) {
		    echo "Connection error: " . $e->getMessage();
		}

		// Close connection
		$conn = null;


 	 
 
?>

