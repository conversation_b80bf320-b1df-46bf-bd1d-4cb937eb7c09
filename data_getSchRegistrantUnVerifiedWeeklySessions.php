<?php 
	


	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$PayrollWeek = $_GET['PayrollWeek'];  
	$ServiceDate = $_GET['ServiceDate'];  


 	$query = "SELECT  	a.Id as MandateId,
						a.ServiceTypeId, 
					   	c.ServiceTypeDesc,	
				       	a.<PERSON>,
				       	a.SessionGrpSize,
				       	a.SessionFrequency,
				       	a.StudentId,
				       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
				       	a.SchoolId,
				       	b.SubSchoolTypeId,
				       	m.SchoolName,
				       	a.Billing<PERSON>ontractId,
				       	g.<PERSON>ontractDesc,
				       	a.Comments

				FROM 	SchStudentMandates a, 
						SchStudents b, 
						SchServiceTypes c, 
						SchBillingContracts g,
						SchSchools f,
						SchSubSchools m
				WHERE a.RegistrantId = '{$RegistrantId}' 
				AND a.StatusId = '1'
				AND a.StudentId = b.Id
				AND a.SchoolId = f.Id
				AND a.BillingContractId = g.Id  
				AND '{$ServiceDate}' BETWEEN a.StartDate AND a.EndDate
				AND a.ServiceTypeId = c.Id
				AND a.SchoolId = m.SchoolId
                AND b.SubSchoolTypeId = m.SubSchoolTypeId
				AND  (SELECT COUNT(*) FROM WeeklyServices d
				       	  	WHERE d.MandateId = a.Id 
				       	  	AND d.ScheduleStatusId > 5
				       	  	AND d.PayrollWeek = '{$PayrollWeek}'    
				       	) < a.SessionFrequency	
				AND NOT EXISTS (SELECT 1 FROM WeeklyServices e
							WHERE e.MandateId = a.Id 
				       	  	AND e.ScheduleStatusId > 5
				       	  	AND e.ServiceDate = '{$ServiceDate}' 
				       	  	AND a.ServiceTypeId = e.ServiceTypeId
					)	 

				ORDER BY b.LastName,  b.FirstName	
					";


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  

/*
	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_GET['RegistrantId'];  
	$PayrollWeek = $_GET['PayrollWeek'];  
	$ServiceDate = $_GET['ServiceDate'];  

	 
	$result = $rcr_transaction->getSchRegistrantUnVerifiedWeeklySessions(	$RegistrantId, 
																			$PayrollWeek,
																			$ServiceDate		
																		);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/
?>
