<?php 


   require_once("db_GetSetData.php");

   $conn = getCon();

  	$ScheduleId = $_POST['ScheduleId'];
	$StartTime = $_POST['StartTime'];	
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$ConfirmationNumber = $_POST['ConfirmationNumber'];
	$RegistrantId = $_POST['RegistrantId'];
	$UserId = $_POST['UserId'];


	if ($RegistrantId > 0) {

		$ScheduleStatusId = '7';

	} else {
		
		$ScheduleStatusId = '0';


	}
 
  
  $query = "UPDATE WeeklyServices 
	            SET StartTime = '{$StartTime}', 
                    EndTime = '{$EndTime}', 
					TotalHours = '{$TotalHours}', 
					ScheduleStatusId = '{$ScheduleStatusId}',
					ConfirmationNumber = '{$ConfirmationNumber}',
					RegistrantId = '{$RegistrantId}',
                    UserId = '{$UserId}',
                    TransDate = now()										
				WHERE 	Id = '{$ScheduleId}' "; 


  $ret = setData ($conn, $query);
  setDisConn($conn);

  echo $ret;


	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $ScheduleId = $_POST['ScheduleId'];
	// $StartTime = $_POST['StartTime'];	
	// $EndTime = $_POST['EndTime'];
	// $TotalHours = $_POST['TotalHours'];
	// $ConfirmationNumber = $_POST['ConfirmationNumber'];
	// $RegistrantId = $_POST['RegistrantId'];
	// $UserId = $_POST['UserId'];


	// if ($RegistrantId > 0) {

	// 	$ScheduleStatusId = '7';

	// } else {
		
	// 	$ScheduleStatusId = '0';


	// }


	// $result = $rcr_transaction->setSchSchoolWklyScheduleAdjust(	$ScheduleId,
	// 															$StartTime,
	// 															$EndTime,
	// 															$TotalHours,
	// 															$ScheduleStatusId,
	// 															$ConfirmationNumber,
	// 															$RegistrantId,
	// 															$UserId ); 

	// $rcr_transaction->disconnectDB (); 

	// //echo  '{ success: true };
	// echo $result;

?>
