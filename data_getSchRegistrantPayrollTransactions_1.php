<?php 



  require_once("db_GetSetData.php");

  $conn = getCon();
	
  $RegistrantId = $_GET['RegistrantId'];  
  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];

  $InclStatuses =$_GET['InclStatuses']; 
  $SchoolId =$_GET['SchoolId']; 

  if (!$SchoolId) {

    $SchoolId = '%%';
  }

$query = "SELECT   DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
                      TotalHours as TotalHoursUnf,
                      COALESCE(group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ),'') as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
                      
                      COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                        WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                      CASE  SessionGrpSize 
                        WHEN '0' THEN '1'
                      ELSE SessionGrpSize
                      END AS SessionGrpSize,

                      a.RegistrantId,
                      CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
  
                      case  e.TypeId
                         when '12' then  d.SchoolName
                         else  m.SchoolName
                      end   as SchoolName,
                      d.SchoolName as SchoolNameDBN,
                      (SELECT a.PaidFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as PaidFL,
                      (SELECT a.BilledFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as BilledFL,

                      ServiceTypeDesc                      

                  FROM  WeeklyServices a  
                          LEFT JOIN
                        SchStudents b ON   b.Id = a.StudentId
                          JOIN
                        Registrants e ON     a.RegistrantId = e.Id 
                          JOIN
                        ScheduleStatuses g ON        a.ScheduleStatusId = g.Id
                          JOIN
                        SchSchools d ON   a.SchoolId = d.Id
                          JOIN
                        SchServiceTypes h ON     a.ServiceTypeId = h.Id
                          LEFT JOIN
                        SchSubSchools m ON     a.SchoolId = m.SchoolId
                        AND   b.SubSchoolTypeId = m.SubSchoolTypeId

                        WHERE a.RegistrantId = '{$RegistrantId}'  
                        
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.SchoolId like '{$SchoolId}'
                        AND   a.ScheduleStatusId  in ({$InclStatuses})  
                        AND   a.StudentId != 0
                   GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours 

                     UNION 

 
 SELECT   DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
                      TotalHours as TotalHoursUnf,
                      '' as StudentName,
                      ''  as SessionSchedulesList,
                      
                      '' as SessionFrequency,

                      '' AS SessionGrpSize,

                      a.RegistrantId,
                      CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
  
                      d.SchoolName    as SchoolName,
                      d.SchoolName as SchoolNameDBN,
                      (SELECT a.PaidFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as PaidFL,
                      (SELECT a.BilledFL FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as BilledFL,

                      ServiceTypeDesc                      

                  FROM  WeeklyServices a  
                          JOIN
                        Registrants e ON     a.RegistrantId = e.Id 
                          JOIN
                        ScheduleStatuses g ON        a.ScheduleStatusId = g.Id
                          JOIN
                        SchSchools d ON   a.SchoolId = d.Id
                          JOIN
                        SchServiceTypes h ON     a.ServiceTypeId = h.Id

                        WHERE a.RegistrantId = '{$RegistrantId}'  
                        
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.SchoolId like '{$SchoolId}'
                        AND   a.ScheduleStatusId  in ({$InclStatuses})  
                        AND   a.StudentId = 0

                    ";

  $ret = getData ($conn, $query);
  setDisConn($conn);

  // echo $ret;  
  echo $query;  

 

?>

