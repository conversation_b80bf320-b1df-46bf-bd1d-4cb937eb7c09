<?php

    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\Exception;

    require 'PHPMailer/src/Exception.php';
    require 'PHPMailer/src/PHPMailer.php';
    require 'PHPMailer/src/SMTP.php';

 
    $RNLiaisonId = $_POST['RNLiaisonId'];
    $InclSchedules = $_POST['InclSchedules'];
    $ReportDate = $_POST['ReportDate'];
    
    echo "Step 01 <br>"; 


    $AgencyName = 'RCM Healthcare';
    $ReplyEmail = '<EMAIL>';
 
    // $ReportDate = '2023-10-11';
 
 

    // $date = strtotime($ServiceDate);;
    // $service_date_frm = date('m/d/Y',$date);;
 
    // $service_date_frm = date('m-d-Y',strtotime($ServiceDate));
    
    $charset = 'utf8mb4';

    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

   $ret_liaison = getLiaisonEmailAddress($pdo, $RNLiaisonId);

   $arr_liaison = explode(":", $ret_liaison);
   $LiaisonEmail =  $arr_liaison[0];  
   $LiaisonName  =  $arr_liaison[1];  


 
    $LiaisonEmail = '<EMAIL>';



    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNDailyReportData(?)");
    $stmt->execute([$InclSchedules]);

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


    $objPHPExcel->getActiveSheet()->getStyle("A1:K1")->getFont()->setSize(24);
    $objPHPExcel->getActiveSheet()->getStyle("A1:K1")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 1, $AgencyName); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 1, 'Date:'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, 1, $ReportDate); 

   $objPHPExcel->getActiveSheet()->getStyle("A2:K2")->getFont()->setSize(22);
    $objPHPExcel->getActiveSheet()->getStyle("A2:K2")->getFont()->setBold( true );

    // $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 2, 'District Name:'); 
    // $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 2, $DistrictName); 


    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A4:K4")->getFont()->setBold( true );

    foreach ($results[0] as $header => $value) {
        // $sheet->setCellValueByColumnAndRow($column, 1, $header);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, 4, $header); 
    
        $column++;
    }

    // Set results
    $row = 5;
    foreach ($results as $result) {
        $column = 0;
        foreach ($result as $value) {
            // $sheet->setCellValueByColumnAndRow($column, $row, $value);
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, $row, $value); 

            $column++;
        }
        $row++;
    }  
 
     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_daily_report-".$DistrictId.".xlsx";

     $objWriter->save($out_File);

    //==============

    // Send Email

    // Path to the file
    $file = $out_File;
    $filename = basename($file);

    // Determine the content type based on the file extension
    $content_type = "application/octet-stream"; // default
    if (strtolower(pathinfo($filename, PATHINFO_EXTENSION)) === 'xls') {
        $content_type = "application/vnd.ms-excel";
    } elseif (strtolower(pathinfo($filename, PATHINFO_EXTENSION)) === 'xlsx') {
        $content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    }

    // Read the file to be attached as a string
    $file_content = file_get_contents($file);
    $encoded_content = chunk_split(base64_encode($file_content));

    // A unique identifier for the email
    $boundary = md5(uniqid(time()));

    // Creating the headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "From: $AgencyName <$ReplyEmail>" . "\r\n";
    $headers .= "Reply-To: <$ReplyEmail>" . "\r\n";
    $headers .= "Content-Type: multipart/mixed; boundary = $boundary" . "\r\n\r\n";

    // Plain text version of the message
    $body = "--$boundary\r\n";
    $body .= "Content-Type: text/plain; charset=ISO-8859-1\r\n";
    $body .= "Content-Transfer-Encoding: base64\r\n\r\n";
    
    $body_content = "Dear $LiaisonName,\n\nPlease confirm if there are any school nurses listed that have not called you for attendance this morning.\n\n";
    // $body_content .= "Please confirm if the hours listed on the coverage report are the correct approved hours.\n\n\n\nThank you.\n\n\n\nRCM Health Service";
    $body_content .= "Please confirm if the hours listed on the coverage report are the correct approved hours.\n\n\n\nThank you.\n\n\n\n$AgencyName";

    // $body .= chunk_split(base64_encode("Dear $LiaisonName,\n\nPlease confirm if there are any school nurses listed that have not called you for attendance this morning.\n\nRegards,\n\nRCM Health Services"));
    $body .= chunk_split(base64_encode($body_content));

    // Attachment
    $body .= "--$boundary\r\n";
    $body .= "Content-Type: $content_type; name=\"$filename\"\r\n";
    $body .= "Content-Transfer-Encoding: base64\r\n";
    $body .= "Content-Disposition: attachment; filename=\"$filename\"\r\n\r\n";
    $body .= $encoded_content . "\r\n\r\n";
    $body .= "--$boundary--";

    // Send email
    $subject = "$AgencyName daily coverage report for $ReportDate";
    
    $LiaisonEmail = '<EMAIL>,<EMAIL>,<EMAIL>';
    $to = $LiaisonEmail;
    $mail_result = mail($to, $subject, $body, $headers);

    if ($mail_result) {
        echo "Mail sent successfully";
    } else {
        echo "Mail failed";
    }

    function getLiaisonEmailAddress($pdo, $RNLiaisonId) {

        $stmt = $pdo->prepare("SELECT 
                                Email AS LiaisonEmail,
                                concat(FirstName,' ',LastName)  as LiaisonName
                            FROM
                                SchRNSchoolLiaisons
                            WHERE
                                Id = ?");
        $stmt->execute([$RNLiaisonId]);
        $row = $stmt->fetch();

        return $row['LiaisonEmail'].':'.$row['LiaisonName']; 

    }


?>
