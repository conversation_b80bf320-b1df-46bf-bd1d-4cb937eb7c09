<?php 

 


	require_once("db_GetSetData.php"); 	

	$conn = getCon();

	
	$Id = $_POST['Id'];  
	$StatusId = $_POST['StatusId'];  
	$LastName = $_POST['LastName'];  
	$FirstName = $_POST['FirstName'];  
	$Email = $_POST['Email'];  
	$UserId = $_POST['UserId'];  

  	if(is_numeric($Id) ) { 	


    $query =" UPDATE  SchRNSchoolLiaisons
			SET
			 
			StatusId = '{$StatusId}',
			FirstName = '{$FirstName}',
			LastName = '{$LastName}',
			Email = '{$Email}',
			UserId = '{$UserId}'  
			WHERE Id = '{$Id}' 


        ";     

 
							        
    } else {

     $query ="	INSERT INTO  SchRNSchoolLiaisons
			(
			FirstName,
			LastName,
			Email,
			UserId
            )
			VALUES
			( 
			'{$FirstName}',
			'{$LastName}',
			'{$Email}',
			'{$UserId}'
            ) 
 

			 


        ";      

    }




	$ret = setData ($conn, $query);
	setDisConn($conn);


	// echo $ret;
	echo $query;
	
 

?>
