<?php 

 error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$form_data = json_decode(file_get_contents('php://input'));

	$Id =  	$form_data->{'Id'};
	$StatusId =		$form_data->{'StatusId'};

	$SearchId =		$form_data->{'SearchId'};
	$ExtId =		$form_data->{'ExtId'};
	$TerminationType =		$form_data->{'TerminationType'};
	$TerminationReason =		$form_data->{'TerminationReason'};
	$TerminationDate =		$form_data->{'TerminationDate'};
	$BirthDate =		$form_data->{'BirthDate'};
	$HireDate =		$form_data->{'HireDate'};
	$Gender =		$form_data->{'Gender'};
	$TypeId =		$form_data->{'TypeId'};
	$FirstName =		$form_data->{'FirstName'};
	$LastName =		$form_data->{'LastName'};
	$MiddleInitial =		$form_data->{'MiddleInitial'};
	$StreetAddress1 =		$form_data->{'StreetAddress1'};
	$StreetAddress2 =		$form_data->{'StreetAddress2'};
	$City =		$form_data->{'City'};
	$State =		$form_data->{'State'};
	$ZipCode =		$form_data->{'ZipCode'};
	$MobilePhone =		$form_data->{'MobilePhone'};
	$HomePhone =		$form_data->{'HomePhone'};
	$Fax =		$form_data->{'Fax'};
	$Email =		$form_data->{'Email'};
	$VendorNumber =		$form_data->{'VendorNumber'};
	$HrId =		$form_data->{'HrId'};
	$HrTypeId =		$form_data->{'HrTypeId'};
	$BoroughId =		$form_data->{'BoroughId'};
	$RecruitorId =		$form_data->{'RecruitorId'};
	$RNLicenseNumber =		$form_data->{'RNLicenseNumber'};
	$Comments =		$form_data->{'Comments'};
 	$UserId =		$form_data->{'UserId'};
 


	$FirstName = mysqli_real_escape_string($conn, $FirstName); 
	$LastName = mysqli_real_escape_string($conn, $LastName); 
	$Comments = mysqli_real_escape_string($conn, $Comments); 

	$FirstName = trim($FirstName); 
	$LastName = trim($LastName); 


				if(is_numeric($Id) ) { 	
			
                        $query ="UPDATE Registrants 
								set StatusId =  '{$StatusId}', 
								TerminationType =  '{$TerminationType}',
								TerminationReason =  '{$TerminationReason}',
								TerminationDate =  '{$TerminationDate}',
								BirthDate =  '{$BirthDate}',
								HireDate =  '{$HireDate}',
								Gender =  '{$Gender}',

								TypeId =  '{$TypeId}',
								ExtId =  '{$ExtId}',
								FirstName =  '{$FirstName}',
								LastName	 =  '{$LastName}',
								MiddleInitial	 =  '{$MiddleInitial}',
								StreetAddress1	 =  '{$StreetAddress1}',
								StreetAddress2	 =  '{$StreetAddress2}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								MobilePhone	 =  '{$MobilePhone}',
								HomePhone	 =  '{$HomePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								VendorNumber =  '{$VendorNumber}',
								HrId =  '{$HrId}',
								HrTypeId =  '{$HrTypeId}',
								BoroughId =  '{$BoroughId}',	
								RecruitorId =  '{$RecruitorId}',
								Comments =  '{$Comments}',
								RNLicenseNumber =  '{$RNLicenseNumber}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="INSERT into Registrants  
								(StatusId, 
                                SearchId, 		
                                ExtId,								
								TerminationType,
								TerminationReason,
								TerminationDate,
								BirthDate,
								HireDate,
								Gender,

								TypeId,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								Fax,
								Email,
								VendorNumber,
								HrId,
								HrTypeId,
								BoroughId,
								RecruitorId,
								Comments,
								RNLicenseNumber,
								UserId,
								TransDate )
				values 	('{$StatusId}',  
						'{$SearchId}',	
						'{$ExtId}',
						'{$TerminationType}',
						'{$TerminationReason}',						
						'{$TerminationDate}',
						'{$BirthDate}',
						'{$HireDate}',
						'{$Gender}',
						
						'{$TypeId}',  
						'{$FirstName}',  
						'{$LastName}',  
						'{$MiddleInitial}',  
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$MobilePhone}', 
						'{$HomePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$VendorNumber}',
						'{$HrId}',
						'{$HrTypeId}',
						'{$BoroughId}',
						'{$RecruitorId}',
						'{$Comments}',
						'{$RNLicenseNumber}',
						'{$UserId}',
						NOW()  ) ";
		}				

	$ret =  setData ($conn, $query);   			

	setDisConn($conn);

	echo $query;


// 	require "ewDataHandler.php";   
	  
// 	$rcr_transaction = new dataHandler();  

// 	$form_data = json_decode(file_get_contents('php://input'));

	
 

// $result = $rcr_transaction->setSelRegistrant(	$form_data->{'Id'},
// 												$form_data->{'SearchId'},
// 												$form_data->{'StatusId'},	
// 												$form_data->{'ExtId'},
// 												$form_data->{'TerminationType'},
// 												$form_data->{'TerminationReason'},

// 												$form_data->{'TerminationDate'},
// 												$form_data->{'BirthDate'},
// 												$form_data->{'HireDate'},

// 												$form_data->{'TypeId'},
// 												$form_data->{'FirstName'},
// 												$form_data->{'LastName'},
// 												$form_data->{'MiddleInitial'},
// 												$form_data->{'StreetAddress1'},
// 												$form_data->{'StreetAddress2'},
// 												$form_data->{'City'},
// 												$form_data->{'State'},
// 												$form_data->{'ZipCode'},
// 												$form_data->{'MobilePhone'},
// 												$form_data->{'HomePhone'},
// 												$form_data->{'Fax'},
// 												$form_data->{'Email'},
// 												$form_data->{'VendorNumber'},
// 												$form_data->{'HrId'},
// 												$form_data->{'HrTypeId'},
// 												$form_data->{'BoroughId'},
// 												$form_data->{'RecruitorId'},

// 												$form_data->{'Comments'},
// 												$form_data->{'UserId'} ); 
	 

// 	/* If Existing Registrant's  "TypeId" was Changed - Change Credentialing Items
// 	 ===============================================================================*/  
 
// 	$registrant_id =  $form_data->{'Id'};	

 	
// 	if ((is_numeric($registrant_id)) && ($form_data->{'TypeId'} != $form_data->{'OrigTypeId'})) {

// 		 $result1 = $rcr_transaction->setRegistrantTypeCredItemsChange (	$registrant_id,
// 																			$form_data->{'OrigTypeId'},
// 																			$form_data->{'TypeId'},
// 																			$form_data->{'UserId'}	
// 		 																);			

// 	}
 
 

// 	/* Create "Atomatic" NYC Board of Education Approval for all New Registrants 
// 	 ===============================================================================*/
 
 
// 	if (!is_numeric($registrant_id)) {

// 		 $result2 = $rcr_transaction->setSchRegistrantClientApproval (	    $form_data->{'SearchId'},
// 																			$form_data->{'TypeId'},
// 																			$form_data->{'UserId'}	
// 		 																);			

// 	}

 

// 	$rcr_transaction->disconnectDB (); 

// 	//echo  '{ success: true }';

// 	echo $result;


?>
