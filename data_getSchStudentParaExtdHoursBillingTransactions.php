
<?php 
	
 
    require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId  =  $_GET['RegistrantId'];
	$FromDate  =  $_GET['FromDate'];
	$ToDate  =  $_GET['ToDate'];

    $query = "SELECT  	 a.Id, 
    					 DATE_FORMAT( b.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
						 DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
						 DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
				         a.TotalHours,
				         CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', d.RegistrantTypeDesc,')' ) as RegistrantName,
				         e.ExtId as StudentExtId,
				         CONCAT( trim( e.LastName) , ', ', trim( e.FirstName)) as StudentName,
				         ServiceTypeDesc,
				         BillAmount
				 
				 from 	SchStudentParaUnBilledTransactions a, 
						WeeklyServices b,
				        Registrants c,
				        RegistrantTypes d,
				        SchStudents e,
				        SchServiceTypes f
				 where a.ScheduleId = b.Id
				 and   b.RegistrantId = c.Id
				 and   c.TypeId = d.Id
				 and   b.StudentId = e.Id
				 and   b.ServiceTypeId = f.Id 
				 AND   a.StatusId = 0
				 AND   b.ServiceDate between '{$FromDate}' and '{$ToDate}'  
				 AND   b.RegistrantId =  '{$RegistrantId}'

				 ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 

  
?>

 