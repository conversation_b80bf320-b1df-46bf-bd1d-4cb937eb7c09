<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$BoroughId = $_GET['BoroughId'];


    $query = "	SELECT  id,
    					id as RegistrantId,	
						FirstName,
						LastName,
						CONCAT( trim( LastName) , ', ', trim(FirstName) )as RegistrantName,
					    StreetAddress1,
					    City,
					    State,
					    ZipCode,
					    COALESCE(MobilePhone,'') as MobilePhone,
					    COALESCE(Email,'') as Email						
					FROM Registrants   
						WHERE TypeId  = '23'   
						AND StatusID = '1'
                        AND BoroughId = '{$BoroughId}'
					ORDER BY LastName, FirstName
 	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	//echo $query;


?>
