<?php 
	
	// error_reporting(E_ALL);
	// ini_set('display_errors', TRUE);
	// ini_set('display_startup_errors', TRUE);


   require_once("db_GetSetData.php");

  $conn = getCon();

  $StudentId = $_GET['StudentId'];
  $AssignmentId = $_GET['AssignmentId'];
  $AssignStartDate = $_GET['AssignStartDate'];
  $AssignEndDate = $_GET['AssignEndDate']; 
  $CoverageType = $_GET['CoverageType'];
  $StartTime = $_GET['StartTime'];

  if ($CoverageType == 'Student') {


 



 $query = "SELECT distinct  c.Id as id,
    			  c.Id as RegistrantId,
		       		CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName  
		 
		 FROM 	 
		        Registrants c,
		        RegistrantTypes f,
		        
		        SchServiceTypes d
		WHERE    
		 
		    c.TypeId = f.Id 
		AND   c.TypeId = d.RegistrantTypeId 
		AND   c.TypeId = '12' 
		AND not exists (
							SELECT 1 FROM SchSchoolAssignmentDetails e,
							              SchSchoolAssignmentHeader g 

							    
							   where g.StatusId = 1
							   and g.Id = e.AssignmentId
							   AND e.RegistrantId = c.Id
							   and g.StartDate between  '{$AssignStartDate}' and '{$AssignEndDate}' 
							   AND '{$StartTime}' >= e.StartTime 
			                   AND '{$StartTime}' < e.EndTime 


			            ) 
		AND not exists (
							SELECT 1 FROM SchStudentAssignmentDetails e,
							              SchStudentAssignmentHeader g  
							   where g.StudentId != '{$StudentId}' 
							   and g.StatusId = 1
							   and g.Id = e.AssignmentId
							   AND e.RegistrantId = c.Id
							   and g.StartDate between  '{$AssignStartDate}' and '{$AssignEndDate}'
							   AND '{$StartTime}' >= e.StartTime 
			                   AND '{$StartTime}' < e.EndTime 
 

			            ) 


		ORDER BY c.LastName, c.FirstName "; 

  } else {

  
 $query = "SELECT distinct
				c.Id AS id,
				c.Id as  RegistrantId,
				CONCAT(TRIM(c.LastName),
						', ',
						TRIM(c.FirstName),
						' (',
						RegistrantTypeDesc,
						')') AS RegistrantName
			FROM
				 
				Registrants c,
				RegistrantTypes f,
				SchServiceTypes d
			WHERE
				 
					 
					  c.TypeId = f.Id
					AND c.TypeId = d.RegistrantTypeId
					AND c.TypeId = '12'
					AND NOT EXISTS( SELECT 
						1
					FROM
						SchSchoolAssignmentDetails e,
						SchSchoolAssignmentHeader g
					WHERE
						g.Id != '{$AssignmentId}'
							AND g.StatusId = 1
							AND g.Id = e.AssignmentId
							AND e.RegistrantId = c.Id
							and g.StartDate between  '{$AssignStartDate}' and '{$AssignEndDate}' 
							AND '{$StartTime}' >= e.StartTime 
			                AND '{$StartTime}' < e.EndTime 

					)		 
					AND NOT EXISTS( SELECT 
						1
					FROM
						SchStudentAssignmentDetails h,
						SchStudentAssignmentHeader i
					WHERE
						i.StatusId = 1
							AND i.ServiceTypeId IN (43 , 44, 45)
							AND i.Id = h.AssignmentId
							AND h.RegistrantId = c.Id
					        AND i.StartDate between  '{$AssignStartDate}' and '{$AssignEndDate}' 
					        AND '{$StartTime}' >= h.StartTime 
			                AND '{$StartTime}' < h.EndTime 

					)             
			ORDER BY c.LastName , c.FirstName
 "; 


  }





  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  // echo $query;


  // echo 'rt'; 
	
  
?>
