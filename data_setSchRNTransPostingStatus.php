<?php 
	

require_once("db_GetSetData.php");

	$conn = getCon();

  $ScheduleId = $_POST['ScheduleId'];  
  $PostingStatusId = $_POST['PostingStatusId'];
  $PostingStatusComments = $_POST['PostingStatusComments'];
  $UserId = $_POST['UserId'];
   
      $query = "INSERT INTO SchRNTransactionPostings
				(
					ScheduleId,
					PostingStatusId,
					PostingStatusComments,
					UserId 

				)
				
				VALUES
				(
				'{$ScheduleId}',
				'{$PostingStatusId}',
				'{$PostingStatusComments}',
				'{$UserId}' 
               ) 
          	
				ON DUPLICATE KEY UPDATE
				   PostingStatusId = '{$PostingStatusId}',
				   PostingStatusComments = '{$PostingStatusComments}'

          ";

	 
   $ret = setData ($conn, $query);
   setDisConn($conn);

	echo $ret;


?>
