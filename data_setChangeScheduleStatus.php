<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$ScheduleId = $_POST['ScheduleId'];
$ScheduleStatusId = $_POST['ScheduleStatusId'];
$UserId = $_POST['UserId'];


$sch_arr = explode(",",$ScheduleId);

foreach ($sch_arr as &$selSchedule) {


	$result = $rcr_transaction->setChangeScheduleStatus($selSchedule,
														$ScheduleStatusId,
														$UserId ); 



}

		
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result;
?>
