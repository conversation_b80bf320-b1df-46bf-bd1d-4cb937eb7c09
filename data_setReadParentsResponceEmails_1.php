<?php 

	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


	require_once("db_GetSetData.php");

	$conn = getCon();

			$query1 ="SELECT 
		    ApprovalEmail, ApprovalEmailPass
		FROM
		    Company";

		  $result1 =  mysqli_query($conn, $query1) or die
		  ("Error in Selecting " . mysqli_error($conn));

	  while ($row1 = $result1->fetch_assoc()) {

	  	   $username = $row1['ApprovalEmail'];
	  	   $password = $row1['ApprovalEmailPass'];


	  }	


 		setDisConn($conn);

	
	 /* connect to gmail */
	$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
	// $username = '<EMAIL>';
	// $password = 'tyaffqbzbhgsfhbp';

/* try to connect */
$inbox = imap_open($hostname,$username,$password) or die('Cannot connect to Gmail: ' . imap_last_error());

/* grab emails */
//$emails = imap_search($inbox,'ALL');
$emails = imap_search($inbox,'UNSEEN');

//$search_string = ' BODY "CONFIRMED"';   
//$emails = imap_search($inbox, $search_string);




/* if emails are returned, cycle through each... */
if($emails) {
	
	/* begin output var */
	$output = '';
	
	/* put the newest emails on top */
	rsort($emails);
	
	/* for every email... */
	foreach($emails as $email_number) {
		
		/* get information specific to this email */
		//$overview = imap_fetch_overview($inbox,$email_number,0);
		//$message = imap_fetchbody($inbox,$email_number,2);

   	try
    {


   		$headers = imap_fetchheader($inbox, $email_number, FT_PREFETCHTEXT);
        $body_eml = imap_body($inbox, $email_number);



    	$body = imap_body($inbox, $email_number);
    	$body = base64_decode($body);


		//echo '</br></br>$body: '.$body.'</br></br>'; 


		$message_body = imap_fetchtext($inbox,$email_number);
		$saved_email = imap_fetchtext($inbox,$email_number);
		
		echo 'saved_email: 	'.$saved_email.'</br></br>';
		//$message_body = imap_fetchbody($inbox,$email_number,2.1);

		$message_body = quoted_printable_decode($message_body);


		$message_plain = imap_fetchbody($inbox,$email_number,1);



		//$message_plain = quoted_printable_decode($message_plain);



		$body_1 = imap_fetchbody($inbox,$email_number,1);
		//$body_1 = imap_base64($body_1);
		//$body_1 = imap_qprint($body_1);		
		//echo '</br></br>$body_1: '.$body_1.'</br></br>'; 

 
 

		//=====

		$structure = imap_fetchstructure($inbox, $email_number);


        if(isset($structure->parts) && is_array($structure->parts) && isset($structure->parts[1])) {
            $part = $structure->parts[1];
            $message_1 = imap_fetchbody($inbox,$email_number,1);

            echo 'Ecoding... $part->encoding: 	'.$part->encoding.'</br></br>';
            echo 'message_1: 	'.$message_1.'</br></br>';

            $find_embeded_string = stristr($message_1, '#s');

            echo 'find_embeded_string: 	'.$find_embeded_string.'</br></br>';

            if(($part->encoding == 3) && ($find_embeded_string == '') ) {

                $message_1 = imap_fetchbody($inbox,$email_number,1.2);
                $message_plain = imap_base64($message_1);
                //$message_plain = $body_1;


                $message_2 = imap_fetchbody($inbox,$email_number,2);
                $message_2 = imap_base64($message_2);
                echo 'message_2: 	'.$message_2.'</br>';


                if (!$message_plain) {
                     $message_plain = $message_2;

                }
            echo 'message_plain: 	'.$message_plain.'</br>';


            } else if($part->encoding == 1) {
                $message_plain = imap_8bit($message_1);

            } else if($part->encoding == 4) {
                //$message_plain = imap_8bit($message_1);
                
            	if ($find_embeded_string == '') {

	                $message_plain = imap_base64($message_1);

            	} else {

            		$message_plain = $message_1;


            	}

            } else {
                $message_plain = imap_qprint($message_1);
            }
        } else {
            
            $$message_2 = imap_fetchbody($inbox,$email_number,1);
            $message_plain = imap_base64($message_2);
            
            echo 'message_plain: 	'.$message_plain.'</br>';
            //$message_plain = imap_base64($body);
            //$message_plain = $body_1 ;

            echo 'No Ecoding....</br>';
        }
        // if(isset($structure->parts) && is_array($structure->parts) && isset($structure->parts[1])) {
        //     $part = $structure->parts[1];
        //     $message_1 = imap_fetchbody($inbox,$email_number,1);

        //     echo 'Ecoding... $part->encoding: 	'.$part->encoding.'</br></br>';
        //     echo 'message_1: 	'.$message_1.'</br></br>';

        //     if($part->encoding == 3) {

        //         $message_1 = imap_fetchbody($inbox,$email_number,1.2);
        //         $message_plain = imap_base64($message_1);
        //         //$message_plain = $body_1;


        //         $message_2 = imap_fetchbody($inbox,$email_number,2);
        //         $message_2 = imap_base64($message_2);
        //         //echo 'message_2: 	'.$message_2.'</br>';


        //         if (!$message_plain) {
        //              $message_plain = $message_2;

        //         }
        //     //echo 'message_plain: 	'.$message_plain.'</br>';


        //     } else if($part->encoding == 1) {
        //         $message_plain = imap_8bit($message_1);
        //     } else {
        //         //$message_plain = imap_qprint($message_1);
        //         $message_plain = $message_body;

        //     }
        // } else {
            
        //     // $$message_2 = imap_fetchbody($inbox,$email_number,1);
        //     // $message_plain = imap_base64($$message_2);
            
        //         $message_plain = $message_body;

        //     //echo 'message_plain: 	'.$message_plain.'</br>';
        //     //$message_plain = imap_base64($body);
        //     //$message_plain = $body_1 ;

        //     //echo 'No Ecoding....</br>';
        // }

        

		$message_body_upper = strtoupper($message_plain);


		$message_plain = str_replace("#default#","",$message_plain);



 
	    //echo 'message_plain for encoding 4: '.$message_plain.'</br>';


        $embeded_string = strstr($message_plain, '#s');
		//$embeded_string = str_replace(" ","",$embeded_string);        
		// $embeded_string = str_replace("!","",$embeded_string);        
		//$embeded_string = str_replace("-","",$embeded_string);        

        //$embeded_string = preg_replace('/[^A-Za-z0-9\-$#]/', '', $embeded_string);


        echo '$embeded_string: '.$embeded_string.'</br>'; 

         
        $search_id = get_string_between($embeded_string, '#s', '$s');
        

         echo '$search_id: '.$search_id.'</br>'; 


        //=========
   
 
	    } 	
	    catch (Exception $e) {
		    

		    echo 'Caught exception: ',  $e->getMessage(), "\n";
		}	 
        

 

        //if ($confirmed_count > 1) {
        if ($search_id) {


// Get Mandate Type
//=================


 //===============	

		  	/* Generate Name for email copy uploaded file 
			 =============================================*/
			$new_file_name =  generateRandomString();
			$new_file_path =  '../em/'.$new_file_name.'.eml';
			$db_new_file_name =  $new_file_name.'.eml';


   
// Procoss RSA 7a Mandate - Start
//============================




				$conn = getCon();

				$query ="UPDATE SchRsa7aFormSignatures 
						   SET 	StatusId = '4',
								ParentSignatureName =  'Verified via Email',  
								ParentApprovalEmailFileName =   '{$db_new_file_name}',      
								ParentSignatureTimeStamp = NOW()

						WHERE   SearchId = '{$search_id}'
  						";

			    echo 'RSA 7a  $query:'.$query.'</br>';			


			  $result =  mysqli_query($conn, $query) or die
			  ("Error in Selecting " . mysqli_error($conn));

	 		  setDisConn($conn);





		    echo '</br></br>Save email....</br></br>'; 	
        
		    file_put_contents($new_file_path, $headers . "\n" . $saved_email);


 

        }



	}
	
	  

} 

	/* close the connection */
	imap_close($inbox);
	 
	function get_string_between($string, $start, $end){
	    $string = ' ' . $string;
	    $ini = strpos($string, $start);
	    if ($ini == 0) return '';
	    $ini += strlen($start);
	    $len = strpos($string, $end, $ini) - $ini;
	    return substr($string, $ini, $len);
	}


	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	

 

?>

