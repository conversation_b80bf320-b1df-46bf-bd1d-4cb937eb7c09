	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_setSchStudentAssignmentFromMandate$$

	CREATE PROCEDURE proc_setSchStudentAssignmentFromMandate (IN 	p_mandate_id INT, p_user_id INT ) 
																	   


BEGIN


	DECLARE v_Registrant_Id, v_Student_Id, v_Assignment_Id, v_Para_Trans_Perc, v_Service_TypeId INT;
	DECLARE v_Search_Id VARCHAR(45);
	DECLARE V_Para_Start_Time_Mon1, V_Para_End_Time_Mon1, V_Para_Start_Time_Mon2, V_Para_End_Time_Mon2 TIME;
	DECLARE V_Para_Start_Time_Tue1, V_Para_End_Time_Tue1, V_Para_Start_Time_Tue2, V_Para_End_Time_Tue2 TIME;
	DECLARE V_Para_Start_Time_Wed1, V_Para_End_Time_Wed1, V_Para_Start_Time_Wed2, V_Para_End_Time_Wed2 TIME;
	DECLARE V_Para_Start_Time_Thu1, V_Para_End_Time_Thu1, V_Para_Start_Time_Thu2, V_Para_End_Time_Thu2 TIME;
	DECLARE V_Para_Start_Time_Fri1, V_Para_End_Time_Fri1, V_Para_Start_Time_Fri2, V_Para_End_Time_Fri2 TIME;
	DECLARE V_Para_TotalHoursMon1, V_Para_TotalHoursMon2 DECIMAL(5,2);
	DECLARE V_Para_TotalHoursTue1, V_Para_TotalHoursTue2 DECIMAL(5,2);
	DECLARE V_Para_TotalHoursWed1, V_Para_TotalHoursWed2 DECIMAL(5,2);
	DECLARE V_Para_TotalHoursThu1, V_Para_TotalHoursThu2 DECIMAL(5,2);
	DECLARE V_Para_TotalHoursFri1, V_Para_TotalHoursFri2 DECIMAL(5,2);


	/* Get Search ID   
	  =================================================*/ 	

	SELECT rand()  INTO v_Search_Id ;
 

	/* Get Student ID   
	  =================================================*/ 	

	SELECT 	 StudentId,
	         RegistrantId,
			 ParaTransportPerc,
			 ServiceTypeId,

			 ParaStartTimeMon1,
			 ParaEndTimeMon1,
			 ParaTotalHoursMon1,
			 ParaStartTimeMon2,
			 ParaEndTimeMon2,
			 ParaTotalHoursMon2,
			 ParaStartTimeTue1,
		     ParaEndTimeTue1,
		     ParaTotalHoursTue1,
		     ParaStartTimeTue2,
		     ParaEndTimeTue2,
		     ParaTotalHoursTue2,

	         ParaStartTimeWed1,
             ParaEndTimeWed1,
             ParaTotalHoursWed1,
             ParaStartTimeWed2,
             ParaEndTimeWed2,
             ParaTotalHoursWed2,
             ParaStartTimeThu1,
             ParaEndTimeThu1,
             ParaTotalHoursThu1,
             ParaStartTimeThu2,
             ParaEndTimeThu2,
             ParaTotalHoursThu2,

             ParaStartTimeFri1,
             ParaEndTimeFri1,
             ParaTotalHoursFri1,
             ParaStartTimeFri2,
             ParaEndTimeFri2,
             ParaTotalHoursFri2 


	       INTO  v_Student_Id,
	       		 v_Registrant_Id,
	       		 v_Para_Trans_Perc,
				 v_Service_TypeId,
				 V_Para_Start_Time_Mon1,
		         V_Para_End_Time_Mon1,
		         V_Para_TotalHoursMon1,
		         V_Para_Start_Time_Mon2,
		         V_Para_End_Time_Mon2,
		         V_Para_TotalHoursMon2,
		         V_Para_Start_Time_Tue1,
                 V_Para_End_Time_Tue1,
                 V_Para_TotalHoursTue1,
                 V_Para_Start_Time_Tue2,
                 V_Para_End_Time_Tue2,
                 V_Para_TotalHoursTue2,

                 V_Para_Start_Time_Wed1,
                 V_Para_End_Time_Wed1,
                 V_Para_TotalHoursWed1,
                 V_Para_Start_Time_Wed2,
	             V_Para_End_Time_Wed2,
	             V_Para_TotalHoursWed2,
	             V_Para_Start_Time_Thu1,
	             V_Para_End_Time_Thu1,
	             V_Para_TotalHoursThu1,
	             V_Para_Start_Time_Thu2,
	             V_Para_End_Time_Thu2,
	             V_Para_TotalHoursThu2,

	             V_Para_Start_Time_Fri1,
	             V_Para_End_Time_Fri1,
	             V_Para_TotalHoursFri1,
	             V_Para_Start_Time_Fri2,
	             V_Para_End_Time_Fri2,
	             V_Para_TotalHoursFri2 

		FROM SchStudentMandates
	WHERE Id = p_mandate_id ;
 

  	
 
 
	IF (v_Para_Trans_Perc = 0) THEN /* 1 to 1 - Start   */
 	



 					INSERT INTO SchStudentAssignmentHeader /* Assignment Header  */
					( 
					SearchId,
					StudentId,
					ServiceTypeId,
					StatusId,
					StartDate,
					EndDate,
					UserId,
					TransDate)
					 
				SELECT
					v_Search_Id,
					StudentId,
					ServiceTypeId,
					'1',
					StartDate,
					EndDate,
					p_user_id,
					NOW()  
				FROM SchStudentMandates
				WHERE  Id = p_mandate_id	
					;


				  	SELECT SLEEP(5);	 


					/*=======   Get Assignment ID =======*/

					SELECT Id INTO v_Assignment_Id
						FROM SchStudentAssignmentHeader
					WHERE SearchId = v_Search_Id ;

					/*=======   Set Assignment Details - Mon =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'1',
									V_Para_Start_Time_Mon1,
        							V_Para_End_Time_Mon1,
        							V_Para_TotalHoursMon1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )   
 
						;
					 

 	
					/*=======   Set Assignment Details - Tue =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'2',
									V_Para_Start_Time_Tue1,
        							V_Para_End_Time_Tue1,
        							V_Para_TotalHoursTue1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;


 					/*=======   Set Assignment Details - Wed =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'3',
									V_Para_Start_Time_Wed1,
        							V_Para_End_Time_Wed1,
        							V_Para_TotalHoursWed1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;

					/*=======   Set Assignment Details - Thu =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'4',
									V_Para_Start_Time_Thu1,
        							V_Para_End_Time_Thu1,
        							V_Para_TotalHoursThu1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;

					/*=======   Set Assignment Details - Fri =======*/

						

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'5',
									V_Para_Start_Time_Fri1,
        							V_Para_End_Time_Fri1,
        							V_Para_TotalHoursFri1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;


		
	END IF; /* 1 to 1 - End   */

	/*=============================*/

 
	IF (v_Para_Trans_Perc != 0) THEN /* Transporation - Start   */

	SELECT rand()  INTO v_Search_Id ;


 					/*========== Bus - To School =========*/

 					INSERT INTO SchStudentAssignmentHeader /* Assignment Header - To School Assignment  */
					( 
					SearchId,
					StudentId,
					ServiceTypeId,
					StatusId,
					StartDate,
					EndDate,
					UserId,
					TransDate)
					 
				SELECT
					v_Search_Id,
					StudentId,
					'8',
					'1',
					StartDate,
					EndDate,
					p_user_id,
					NOW()  
				FROM SchStudentMandates
				WHERE  Id = p_mandate_id	
					;


				  	SELECT SLEEP(5);	 


					/*=======   Get Assignment ID =======*/

					SELECT Id INTO v_Assignment_Id
						FROM SchStudentAssignmentHeader
					WHERE SearchId = v_Search_Id ;

					/*=======   Set Assignment Details - Mon =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'1',
									V_Para_Start_Time_Mon1,
        							V_Para_End_Time_Mon1,
        							V_Para_TotalHoursMon1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )   
 
						;
					 

 	
					/*=======   Set Assignment Details - Tue =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'2',
									V_Para_Start_Time_Tue1,
        							V_Para_End_Time_Tue1,
        							V_Para_TotalHoursTue1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;


 					/*=======   Set Assignment Details - Wed =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'3',
									V_Para_Start_Time_Wed1,
        							V_Para_End_Time_Wed1,
        							V_Para_TotalHoursWed1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;

					/*=======   Set Assignment Details - Thu =======*/

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'4',
									V_Para_Start_Time_Thu1,
        							V_Para_End_Time_Thu1,
        							V_Para_TotalHoursThu1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;

					/*=======   Set Assignment Details - Fri =======*/

						

						
						INSERT into SchStudentAssignmentDetails   
									(AssignmentId, 
									WeekDayId,
									StartTime,
									EndTime,
									TotalHours,
									RegistrantId,
									UserId,
									TransDate )
							VALUES 	(v_Assignment_Id,  
									'5',
									V_Para_Start_Time_Fri1,
        							V_Para_End_Time_Fri1,
        							V_Para_TotalHoursFri1,
									v_Registrant_Id,  
									p_user_id,
									NOW() )  
 
						;


          /*========== Bus - From School =========*/

	      SELECT rand()  INTO v_Search_Id ;
 

          INSERT INTO SchStudentAssignmentHeader /* Assignment Header - From School Assignment  */
          ( 
          SearchId,
          StudentId,
          ServiceTypeId,
          StatusId,
          StartDate,
          EndDate,
          UserId,
          TransDate)
           
        SELECT
          v_Search_Id,
          StudentId,
          '13',
          '1',
          StartDate,
          EndDate,
          p_user_id,
          NOW()  
        FROM SchStudentMandates
        WHERE  Id = p_mandate_id  
          ;


            SELECT SLEEP(5);   


          /*=======   Get Assignment ID =======*/

          SELECT Id INTO v_Assignment_Id
            FROM SchStudentAssignmentHeader
          WHERE SearchId = v_Search_Id ;

          /*=======   Set Assignment Details - Mon =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id,  
                  '1',
                  V_Para_Start_Time_Mon2,
                      V_Para_End_Time_Mon2,
                      V_Para_TotalHoursMon2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )   
 
            ;
           

  
          /*=======   Set Assignment Details - Tue =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id,  
                  '2',
                  V_Para_Start_Time_Tue2,
                      V_Para_End_Time_Tue2,
                      V_Para_TotalHoursTue2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*=======   Set Assignment Details - Wed =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id,  
                  '3',
                  V_Para_Start_Time_Wed2,
                      V_Para_End_Time_Wed2,
                      V_Para_TotalHoursWed2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Thu =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id,  
                  '4',
                  V_Para_Start_Time_Thu2,
                      V_Para_End_Time_Thu2,
                      V_Para_TotalHoursThu2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Fri =======*/

            

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id,  
                  '5',
                  V_Para_Start_Time_Fri2,
                      V_Para_End_Time_Fri2,
                      V_Para_TotalHoursFri2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;



	END IF; /* Transporation - End   */

	/*=============================*/


	/*====== Set Assisgment Generated Fag ========*/

	UPDATE SchStudentMandates
		SET  AssignmentGeneratedFL = '1'
	WHERE Id = p_mandate_id ;


END $$

	DELIMITER ;	  

 