<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

	$form_data = json_decode(file_get_contents('php://input'));

	$Id = $form_data->{'Id'};
	$ExtId = $form_data->{'ExtId'};
	$SearchId = $form_data->{'SearchId'};
	$StatusId = $form_data->{'StatusId'};
	$SchoolId = $form_data->{'SchoolId'};
	$SubSchoolTypeId = $form_data->{'SubSchoolTypeId'};

	$DateOfBirth = $form_data->{'DateOfBirth'};
	$FirstName = $form_data->{'FirstName'};
	$LastName = $form_data->{'LastName'};
	$MiddleInitial = $form_data->{'MiddleInitial'};
	$StreetAddress1 = $form_data->{'StreetAddress1'};
	$StreetAddress2 = $form_data->{'StreetAddress2'};
	$City = $form_data->{'City'};
	$State = $form_data->{'State'};
	$ZipCode = $form_data->{'ZipCode'};
	$MobilePhone = $form_data->{'MobilePhone'};
	$HomePhone = $form_data->{'HomePhone'};
	$GuardianName = $form_data->{'GuardianName'};

	$GuardianFirstName = $form_data->{'GuardianFirstName'};
	$GuardianLastName = $form_data->{'GuardianLastName'};

	$GuardianPhone = $form_data->{'GuardianPhone'};
	$GuardianEmail = $form_data->{'GuardianEmail'};
	$MedicalNeeds = $form_data->{'MedicalNeeds'};
	$Comments = $form_data->{'Comments'};
	$ReportGroupId = $form_data->{'ReportGroupId'};
	$OverrideSchoolName = $form_data->{'OverrideSchoolName'};
    $OverrideSchoolAddress1 = $form_data->{'OverrideSchoolAddress1'};
    $OverrideSchoolCity = $form_data->{'OverrideSchoolCity'};
    $OverrideSchoolState = $form_data->{'OverrideSchoolState'};
    $OverrideSchoolZipCode = $form_data->{'OverrideSchoolZipCode'};
    $OverrideSchoolContactName = $form_data->{'OverrideSchoolContactName'};
    $OverrideSchoolContactPhone = $form_data->{'OverrideSchoolContactPhone'};
    $OverrideDistrict = $form_data->{'OverrideDistrict'};
	$UserId  = $form_data->{'UserId'};	


	$StreetAddress1 = mysqli_real_escape_string($conn, $StreetAddress1); 

	$OverrideSchoolName = mysqli_real_escape_string($conn, $OverrideSchoolName); 
	$MedicalNeeds = mysqli_real_escape_string($conn, $MedicalNeeds); 
	$Comments = mysqli_real_escape_string($conn, $Comments); 


	if(is_numeric($Id) ) { 	
			
            $query ="UPDATE SchStudents 
					set ExtId =  '{$ExtId}', 
					SearchId =  '{$SearchId}',
					StatusId =  '{$StatusId}',
					SchoolId =  '{$SchoolId}',
					DateOfBirth =  '{$DateOfBirth}',
					FirstName =  '{$FirstName}',
					LastName =  '{$LastName}',
					MiddleInitial =  '{$MiddleInitial}',
					StreetAddress1 =  '{$StreetAddress1}',
					StreetAddress2 =  '{$StreetAddress2}',
					City =  '{$City}',
					State =  '{$State}',
					ZipCode =  '{$ZipCode}',
					MobilePhone =  '{$MobilePhone}',
					HomePhone =  '{$HomePhone}',
					
					GuardianName =  '{$GuardianName}',
					GuardianFirstName =  '{$GuardianFirstName}',
					GuardianLastName =  '{$GuardianLastName}',

					GuardianPhone =  '{$GuardianPhone}',
					GuardianEmail =  '{$GuardianEmail}',
					MedicalNeeds =  '{$MedicalNeeds}',
					Comments =  '{$Comments}',
					ReportGroupId =  '{$ReportGroupId}',
					
					OverrideSchoolName =  '{$OverrideSchoolName}',
					OverrideSchoolAddress1 =  '{$OverrideSchoolAddress1}',
					OverrideSchoolCity =  '{$OverrideSchoolCity}',
					OverrideSchoolState =  '{$OverrideSchoolState}',
					OverrideSchoolZipCode =  '{$OverrideSchoolZipCode}',
					OverrideSchoolContactName =  '{$OverrideSchoolContactName}',
					OverrideSchoolContactPhone =  '{$OverrideSchoolContactPhone}',
					OverrideDistrict =  '{$OverrideDistrict}',
					SubSchoolTypeId =  '{$SubSchoolTypeId}',
					
 					 
					
					UserId =  '{$UserId}',
					TransDate = NOW()
				where Id = '{$Id}' ";
                } else {
               $query ="INSERT IGNORE into SchStudents  
						(ExtId, 
						SearchId,
						StatusId,
						SchoolId,
						DateOfBirth,
						FirstName,
						LastName,
						MiddleInitial,
						StreetAddress1,
						StreetAddress2,
						City,
						State,
						ZipCode,
						MobilePhone,
						HomePhone,
						
						GuardianName,
						GuardianFirstName,
						GuardianLastName,
						
						GuardianPhone,
						GuardianEmail,
						MedicalNeeds,
						Comments,
						ReportGroupId, 
						
						OverrideSchoolName,
						OverrideSchoolAddress1,
						OverrideSchoolCity,
						OverrideSchoolState,
						OverrideSchoolZipCode,
						OverrideSchoolContactName,
						OverrideSchoolContactPhone,
						OverrideDistrict,

						UserId,
						TransDate )
				values 	('{$ExtId}',  
						'{$SearchId}',	
						'{$StatusId}',
						'{$SchoolId}',
						'{$DateOfBirth}',  
						'{$FirstName}',  
						'{$LastName}',
						'{$MiddleInitial}',
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}',
						'{$State}',
						'{$ZipCode}',
						'{$MobilePhone}',
						'{$HomePhone}',
						
						'{$GuardianName}',
						'{$GuardianFirstName}',
						'{$GuardianLastName}',

						'{$GuardianPhone}',
						'{$GuardianEmail}',
						'{$MedicalNeeds}',
						'{$Comments}',						
						'{$ReportGroupId}',

						'{$OverrideSchoolName}',
						'{$OverrideSchoolAddress1}',
						'{$OverrideSchoolCity}',
						'{$OverrideSchoolState}',
						'{$OverrideSchoolZipCode}',
						'{$OverrideSchoolContactName}',
						'{$OverrideSchoolContactPhone}',
						'{$OverrideDistrict}',
						
						'{$UserId}',
						NOW()  ) ";  
	}
                                    

	$ret = setData ($conn, $query);
	setDisConn($conn);



	// ============
    // Update "Current" Mandnates' School  

 	// $conn = getCon();


 
 	// $query = " UPDATE SchStudentMandates
 	// 			SET SchoolId = '{$SchoolId}',
	//                  UserId =  '{$UserId}', 
    //                  TransDate = NOW() 
    //            WHERE StudentId = '{$Id}'
    //           AND   curdate() between StartDate and EndDate
    //            AND   SchoolId != '{$SchoolId}'         

 	// ";


	// $ret = setData ($conn, $query);
	// setDisConn($conn);


    // ============= 

	// echo $ret;
	echo $query;


 
 
?>
