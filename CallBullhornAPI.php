<?php
 

	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


 require_once 'BullhornAPI.php';


$BullhornAPI = new BullhornAPI();

$search_candidate =  array(
			'first_name' => '<PERSON>',
			'last_name' => '<PERSON><PERSON><PERSON>',
			'email' => '',
			'is_deleted' => '0',
		);

$ret =  $BullhornAPI->candidateFind($search_candidate);

 

 echo ' ret: ',$ret; 



?> 