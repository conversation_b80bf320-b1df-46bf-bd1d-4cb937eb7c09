<?php 
	
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


  require_once("db_GetSetData.php");

  $conn = getCon();

  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  $DistrictIdList = $_GET['DistrictIdList'];
  $ServiceGroupIdList = $_GET['ServiceGroupIdList'];
  $ReportTypeFl = $_GET['ReportTypeFl'];
 
 

  $query = "call proc_getSchRNSchoolReportsTransactions ( '{$FromDate}', 
                                                          '{$ToDate}', 
                                                          '{$DistrictIdList}',
                                                          '{$ServiceGroupIdList}',
                                                          '{$ReportTypeFl}' 

                                                 ) "; 


  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;



  
  
?>

