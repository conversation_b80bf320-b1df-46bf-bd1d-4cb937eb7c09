  
<?php
    
/*
	//Portal Out Input File  
	//==========================================================
	$out_File = "../uploads/doe_billing_upload.txt";

	header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    readfile($out_File);
    
    unlink($out_File);    

    exit;
*/


    $filename = "../uploads/eWebToVPortalZipFile.zip";
    $downloaded_filename = "eWebToVPortalZipFile.zip";


    header('Content-Type: application/zip');
    header('Content-disposition: attachment; filename='.$downloaded_filename);
    header('Content-Length: ' . filesize($filename));
    ob_clean();
    flush();
    readfile($filename);

    exit;


?>