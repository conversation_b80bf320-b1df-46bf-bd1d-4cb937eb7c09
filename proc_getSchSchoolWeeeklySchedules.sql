	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchSchoolWeeeklySchedules$$

	CREATE PROCEDURE proc_getSchSchoolWeeeklySchedules (IN 	p_school_id INT, 
															p_payroll_week DATE
														)  

	BEGIN

		create temporary table tmp
		(
			 
		 		ScheduleId BIGINT, 
		 		ScheduleStatusId INT, 
		 		ScheduleStatusDesc VARCHAR(32),
		 		TextColor VARCHAR(32),
		  		BackgroundColor VARCHAR(32),
		 		ConfirmationNumber VARCHAR(32),
		 		ServiceDate VARCHAR(32), 
		 		StartTimeNum TIME,
		 		EndTimeNum TIME, 
		  		StartTime VARCHAR(12),
		 		EndTime VARCHAR(12),
				TotalHours DECIMAL(5,2), 
				HoursScheduled DECIMAL(5,2),  						
				WeekDay VARCHAR(5), 
				RegistrantId INT, 
				RegistrantName VARCHAR(48),   					
				PrimaryVendorFL CHAR(1),   					
				DistrictId INT,
				ServiceTypeId INT,
				ServiceTypeDesc VARCHAR(32),
				RegistrantTypeId INT,
				AssignmentId INT,
				LastMessage VARCHAR(128), 
				MessagesCount INT,
				UserName VARCHAR(96),  
				TransDate VARCHAR(16)	


		);



		INSERT INTO tmp

		SELECT   		a.Id AS ScheduleId, 
				 		ScheduleStatusId, 
				 		ScheduleStatusDesc,
				 		TextColor,
		 		 		BackgroundColor,
				
				 		a.ConfirmationNumber,
				 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
				 		StartTime as StartTimeNum,
		 		  		EndTime as EndTimeNum,
				 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
				 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						a.TotalHours, 
						0,
						a.WeekDay, 
						a.RegistrantId, 
						(SELECT COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
							FROM Registrants c, RegistrantTypes f
							WHERE a.Registrantid = c.Id
							AND b.RegistrantTypeId = f.Id) as RegistrantName,   
						
						COALESCE((SELECT h.PrimaryVendorFL
							FROM SchDistrictServiceDetails h
							WHERE d.DistrictId = h.DistrictId
							AND a.ServiceTypeId = h.ServiceTypeId),'0') as PrimaryVendorFL,   
						
						d.DistrictId,
						a.ServiceTypeId,
						b.ServiceTypeDesc,
						b.RegistrantTypeId,
						a.AssignmentId,
						'',
						0,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						a.TransDate


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchSchools d,
				Users e,
			    ScheduleStatuses g
		WHERE a.SchoolId = p_school_id 
			AND  a.PayrollWeek =  p_payroll_week  
			AND a.UserId = e.UserId
			AND a.StudentId = 0 
			AND ScheduleStatusId = g.Id	
			AND a.ServiceTypeId = b.Id	
			AND a.SchoolId = d.Id	;


		/* Set Total Scheduled Hours for PR Week*/
		/*================================*/
		Update  tmp a 
		   set HoursScheduled = COALESCE((select sum(TotalHours) 
		                             from WeeklyServices b
								Where a.Registrantid > 0  
								AND a.RegistrantId = b.RegistrantId	
	                            AND b.PayrollWeek = p_payroll_week 
								AND b.ScheduleStatusId > 5),'') ;
								 

		/* Set Last Message*/
		/*================================*/
	 
		Update  tmp a
		  Set LastMessage =  COALESCE(( SELECT Msg
				FROM WeeklyServicesMessages b
				WHERE b.Id = ( SELECT max( c.Id )
					FROM WeeklyServicesMessages c
					WHERE c.ScheduleId = a.ScheduleId )),'') ;
	 	
		/* Set Messages Count*/
		/*================================*/
	 
		Update  tmp a
		  Set MessagesCount =  ( SELECT COUNT(*)
				FROM WeeklyServicesMessages b			
					WHERE a.ScheduleId = b.ScheduleId ) ;
	
	 

		SELECT * FROM tmp;	


		drop temporary table if exists tmp;
		 
		
	END $$

	DELIMITER ;		
	 