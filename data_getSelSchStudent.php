
<?php 
	

	require_once("db_GetSetData.php");

	$conn = getCon();

	$StudentId = $_GET['StudentId'];


    $query = "SELECT  	Id as id, 
	                    Id,
						ExtId,
						SearchId,
						SubSchoolTypeId,
						COALESCE(SchoolId,'') as SchoolId,
						COALESCE((select SchoolName from SchSchools
						           where SchoolId = SchSchools.Id 
						),'') as SchoolNameDBN,

						StatusId,
						DATE_FORMAT( DateOfBirth, '%m-%d-%Y' ) as DateOfBirth,
						FirstName,
						LastName,
						CONCAT( trim( LastName) , ', ', trim(FirstName)) as StudentName,
						MiddleInitial,
						StreetAddress1,
						StreetAddress2,
						City ,
						State ,
						ZipCode ,
						COALESCE(MobilePhone,'') as MobilePhone,
						COALESCE(HomePhone,'') as HomePhone,
						GuardianName,
						GuardianFirstName,
						GuardianLastName,
						COALESCE(GuardianPhone,'') as GuardianPhone,
						GuardianEmail,
						MedicalNeeds,
						Comments,
						ReportGroupId,
					    OverrideSchoolName,
					    OverrideSchoolAddress1,
					    OverrideSchoolCity,	
					    OverrideSchoolState,
					    OverrideSchoolZipCode,
					    OverrideDistrict,
					    OverrideSchoolContactName,
					    OverrideSchoolContactPhone,
					    OverrideDistrict,
						UserId
										
									FROM SchStudents
								  where Id = '{$StudentId}'";	 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

 
  
?>
