  
<?php
    

/*
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);

*/

	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	//==============
	// Billing Contract Id

	$billing_contract_id = $_POST['BillingContractId'];
	if (!$billing_contract_id) {
		$billing_contract_id = '1';
	}	

	//==============
	// School Season Id

	// $school_season_id = $_POST['SchoolYearId'];
	$school_season_id =18;

	//===============

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
		$query = "SELECT SchoolSeasonStartDate, SchoolSeasonEndDate
			FROM SchSchoolYear			 
			WHERE Id = '{$school_season_id}'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$StartDate = $row['SchoolSeasonStartDate'];
		$EndDate = $row['SchoolSeasonEndDate'];

	}	
	 
	/* Upload New File 
	 =============================================*/
		

	$inputFileName = '../uploads/mandates_0909.csv';

  
   // if($ufile != none){ 
      
// 		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
// 		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
// 	} else {
// 		print "1:Error uploading extracted file. Please try again!!! "; 
	  
// 		echo  "{ success: error,  data: ".json_encode($file)."}";
// 	    Return ; 

// 	}
   
 
	$file_handle = fopen("../uploads/mandates_0909.csv", "r");
	
	ini_set("auto_detect_line_endings", true);

	$cnt = 0;	
	$linecount = 0;	

   while (($row = fgetcsv($file_handle)) !== FALSE) { // while - start

   			$cnt++;


			$m_rec = substr(($row[4]),0,1);

		//	if ($m_rec == '2') { // m_rec - Start
			if ((($m_rec == '1') || ($m_rec == '2') || ($m_rec == '8')) && (!strstr($row[18], 'Para - '))) { // m_rec/Non Para- Start

				/* Student External ID
				 ===================*/

				$student_ext_id = $row[4];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row[5];


				/* Student First Name
				 ===================*/

				$student_first_name = $row[6];


				/* Student DOB
				============================= */
				
     			$dob_str = date("Y-m-d", strtotime($row[11]));
			 	$diff = abs(strtotime($dob_str) - strtotime(date("Y-m-d")));
				$years = floor($diff / (365*60*60*24));


				$sub_school_type_id = 0;

				if ($years >= 11 && $years <= 13) {

					$sub_school_type_id = 1;

				}

				if ($years > 13) {

					$sub_school_type_id = 2;

				}



				/* Student School ID
				 ===================*/

				$school_ext_id = '%'.$row[12].'%';
				$mandate_school_id = $row[12];


				/* Student Service Type Desc
				 ================================*/

				$student_serv_type_desc = $row[18];

				//==================================
				// Get eWeb Service Type 
				//==================================
			 	
				$service_type_id = ''; 

				$query_ser_id = " SELECT  	Id as ServiceTypeId, 
									        SESISServiceDurType,
									        DOEServiceTypeIndId,
              								DOEServiceTypeGrpId,
              								SESISParaServiceTypeId  
						   FROM SchServiceTypes	
   						where SESISServiceTypeDesc  like '{$student_serv_type_desc}' LIMIT 1 	 
				";
				
				$result_ser_id = $connection->query ($query_ser_id);
				if (DB::isError($result)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
			    }

				while ($row_ser_id =& $result_ser_id->fetchRow (DB_FETCHMODE_ASSOC)) {
					$service_type_id = $row_ser_id['ServiceTypeId'];
					$service_dur_type = $row_ser_id['SESISServiceDurType'];
					$service_type_ind = $row_ser_id['DOEServiceTypeIndId'];
					$service_type_grp = $row_ser_id['DOEServiceTypeGrpId'];
					$sesis_para_service_type_id = $row_ser_id['SESISParaServiceTypeId'];


				}	



				/*================================*/

				/* Student Language
				 ================================*/

				$student_language = $row[20];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row[21];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row[22];


				/* Mandate Service Freq
				 ================================*/

				$mandate_serv_freq = $row[23];
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Duration
				 ================================*/

				$mandate_serv_dur = $row[24];
				$mandate_serv_dur_num = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);

				// /* Service Duration for PARAs
                //  ============================*/

				// if ($mandate_serv_dur_num == 100) { // Full Day

				// 	$mandate_serv_dur_num = 8;
				// 	$mandate_serv_dur_min = 8 * 60;
				// 	$para_fl = '1';

				// }


				// if ($mandate_serv_dur_num == 20) { // Full Day

				// 	$mandate_serv_dur_num = 1.5;
				// 	$mandate_serv_dur_min = 1.5 * 60;
				// 	$para_fl = '1';

				// }

				/*===============================*/

				/* Mandate Service Start Date
				============================= */
							
    			$start_date_str = date("Y-m-d", strtotime($row[34]));


				/* Mandate First Attend Date
				============================= */
							
    			$mandate_first_attend_date = date("Y-m-d", strtotime($row[36]));

    			if ( $mandate_first_attend_date == '1969-12-31') {

    				$mandate_first_attend_date = $start_date_str;

    			}

 

				/* Provider Name
				 ================================*/

				
				$mandate_provider_name = $row[28];


				// echo "Provider name: $mandate_provider_name<br>"; 

				//==================================
				// Get Provider (Registrant)  Id
				//==================================
			 	
				$registrant_id = '0'; 

				if ($mandate_provider_name) {


					$mandate_provider_name = $connection->escapeSimple($mandate_provider_name); 

					$query_reg_id = "SELECT COALESCE((SELECT Id from Registrants 
								  WHERE  UPPER(Concat(TRIM(FirstName), ' ', TRIM(LastName)))  LIKE UPPER('%{$mandate_provider_name}%') LIMIT 1),0)   as RegistrantId 
					";
					
					$result_reg_id = $connection->query ($query_reg_id);
					if (DB::isError($result)){
				                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
				    }

					while ($row_reg_id =& $result_reg_id->fetchRow (DB_FETCHMODE_ASSOC)) {
						$registrant_id = $row_reg_id['RegistrantId'];


					}	



				}


 

				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = trim($row["30"]);



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query5 = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result5 = $connection->query ($query5);

				if (DB::isError($result5)){
					die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result5->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT INTO SchStudents
						(ExtId, 
						 SearchId, 
						 SubSchoolTypeId,
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 	
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'{$sub_school_type_id}',
							'1',
							 COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),

							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}
				} // End 1	


			//================================ 
			//  Check if Mandate Already Exists  
			//================================ 
		 	

			
			if (strlen($mandate_first_attend_date) > 10) {

				$mandate_first_attend_date = substr($mandate_first_attend_date,0,10);	

			}

			$query4 = "SELECT 1 
						FROM SchStudentMandates a, SchServiceTypes b
					WHERE a.StudentExtId = '{$student_ext_id}'
				 	AND a.StartDate = '{$mandate_first_attend_date}'  
 				/*	AND b.ServiceTypeDesc like '{$student_serv_type_desc}'	*/

 				/*	AND a.StartDate = '{$EndDate}' */
 					AND b.SESISServiceTypeDesc like '{$student_serv_type_desc}'	
 					
 					AND a.SessionGrpSize = '{$mandate_grp_size}'
 					AND a.SessionFrequency = '{$mandate_serv_freq_num}'
                    AND a.ServiceTypeId = b.Id 
                /*    AND a.DOEFirstAttendDate = '{$mandate_first_attend_date}' */
                    AND a.DOEProvider = '{$mandate_provider_name}'  
                    AND a.StatusId = '1' 

                    ";
                    
			$result4 = $connection->query ($query4);

		 	
			if (DB::isError($result4)){
						die("Could not query the database:<br />$query4 ".DB::errorMessage($result));
			}			
		 	
			$mandate_exists =& $result4->numRows();			
		 
			

			//=============================
			// Terminate Existing Mandate
			//============================



 			//=======================
			// Add New Mandate
			//=======================
			

			if (($mandate_exists == 0) && ($mandate_status_desc != 'Terminated') && ($service_type_id))  { // Mandate not exists - Start
			 


			$query2 = "INSERT INTO SchStudentMandates
						(	
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId, 
							DOEFirstAttendDate,
							DOEProvider,
							DOESchoolId,
							BillingContractId,
							UserId,
							TransDate
						) 

						VALUES (

						 	'0',
							'{$student_ext_id}',
							COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId   like '%{$school_ext_id}%' LIMIT 1),0),
							'1',
							'{$service_type_id}',

							'{$registrant_id}',
							
							'{$mandate_first_attend_date}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN '{$service_type_ind}'
								ELSE '{$service_type_grp}'
							END,
							'{$mandate_first_attend_date}',
							'{$mandate_provider_name}',
							'{$mandate_school_id}',
							'{$billing_contract_id}',
							'{$user_id}',
							now()  

							)";
						
	
					echo "mand insert query: $query2<br>";
					
						//$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);
						$result2 = $connection->query ($query2);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			






				} //Add New Para Assignments - End

				/*===================================================================*

				} // Mandate not exists - End	        
		 
				/*=========================*/

			 	
			 
			} // m_rec - end



		} // while - end	


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result3)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result3));
			        }

				//========================================================
				// Updade Student Students School Info 
				//========================================================
				
				// $query4 = "UPDATE WeeklyServices a,
					        
				// 	        SchStudentMandates c 
				// 	  set   a.SchoolId = c.SchoolId 

				// 	where a.ScheduleStatusId > 6
				// 	and a.ServiceDate >= '2019-09-01' 
				// 	and a.MandateId = c.Id 
				// 	and a.SchoolId != c.SchoolId 
				// 	and c.SchoolId != '0'   "; 		
 			
			
				// $result4 = $connection->getAll($query4, DB_FETCHMODE_ASSOC);

			 //        if (DB::isError($result4)){
			 //            die("Could not query the database:<br />$query3 ".DB::errorMessage($result4));
			 //        }

				//========================================================
				// Updade School ID within Student Profile Info 
				//========================================================
				
				$query5 = " UPDATE SchStudents a, SchStudentMandates b
				     set   a.SchoolId = b.SchoolId
				    where a.Id =   b.StudentId   
				    and b.StatusId = '1'
				    and curdate() between b.StartDate and b.EndDate
				    and a.SchoolId != b.SchoolId
					a.StudentExtId = '{$student_ext_id}'
					          "; 		
 			
			
				$result5 = $connection->getAll($query5, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result4)){
			            die("Could not query the database:<br />$query5 ".DB::errorMessage($result5));
			        }




	$connection->disconnect();
 
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

?>