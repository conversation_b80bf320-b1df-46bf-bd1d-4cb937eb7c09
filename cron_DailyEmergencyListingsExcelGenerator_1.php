<?php

	include("excelwriter.inc.php");
	include("Excel.php");


	//$path = "/usr/share/php/libzend-framework-php";

	$path = "/var/www/gotham_new/data";
    set_include_path(get_include_path() . PATH_SEPARATOR . $path);


 	require_once('DB.php');
	include('db_login.php');

	//================================
	
	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 	


	/* Get Service Current Service Date
	 ===================================*/

	$ServiceDate = date('Y-m-d H:i:s');
	$dw = date( "w", strtotime( $ServiceDate));
	$ServiceDateFrm = date('m/d/Y');

	$fileName = '/var/www/gotham_new/uploads/gotham_emergency_list_'.$ServiceDate.'.xlsx';

	//$fileName = "../uploads/gotham_emergency_list1.xls";	


	$excel = new ExcelWriter($fileName);
	
	if($excel==false)	
	{
		echo $excel->error;
		die;
	}
	
	 


	$excel->writeRow();	
	$excel->writeRow();	
	
	/* Get List Of Registrants
	 ===================================*/
 
	$RegListHdr=array(
				
				
				"Registrant Name",
				"Phone Numbers"
				);
	
	$excel->writeLine($RegListHdr, array('text-align'=>'center', 'color'=> 'blue','font-size'=> '14px','font-weight'=> '700' ,'border'=> '1pt solid black'));
	

	$query ="SELECT DISTINCT
			CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName1, 
			CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers
		FROM 	ClientApprovedRegistrants a, 
			Registrants c,
			RegistrantTypes f
			 	   
			WHERE 	c.StatusId = '1' 
			AND 	a.RegistrantId = c.Id
			AND 	c.TypeId = f.Id
			AND 	a.Status = '1'
			Order By c.LastName, c.FirstName  "; 

	$result = $connection->query ($query);

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {

			//================================================
			$RegListTrans=array(

				$row['RegistrantName1'],
				$row['PhoneNumbers'],
			);
			
			$excel->writeLine($RegListTrans);
	 		
			
	}		
 
	/* Get Daily Schedule
	 ===================================*/


/*
	if ($dw != 6) {
		$we_date = strtotime('next Saturday', strtotime($ServiceDate));

	}   else { 
		
		$we_date = strtotime('Saturday', strtotime($ServiceDate));

	}

	$PayrollWeek =  date('Y-m-d', $we_date);	
*/

	$excel->writeRow();	
	$excel->writeRow();	
	
	/* Get List Of Registrants
	 ===================================*/

	$SchedListHdr=array(
				
				
				"Client Name",
				"Unit",
				"Status",
				"Start Time",
				"End Time",
				"Total Hours",
				"Registrant Name",
				"Last Message"

				);
	
	$excel->writeLine($SchedListHdr, array('text-align'=>'center', 'color'=> 'blue','font-size'=> '14px','font-weight'=> '700' ,'border'=> '1pt solid black'));

	$query1 = "Call  proc_ClientsDateRangeSchedules ('{$ServiceDate}','{$ServiceDate}') "; 
	$result1 = $connection->query ($query1);


	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {

			//================================================
			$SchedListTrans=array(

				$row1['ClientName'],
				$row1['UnitName'],
				$row1['ScheduleStatusDesc'],
				$row1['StartTime'],
				$row1['EndTime'],
				$row1['TotalHours'],
				$row1['RegistrantName'],
				$row1['LastMessage']

			);

				$excel->writeLine($SchedListTrans);
			
	}		


	$excel->close();
	$connection->disconnect();

	// E-mail Emergency Listing
	//============================

function mail_attachment($filename, $path, $mailto, $from_mail, $from_name, $replyto, 
                            $subject, $message) {
    	$file = $path.$filename;
	    $file_size = filesize($file);
	    $handle = fopen($file, "r");
	    $content = fread($handle, $file_size);
	    fclose($handle);
	    $content = chunk_split(base64_encode($content));
	    $uid = md5(uniqid(time()));
	    $header = "From: ".$from_name." <".$from_mail.">\r\n";
	    $header .= "Reply-To: ".$replyto."\r\n";
	    $header .= "MIME-Version: 1.0\r\n";
	    $header .= "Content-Type: multipart/mixed; boundary=\"".$uid."\"\r\n\r\n";
	    $header .= "This is a multi-part message in MIME format.\r\n";
	    $header .= "--".$uid."\r\n";
	    $header .= "Content-type:text/plain; charset=iso-8859-1\r\n";
	    $header .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
	    $header .= $message."\r\n\r\n";
	    $header .= "--".$uid."\r\n";
	    $header .= "Content-Type: application/octet-stream; name=\"".$filename."\"\r\n"; // use different content types here
	    $header .= "Content-Transfer-Encoding: base64\r\n";
	    $header .= "Content-Disposition: attachment; filename=\"".$filename."\"\r\n\r\n";
	    $header .= $content."\r\n\r\n";
	    $header .= "--".$uid."--";
    
 
	    if (mail($mailto, $subject, "", $header)) {
	        //echo "mail send ... OK"; // or use booleans here
	    } else {
	        echo "mail send ... ERROR!";
	    }
     
	}	

    $my_file = 'gotham_emergency_list_'.$ServiceDate.'.xls';

	//echo '</br>'.'$fileName/$my_file'.'</br>';
	//echo ($fileName).'</br>'.$my_file;

    //echo 'Emergency Listing was Generated/Emalied Successfully';

    $my_path = "/var/www/gotham_new/uploads/";
    $my_name = "eWebStaffing";
    $my_mail = "<EMAIL>";
    $my_replyto = "<EMAIL>";
    $my_subject = "Emergency Information Listing (Gotham Per Diem).";
    $my_message = "Emergency Information Listing (Gotham Per Diem)\r\nService Date: ".$ServiceDateFrm."\r\n\r\neWebStaffing";
	mail_attachment($my_file, $my_path, "<EMAIL>;<EMAIL>;<EMAIL>", $my_mail, $my_name, $my_replyto, $my_subject, $my_message);

     

	 
?>