<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

include("db_login.php");

// Get the current date
$date = date('Y-m-d');
 

//   current date
$fri_date_0 = $date;


// Add 1 weeks to the current date
$fri_date_1 = date('Y-m-d', strtotime('+1 week', strtotime($date)));

// Add 2 weeks to the current date
$fri_date_2 = date('Y-m-d', strtotime('+2 week', strtotime($date)));

// Add 3 weeks to the current date
$fri_date_3 = date('Y-m-d', strtotime('+3 week', strtotime($date)));



// Establish PDO connection
try {
    $dsn = "mysql:host=$db_hostname;dbname=$db_database;charset=utf8";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ];
    $pdo = new PDO($dsn, $db_username, $db_password, $options);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage(), 3, "error_log.log");
    die("Could not connect to the database.");
}

//=========================
// Student Assignments (Para)
//=========================
try {
    $query = "CALL proc_SchSchoolAssigmentsToWklyServicesConversion()";
    $stmt = $pdo->query($query);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchSchoolAssigmentsToWklyServicesConversion: " . $e->getMessage(), 3, "error_log.log");
}

//===================================
// School Assignments (RN) - Week 0 
//===================================
try {
    $query = "CALL proc_SchSchoolRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_0]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchSchoolRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//===================================
// School Assignments (RN) - Week 1 
//===================================
try {
    $query = "CALL proc_SchSchoolRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_1]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchSchoolRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//===================================
// School Assignments (RN) - Week 2 
//===================================
try {
    $query = "CALL proc_SchSchoolRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_2]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchSchoolRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//===================================
// School Assignments (RN) - Week 3 
//===================================
try {
    $query = "CALL proc_SchSchoolRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_3]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchSchoolRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);


//=================================
// Student Assignments (RN) Week 0
//=================================
try {
    $query = "CALL proc_SchStudentRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_0]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchStudentRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//=================================
// Student Assignments (RN) Week 1
//=================================
try {
    $query = "CALL proc_SchStudentRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_1]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchStudentRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//=================================
// Student Assignments (RN) Week 2
//=================================
try {
    $query = "CALL proc_SchStudentRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_2]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchStudentRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);

//=================================
// Student Assignments (RN) Week 3
//=================================
try {
    $query = "CALL proc_SchStudentRNAssignmentsToWklyServicesConversion_1(:fri_date)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([':fri_date' => $fri_date_3]);
    $result = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error executing proc_SchStudentRNAssignmentsToWklyServicesConversion_1: " . $e->getMessage(), 3, "error_log.log");
}

sleep(5);


//=========================
// Correct School Assignments
//=========================
try {
    $query = "UPDATE WeeklyServices a
              JOIN SchStudentAssignmentHeader b ON a.StudentId = b.StudentId
              SET a.AssignmentId = b.Id
              WHERE a.ScheduleStatusId >= 7
              AND a.MandateId = 0
              AND a.AssignmentId = 0
              AND a.ServiceDate >= '2024-07-01'
              AND a.ServiceDate BETWEEN b.StartDate AND b.EndDate";
              
    $stmt = $pdo->query($query);
} catch (PDOException $e) {
    error_log("Error updating WeeklyServices: " . $e->getMessage(), 3, "error_log.log");
}

// Close the connection
$pdo = null;

?>
