<?php 


require "ewDataHandler.php";  
  
$rcr_transaction = new dataHandler();  

$ScheduleId  =  $_POST['ScheduleId'];
$ScheduleStatusId = $_POST['ScheduleStatusId'];
$RivertToPending = $_POST['RivertToPending'];
$RegistrantId = $_POST['RegistrantId'];
$ClientId = $_POST['ClientId'];
$CancelReason = $_POST['CancelReason'];
$ServiceCancellationReasonId = $_POST['ServiceCancellationReasonId'];
$Msg = $_POST['Msg'];
$UserId = $_POST['UserId'];
$HighPriority = 0;

$RegistrantId1 = $RegistrantId; 


	// Revert to Pending Status
	//==========================
	 
	if ($RivertToPending) {
		$ScheduleStatusId = 0;
		$RegistrantId = 0;
	}  



// Cancelled by Registrant
//=================================
if ($ScheduleStatusId == '2') {
	
	// Revert to Pending Status
	//==========================

	$result1 = $rcr_transaction->setCancelledByRegistrant(
							$RegistrantId1,
							$ScheduleId, 
							$ServiceCancellationReasonId,
							$CancelReason,
							$UserId); 



}

// Cancelled by Client
//=================================
if ($ScheduleStatusId == '3') {
	//$RegistrantId = 0;
	$CancelledByClient = 1;


	$result = $rcr_transaction->setCancelledByClient(
							$ClientId,
							$ScheduleId, 
							$ServiceCancellationReasonId,
							$CancelReason,
							$UserId); 

}

// Cancelled by Coordinator
//=================================
if ($ScheduleStatusId == '4') {
	
	 
	$result = $rcr_transaction->setCancelledByCoordinator(
									$UserId,
									$ScheduleId, 
									$ServiceCancellationReasonId,
									$CancelReason,
									$UserId); 
	
}

// Change Client Schedule Status
//================================= 
$result = $rcr_transaction->setClientWklyScheduleStatus(
								$ScheduleId,  
								$ScheduleStatusId, 
								0, 
								0, 
								$RegistrantId, 
								$UserId	); 

// Add New Schedule Message
//================================= 
$result = $rcr_transaction->setClientWklyScheduleMsg(
								$ScheduleId, 
								$Msg,
								$HighPriority,	  
								$UserId); 



						
								
$rcr_transaction->disconnectDB (); 

//echo  '{ success: true };
echo $result1;

?>
