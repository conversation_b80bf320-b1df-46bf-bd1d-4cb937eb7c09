	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchSchoolboardStudentsDateRangeSchedules$$

	CREATE PROCEDURE proc_getSchSchoolboardStudentsDateRangeSchedules (IN p_from_date DATE,
																		  p_to_date DATE,
																		  p_para_therapy_type_id INT
														         	)  

	BEGIN

		create temporary table tmp
		(
			 
		 		ScheduleId BIGINT, 
		 		ScheduleStatusId INT, 
		 		ScheduleStatusDesc VARCHAR(32),
		 		TextColor VARCHAR(32),
		  		BackgroundColor VARCHAR(32),
		 		ConfirmationNumber VARCHAR(32),
		 		ServiceDate VARCHAR(32), 
		 		ServiceDateSort DATE, 

		 		StartTimeNum TIME,
		 		EndTimeNum TIME, 
		  		StartTime VARCHAR(12),
		 		EndTime VARCHAR(12),
				TotalHours DECIMAL(5,2), 
				WeekDay VARCHAR(5), 
				PayrollWeek DATE,
				SchoolId INT, 
				SchoolName VARCHAR(48),   	
				
				StudentId INT, 
				StudentName VARCHAR(48),   	
				StudentSearchId VARCHAR(48),
				MandateDesc VARCHAR(48),
				SessionGrpSize INT,								
				RegistrantId INT, 
				RegistrantName VARCHAR(48),   					
				PrimaryVendorFL CHAR(1),   					
				ServiceTypeId INT,
				ServiceTypeDesc VARCHAR(32),
				RegistrantTypeId INT,
				LastMessage VARCHAR(128), 
				MessagesCount INT,
				UserName VARCHAR(96),  
				TransDate VARCHAR(16)	


		);



		IF ((p_para_therapy_type_id = 23) || (p_para_therapy_type_id = 12)) THEN 



		INSERT INTO tmp

		SELECT   		a.Id AS ScheduleId, 
				 		ScheduleStatusId, 
				 		ScheduleStatusDesc,
				 		TextColor,
		 		 		BackgroundColor,
				
				 		a.ConfirmationNumber,
				 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
				 		ServiceDate as ServiceDateSort,
				 		StartTime as StartTimeNum,
		 		  		EndTime as EndTimeNum,
				 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
				 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						a.TotalHours, 
						a.WeekDay, 
						a.PayrollWeek,
						a.SchoolId,

						COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
							FROM SchSchools f, SchDistricts g
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = g.Id),''),   

						a.StudentId, 
						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ),  
						c.SearchId,

						(SELECT COALESCE(CONCAT( c.SessionFrequency , ' X ', c.SessionLength), '')
							FROM SchStudentMandates c
							WHERE a.MandateId = c.Id), 
					
						COALESCE(a.SessionGrpSize,''),

						a.RegistrantId, 
						COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
							FROM Registrants c, RegistrantTypes f
							WHERE a.Registrantid = c.Id
							AND b.RegistrantTypeId = f.Id),'') as RegistrantName,   
						
						COALESCE((SELECT h.PrimaryVendorFL
							FROM SchDistrictServiceDetails h, SchSchools f
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = h.DistrictId
							AND   a.ServiceTypeId = h.ServiceTypeId LIMIT 1),'0') as PrimaryVendorFL,   
						
						a.ServiceTypeId,
						b.ServiceTypeDesc,
						b.RegistrantTypeId,
						'',
						0,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						a.TransDate


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
				Users e,
			    ScheduleStatuses g,
			    Registrants h
		WHERE   a.ServiceDate between  p_from_date and p_to_date 
			AND a.UserId = e.UserId
			AND ScheduleStatusId = g.Id	
			AND a.ServiceTypeId = b.Id	
			AND b.ServiceCategoryId = 1
			AND a.StudentId = c.Id
			AND a.StudentId != 0
			AND a.RegistrantId = h.Id
			AND h.TypeId = p_para_therapy_type_id  

				;

		ELSE		

		INSERT INTO tmp

		SELECT   		a.Id AS ScheduleId, 
				 		ScheduleStatusId, 
				 		ScheduleStatusDesc,
				 		TextColor,
		 		 		BackgroundColor,
				
				 		a.ConfirmationNumber,
				 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
				 		ServiceDate as ServiceDateSort,
				 		StartTime as StartTimeNum,
		 		  		EndTime as EndTimeNum,
				 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
				 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						a.TotalHours, 
						a.WeekDay, 
						a.PayrollWeek,
						a.SchoolId,

						COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
							FROM SchSchools f, SchDistricts g
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = g.Id),''),   

						a.StudentId, 
						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ),  
						c.SearchId,

						(SELECT COALESCE(CONCAT( c.SessionFrequency , ' X ', c.SessionLength), '')
							FROM SchStudentMandates c
							WHERE a.MandateId = c.Id), 
					
						COALESCE(a.SessionGrpSize,''),

						a.RegistrantId, 
						COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
							FROM Registrants c, RegistrantTypes f
							WHERE a.Registrantid = c.Id
							AND b.RegistrantTypeId = f.Id),'') as RegistrantName,   
						
						COALESCE((SELECT h.PrimaryVendorFL
							FROM SchDistrictServiceDetails h, SchSchools f
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = h.DistrictId
							AND   a.ServiceTypeId = h.ServiceTypeId LIMIT 1),'0') as PrimaryVendorFL,   
						
						a.ServiceTypeId,
						b.ServiceTypeDesc,
						b.RegistrantTypeId,
						'',
						0,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						a.TransDate


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
				Users e,
			    ScheduleStatuses g,
			    Registrants h
		WHERE   a.ServiceDate between  p_from_date and p_to_date 
			AND a.UserId = e.UserId
			AND ScheduleStatusId = g.Id	
			AND a.ServiceTypeId = b.Id	
			AND b.ServiceCategoryId = 1
			AND a.StudentId = c.Id
			AND a.StudentId != 0
			AND a.RegistrantId = h.Id
			AND h.TypeId not in (12,23)   

				;
		END IF;

								 

		/* Set Last Message*/
		/*================================*/
	 
		Update  tmp a
		  Set LastMessage =  COALESCE(( SELECT Msg
				FROM WeeklyServicesMessages b
				WHERE b.Id = ( SELECT max( c.Id )
					FROM WeeklyServicesMessages c
					WHERE c.ScheduleId = a.ScheduleId )),'') ;
	 	
		/* Set Messages Count*/
		/*================================*/
	 
		Update  tmp a
		  Set MessagesCount =  ( SELECT COUNT(*)
				FROM WeeklyServicesMessages b			
					WHERE a.ScheduleId = b.ScheduleId ) ;
	
	 

		SELECT * FROM tmp
		ORDER BY StudentName, ServiceDateSort, StartTimeNum
		;	


		drop temporary table if exists tmp;
		 
		
	END $$

	DELIMITER ;		
	 