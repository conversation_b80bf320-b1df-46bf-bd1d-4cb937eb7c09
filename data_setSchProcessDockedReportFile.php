<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

require("db_login.php");

$Filename = $_GET['Filename'];
$charset = 'utf8mb4';

// Check if Filename is provided
if (!$Filename) {
    echo "Error: Filename is not provided.";
    exit;
}

$dsn = "mysql:host=$db_hostname;dbname=$db_database;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
];

try {
    $pdo = new PDO($dsn, $db_username, $db_password, $options);
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
    exit;
}

$filePath = '../rn_reports/' . $Filename;

// Validate file extension
if (pathinfo($filePath, PATHINFO_EXTENSION) !== 'csv') {
    echo "Error: The file is not a CSV.";
    exit;
}

// Check if file exists
if (!file_exists($filePath)) {
    echo "Error: File does not exist.";
    exit;
}

if (($handle = fopen($filePath, "r")) !== FALSE) {
    $header = fgetcsv($handle);

    if (stripos($header[0], "Vendor") === FALSE) {
        echo "Error: The header in Column 'A' does not contain the word 'Vendor'.";
        fclose($handle);
        exit;
    }

    $stmt = $pdo->prepare("INSERT IGNORE INTO SchRNDockedHours
        (ConfirmationNumber, SchoolId, ServiceDate, ServiceTypeId, DockedHours, SetupHours, DockedHoursComments, DockedHoursSchoolId, DockedHoursSchoolName)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
        $conf_number = $data[1];
        $school_dbn = $data[2];
        $school_name = $data[3];
        $school_id = getSchoolId($pdo, $school_dbn);
        $coverage_type = $data[4];

        // Map coverage type to service type ID using switch statement
        $service_type_id = '0'; // Default value
        switch ($coverage_type) {
            case 'School Coverage':
                $service_type_id = '39';
                break;
            case 'After School':
                $service_type_id = '40';
                break;
            case 'Trip':
                $service_type_id = '41';
                break;
            case 'Special Events':
                $service_type_id = '42';
                break;
            case '1:1':
            case 'Transportation':
                $service_type_id = '43';
                break;
        }

        $nurse_name = $data[5];
        $service_date = date('Y-m-d', strtotime($data[6]));
        $docked_hours = $data[7];
        $setup_hours = $data[10];
        $docked_hours_comments = $data[11];

        try {
            $res = $stmt->execute([
                $conf_number,
                $school_id,
                $service_date,
                $service_type_id,
                $docked_hours,
                $setup_hours,
                $docked_hours_comments,
                $school_dbn,
                $school_name
            ]);
        } catch (PDOException $e) {
            echo "Insert failed: " . $e->getMessage();
            fclose($handle);
            exit;
        }

        echo "Insert result: $res<br>";
    }

    fclose($handle);
    unlink($filePath);
}

function getSchoolId($pdo, $school_dbn) {
    $stmt = $pdo->prepare("SELECT Id as SchoolId FROM SchSchools WHERE SesisSchoolId = ?");
    $stmt->execute([$school_dbn]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row ? $row['SchoolId'] : '';
}
?>
