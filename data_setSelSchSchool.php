<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

	$Id = $_POST['Id'];
	$SubSchoolTypeId = $_POST['SubSchoolTypeId'];  
	$ExtId = $_POST['ExtId'];
	$SearchId = $_POST['SearchId'];
	$DistrictId = $_POST['DistrictId'];
	$StatusId = $_POST['StatusId'];
	$SchoolName = $_POST['SchoolName'];
	$SchoolNameDBN = $_POST['SchoolNameDBN'];

	$SchoolTypeId = $_POST['SchoolTypeId'];
	$StreetAddress1 = $_POST['StreetAddress1'];
	$City = $_POST['City'];
	$State = $_POST['State'];
	$ZipCode = $_POST['ZipCode'];

	$LiaisonFirstName = $_POST['LiaisonFirstName'];
	$LiaisonLastName = $_POST['LiaisonLastName'];
	$OfficePhone = $_POST['OfficePhone'];
	$MobilePhone = $_POST['MobilePhone'];
	$Fax = $_POST['Fax'];
	$Email = $_POST['Email'];
	
	$LiaisonFirstName2 = $_POST['LiaisonFirstName2'];
	$LiaisonLastName2 = $_POST['LiaisonLastName2'];
	$OfficePhone2 = $_POST['OfficePhone2'];
	$MobilePhone2 = $_POST['MobilePhone2'];
	$Fax2 = $_POST['Fax2'];
	$Email2 = $_POST['Email2'];

	$Comments = $_POST['Comments'];
	$SesisSchoolId = $_POST['SesisSchoolId'];

	$RNSchoolTypeId = $_POST['RNSchoolTypeId'];

	$UserId = $_POST['UserId'];

	$SchoolName = mysqli_real_escape_string($conn, $SchoolName); 
	$SchoolNameDBN = mysqli_real_escape_string($conn, $SchoolNameDBN); 

	$LiaisonFirstName = mysqli_real_escape_string($conn,$LiaisonFirstName); 
	$LiaisonLastName = mysqli_real_escape_string($conn,$LiaisonLastName); 
	$LiaisonFirstName2 = mysqli_real_escape_string($conn,$LiaisonFirstName2); 
	$LiaisonLastName2 = mysqli_real_escape_string($conn,$LiaisonLastName2); 
	$Comments = mysqli_real_escape_string($conn,$Comments); 

	$StreetAddress1 = mysqli_real_escape_string($conn,$StreetAddress1); 
 

      $query = "UPDATE SchSchools a, SchSubSchools b
						set a.ExtId =  '{$ExtId}', 
						a.DistrictId =  '{$DistrictId}',
						a.StatusId =  '{$StatusId}',
						a.SchoolName =  '{$SchoolNameDBN}',
						b.SchoolName =  '{$SchoolName}',
						a.SchoolTypeId =  '{$SchoolTypeId}',
						b.StreetAddress1	 =  '{$StreetAddress1}',
						b.City	 =  '{$City}',
						b.State	 =  '{$State}',
						b.ZipCode	 =  '{$ZipCode}',

						b.LiaisonFirstName =  '{$LiaisonFirstName}',
						b.LiaisonLastName	 =  '{$LiaisonLastName}',
						b.OfficePhone	 =  '{$OfficePhone}',
						b.MobilePhone	 =  '{$MobilePhone}',
						b.Fax	 =  '{$Fax}',
						b.Email	 =  '{$Email}',

						b.LiaisonFirstName2 =  '{$LiaisonFirstName2}',
						b.LiaisonLastName2	 =  '{$LiaisonLastName2}',
						b.OfficePhone2	 =  '{$OfficePhone2}',
						b.MobilePhone2	 =  '{$MobilePhone2}',
						b.Fax2	 =  '{$Fax2}',
						b.Email2	 =  '{$Email2}',
						a.SesisSchoolId =  '{$SesisSchoolId}',
						a.RNSchoolTypeId =  '{$RNSchoolTypeId}',
						b.Comments =  '{$Comments}',
						b.UserId =  '{$UserId}',
						b.TransDate = NOW()
					where a.Id = '{$Id}' 
					and   a.Id = b.SchoolId
                    and   b.SubSchoolTypeId = '{$SubSchoolTypeId}'  "

					;

	$ret =  setData ($conn, $query);   			
	setDisConn($conn);

	echo $ret;


 

?>
