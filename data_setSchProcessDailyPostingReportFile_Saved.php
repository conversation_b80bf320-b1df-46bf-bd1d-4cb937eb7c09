<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

require("db_login.php");

$charset = 'utf8mb4';

try {
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("
        SELECT 
            PostConfirmationNumber,
            PostServiceDate,
            PostLiaison,
            PostDBNNumber,
            PostSchoolName,
            PostSchoolName,
            PostStartDateNeeded,
            PostEndDateNeeded,
            PostCoverageType,
            CASE PostCoverageType
                WHEN 'School Coverage' THEN 1
                WHEN 'After School' THEN 2
                WHEN 'Trip' THEN 3
                WHEN 'Special Events' THEN 4
            END AS PostServiceTypeGroupList,
            CASE PostCoverageType
                WHEN 'School Coverage' THEN 39
                WHEN 'After School' THEN 40
                WHEN 'Trip' THEN 41
                WHEN 'Special Events' THEN 42
            END AS ServiceTypeId,

            PostCoverageReason,
            PostHours,
            PostAgencyNurse,
            PostRNNumber      
        FROM SchRNDailyPostingTransactions
        where PostServiceDate = '2025-04-11'
  
         
    ");
    $stmt->execute();

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $data_1) {
        $conf_number = $data_1['PostConfirmationNumber'];
        $liaison_name = $data_1['PostLiaison'];
        $service_date = $data_1['PostServiceDate'];
        $sesis_school_dbn = $data_1['PostDBNNumber'];
        $school_name = $data_1['PostSchoolName'];
        
        // $school_id = getSchoolId($pdo, $sesis_school_dbn);
        $total_hours = $data_1['PostHours'];
        $coverage_type = $data_1['PostCoverageType'];
        $service_type_id = $data_1['ServiceTypeId'];
   
        $nurse_name = $data_1['PostAgencyNurse'];

        $nurse_number = $data_1['PostRNNumber'];

        $nurse_name_arr = explode(",", $nurse_name);
        $nurse_last_name = isset($nurse_name_arr[0]) ? trim($nurse_name_arr[0]) : '';
        $nurse_first_name = isset($nurse_name_arr[1]) ? trim($nurse_name_arr[1]) : '';
        $nurse_full_name = $nurse_first_name . ' ' . $nurse_last_name;

        $data_2 = getMatchingSessionInfo($pdo, $conf_number, $service_date);

        var_dump($data_2);
        

        $posting_comments = '';

        if ($data_2) {
            $posting_comments .= compareFields($data_2['ConfirmationNumber'], $conf_number, $conf_number, 'eWeb Conf. Number', 'D.Posting Conf. Number');
            $posting_comments .= compareFields($data_2['TotalHours'], $total_hours, $total_hours, 'eWeb Hours', 'D.Posting Hours');
            $posting_comments .= compareFields(strtoupper($data_2['Liaison_Name']), strtoupper($liaison_name), $liaison_name, 'eWeb Liaison', 'D.Posting Liaison');
            $posting_comments .= compareFields($data_2['School_DBN'], $sesis_school_dbn, $sesis_school_dbn, 'eWeb School DBN', 'D.Posting School DBN');
            $posting_comments .= compareFields($data_2['ServiceTypeDesc'], $coverage_type, $coverage_type, 'eWeb Coverage Type', 'D.Posting Coverage Type');
            // $posting_comments .= compareFields(strtoupper($data_2['RN_LastName']), strtoupper($nurse_last_name), $nurse_full_name, 'eWeb Nurse Name', 'D.Posting Nurse Name');
            $posting_comments .= compareFields($data_2['RN_License_Number'], $nurse_number, $nurse_number, 'eWeb RN License Number', 'D.Posting RN License Number');

            $posting_id = $data_2['Posting_Id'];

            // echo "posting_comments: $posting_comments<br>";

            $posting_status_id = $posting_comments == '' ? '1' : '2';

            setPostingStatus($pdo, $posting_id, $posting_status_id, $posting_comments);
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}

 

function getMatchingSessionInfo($pdo, $conf_number, $service_date)
{
    $stmt = $pdo->prepare("
        SELECT 
            a.Id as 'Posting_Id',
            a.TotalHours,
            a.ConfirmationNumber,
            b.FirstName AS 'RN_FirstName',
            b.LastName AS 'RN_LastName',
            b.RNLicenseNumber AS 'RN_License_Number',
            CONCAT(b.FirstName, ' ', b.LastName) as 'RN_Name',
            c.SesisSchoolId as 'School_DBN',
            h.ServiceTypeDesc,
            g.FirstName AS 'Liaison_FirstName',
            g.LastName AS 'Liaison_LastName',
            CONCAT(g.FirstName, ' ', g.LastName) as 'Liaison_Name' 
        FROM
            WeeklyServices a
        LEFT JOIN
            Registrants b ON a.RegistrantId = b.Id
        LEFT JOIN
            SchSchools c ON a.SchoolId = c.Id
        JOIN
            SchServiceTypes h ON a.ServiceTypeId = h.Id
        LEFT JOIN
            SchRNDistrictServiceLiaisons e ON e.DistrictId = c.DistrictId
            AND e.RNSchoolTypeId = c.RNSchoolTypeId
            AND e.ServiceTypeId = a.ServiceTypeId
        LEFT JOIN
            SchRNSchoolLiaisons g ON e.RNLiaisonId = g.Id
        WHERE
            a.ConfirmationNumber = ?
            AND a.ServiceDate = ?
            AND a.ScheduleStatusId >= 7
    ");
    $stmt->execute([$conf_number, $service_date]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}


function setPostingStatus($pdo, $posting_id, $posting_status_id, $posting_comments) {
    
    echo "posting_id: $posting_id<br>";

    try {
        // 1. INSERT IGNORE to insert the row if it does not exist
        $insertStmt = $pdo->prepare("
            INSERT IGNORE INTO SchRNTransactionPostings
                (ScheduleId, PostingStatusId, PostingStatusComments, UserId)
            VALUES
                (?, ?, ?, 0)
        ");
        
        $insertStmt->execute([$posting_id, $posting_status_id, $posting_comments]);

        // Check if a row was inserted
        if ($insertStmt->rowCount() == 0) {
            // Row already exists, so proceed with the UPDATE statement
            echo "Insert ignored, proceeding to update.<br>";
            
            // 2. UPDATE to update the record if PostingStatusId != 1
            $updateStmt = $pdo->prepare("
                UPDATE SchRNTransactionPostings
                SET
                    PostingStatusId = ?,
                    PostingStatusComments = ?
                WHERE
                    ScheduleId = ? AND PostingStatusId != 1
            ");
            
            $updateRet = $updateStmt->execute([$posting_status_id, $posting_comments, $posting_id]);

            if ($updateRet) {
                echo "Update succeeded.<br>";
            } else {
                echo "Update failed.<br>";
            }
        } else {
            // Row was inserted successfully
            echo "Insert succeeded, no need for update.<br>";
        }

    } catch (PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
}



function compareFields($field1, $field2, $original_field, $label1, $label2)
{
    if (is_array($field1) || is_array($field2)) {
        return '';
    }
    return $field1 != $field2 ? " $label1: {$field1} $label2: {$original_field};" : '';
}
?>
