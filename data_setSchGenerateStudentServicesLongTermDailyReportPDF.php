<?php

 
	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php'); 

	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	$logo_image = 'doelogo.jpg';
	
	$GLOBALS['Logo'] = $logo_image;
	 
	$GLOBALS['report_date'] = $argv[1];
	$GLOBALS['DistrictId'] = $argv[2];
	$GLOBALS['Email'] = $argv[3];

	$system_date = new DateTime($GLOBALS['report_date']);

	$GLOBALS['report_date_frm'] = $system_date->format('m/d/Y');



	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "SELECT CompanyName
				FROM Company ";
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['CompanyName'] = $row['CompanyName'];
	}

 
	 
class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		
		
		//echo 'Y: '.$this->GetY().'</b>';
		//echo 'X: '.$this->GetX().'</b>';


		$this->SetY(10.00125);
		$this->SetX(10.00125);


		$this->Image($GLOBALS['Logo'],20,8,50);


	 	
		$this->Ln(2);
		$this->Cell(70,4,'',0,0,'L');

		$this->SetFont('Arial','B',18);
		$this->Cell(100,6,'NEW YORK DEPARTMENT OF',0,1,'L');
		$this->Cell(70,4,'',0,0,'L');
		$this->Cell(100,6,'HEALTH AND MENTAL HYGIENE',0,1,'L');

		$this->SetFont('Times','B',14);

		$this->Ln(30);
		$this->Cell(40,6,'REPORT TYPE: STUDENTS 1:1 (RN) DAILY SERVICES',0,1,'L');

		$this->Ln(10);
		$this->Cell(40,6,'Date: '.$GLOBALS['report_date_frm'],0,1,'L');
		$this->Cell(40,6,'Company: '.$GLOBALS['CompanyName'],0,1,'L');



		$this->Ln(10);
		//Set Table Header
		$this->SetFont('Times','B',10);
		$this->Cell(20,5,'District',1,0,'C');
		$this->Cell(30,5,'School',1,0,'C');
		$this->Cell(25,5,"Today's Nurse",1,0,'C');
		$this->Cell(25,5,'L.T. Nurse',1,0,'C');
		$this->Cell(25,5,'Student',1,0,'C');
		$this->Cell(25,5,'Student ID',1,0,'C');
		$this->Cell(20,5,'Trip Loc.',1,0,'C');
		$this->Cell(20,5,'Conf. #',1,0,'L');
		$this->Cell(20,5,'Placement',1,0,'L');
		$this->Cell(20,5,'Term',1,0,'L');
		$this->Cell(20,5,'Comments',1,1,'C');
		$this->Ln(1);
 

		$y = $this->GetY();

	
		$GLOBALS['y'] = $y;


	
		$this->Ln(1);
	 	

		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-25);
		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	$pdf->SetFont('Arial','B',9);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
    

    	$query1 = "SELECT DISTINCT	
						ReportGroupId
				FROM WeeklyServices a,  SchStudents c  
					WHERE ServiceTypeId = '2'
					AND a.StudentId = c.Id
					AND ServiceDate = '{$GLOBALS['report_date']}' ";
 

		 $result1 = $connection->query ($query1);


		while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) { // Report Groups - Start

			$ReportGroupId = $row1['ReportGroupId'];


			$query = "SELECT 	
								DistrictName ,     
								 
								SchoolName, 
								 
								(SELECT COALESCE(CONCAT( trim( d.LastName) , ', ', trim( d.FirstName)  ), '')
													FROM Registrants d  
													WHERE a.Registrantid = d.Id
													) as RegistrantName,  	
								(SELECT COALESCE(CONCAT( trim( d.LastName) , ', ', trim( d.FirstName) ), '')
													FROM Registrants d,
														SchStudentAssignmentHeader g,
														SchStudentAssignmentDetails h
													  
													WHERE a.StudentId = g.StudentId
													AND a.ServiceTypeId = g.ServiceTypeId 
													AND g.Id = h.AssignmentId
													AND h.WeekDayId = dayofweek('{$sql_today}') 	
													AND h.Registrantid = d.Id

													) as LongTermRegistrantName, 
								CONCAT( trim( f.LastName) , ', ', trim(f.FirstName)) as StudentName,
								f.ExtId as StudentExternalId,
								CASE a.ScheduleStatusId 
									WHEN '7' THEN 'Secured'
								ELSE 'Unsecured'
								END AS ServiceStatusDesc,
								BoroughName,
								SchoolTypeDesc
							FROM WeeklyServices a,
								 SchDistricts b,
								 SchSchools c,
								 SchBoroughs d,
								 SchSchoolTypes e,
								 SchStudents f		
							WHERE  a.ServiceTypeId = '2'
							AND ServiceDate = '{$GLOBALS['report_date']}'
							/*AND ReportGroupId = '{$ReportGroupId}'*/
							AND c.DistrictId = b.Id
							AND a.SchoolId = c.Id 
							AND b.BoroughId = d.Id
							AND a.StudentId = f.Id
							AND c.SchoolTypeId = e.id";
			
			$result = $connection->query ($query);

				
			while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) { // Students - Start

			for ($i=0; $i < 5; $i++) { 
					# code...

				if ($i == 4) {

					
					$pdf->AddPage();

				} 


				// Line 1  - Start 

				$pos_y = $GLOBALS['y'] + 1;

				// District
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(10);
				$pdf->MultiCell(20,5,$row['DistrictName'],'L','C',0);
				
				$GLOBALS['y']=$pdf->GetY();
						
				// School
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(30);
				$pdf->MultiCell(30,5,$row['SchoolName'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Today's Nurse
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(60);
				$pdf->MultiCell(25,5,$row['RegistrantName'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Long Term Nurse
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(85);
				$pdf->MultiCell(25,5,$row['LongTermRegistrantName'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Student
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(110);
				$pdf->MultiCell(25,5,$row['StudentName'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Student ID
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(135);
				$pdf->MultiCell(25,5,$row['StudentExternalId'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y; 
				}

				$pdf->SetY($pos_y);
				$pdf->SetX(160);

				$pdf->MultiCell(20,5,'','L','C',0);
				

				$pdf->SetY($pos_y);
				$pdf->SetX(180);

				$pdf->MultiCell(20,5,'','L','C',0);


				$pdf->SetY($pos_y);
				$pdf->SetX(200);

				$pdf->MultiCell(20,5,'','L','C',0);


				$pdf->SetY($pos_y);
				$pdf->SetX(220);

				$pdf->MultiCell(20,5,'','L','C',0);


				$pdf->SetY($pos_y);
				$pdf->SetX(240);

				$pdf->MultiCell(20,5,'','LR','C',0);

			/*	
				$pdf->Cell(20,5,'',1,'L','L');
				$pdf->Cell(20,5,'',1,'L','L');
				$pdf->Cell(20,5,'',1,'L','L');
				$pdf->Cell(20,5,'',1,'LR','C');
			*/


				$pdf->Line(10, $y+1 , 260, $GLOBALS['y']+1);





				$pdf->Ln(1);

		}	
		


			} 	// Students - End

	
		}	// Report Groups - End	 
	   

	//+++++++++++++++++++++++++++++++++++++++++++++++++

// email stuff (change data below)
$to = $GLOBALS['Email']; 
$from = "<EMAIL>"; 
$subject = "Students Rn - 1to1 - Daily Report"; 
$message = '<p>Attached to this email is the report for Students - 1to1 - Report</p>';

// a random hash will be necessary to send mixed content
$separator = md5(time());

// carriage return type (we use a PHP end of line constant)
$eol = PHP_EOL;

// attachment name
$filename = 'DailyStudentRN-1to1-Report.pdf';

// encode data (puts attachment in proper format)
$pdfdoc = $pdf->Output("", "S");
$attachment = chunk_split(base64_encode($pdfdoc));

// main header
$headers  = "From: ".$from.$eol;
$headers .= "MIME-Version: 1.0".$eol; 
$headers .= "Content-Type: multipart/mixed; boundary=\"".$separator."\"";

// no more headers after this, we start the body! //

$body = "--".$separator.$eol;
$body .= "Content-Transfer-Encoding: 7bit".$eol.$eol;
$body .= "Students Rn - 1to1 - Daily Report.".$eol;

// message
$body .= "--".$separator.$eol;
$body .= "Content-Type: text/html; charset=\"iso-8859-1\"".$eol;
$body .= "Content-Transfer-Encoding: 8bit".$eol.$eol;
$body .= $message.$eol;

// attachment
$body .= "--".$separator.$eol;
$body .= "Content-Type: application/octet-stream; name=\"".$filename."\"".$eol; 
$body .= "Content-Transfer-Encoding: base64".$eol;
$body .= "Content-Disposition: attachment".$eol.$eol;
$body .= $attachment.$eol;
$body .= "--".$separator."--";

// send message


mail($to, $subject, $body, $headers);


	 
	
	//$pdf->Output();
	 
	$connection->disconnect();

	 	
?>
