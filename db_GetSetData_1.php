<?php

	ini_set("memory_limit","-1");

	function getCon() {

			include 'db_login.php';

			$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
			// Check connection
			if (mysqli_connect_errno())
			  {
			  die( "Could not query the database: " . mysqli_connect_error());
			  }


			  return $conn; 

	} 


	function getData ($conn, $query) {


		$result =  mysqli_query($conn, $query) or die
		("Error in Selecting " . mysqli_error($conn));

	 	
		$rows = array();
		while ($row = $result->fetch_all()) {
		    $rows[] = $row;
		}
	 	
	    mysqli_free_result($result);

	    return   "{ success: true,  data: ".json_encode($rows)."}";

	}	


	function setData ($conn, $query) {


		$result =  mysqli_query($conn, $query) or die
		("Error in Updating " . mysqli_error($conn));



	    mysqli_free_result($result);

	    return  $result;



	}	


	function setDisConn ($conn) {

		mysqli_close($conn);


	}	




?>
