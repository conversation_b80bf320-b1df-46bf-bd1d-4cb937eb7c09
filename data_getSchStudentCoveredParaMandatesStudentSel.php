<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

  $SchoolSeasonId = $_GET['SchoolSeasonId'];
 
    $query = "	SELECT   DISTINCT 
                                  b.LastName,
                                  b.FirstName,
                                  StudentId  as id,
                                  StudentId, 
                                  CONCAT( trim( b.LastName) , ', ', trim(b.FirstName), ' (',StudentExtId, ')' ) as StudentName  
  
                FROM    SchStudentMandates a, 
                        SchStudents b,
                        SchSchoolYear c,
                        SchServiceTypes f 
                     WHERE f.RegistrantTypeId = 23   
                    AND a.StudentId = b.Id  
                    -- AND a.AssignmentGeneratedFL = 1  
                    AND a.ServiceTypeId = f.Id
                    AND a.RegistrantId != 0
                    AND a.Statusid = '1'
                    AND c.Id = '{$SchoolSeasonId}'
                    AND StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate

         Order By   b.LastName,   b.FirstName      ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

  
?>

 