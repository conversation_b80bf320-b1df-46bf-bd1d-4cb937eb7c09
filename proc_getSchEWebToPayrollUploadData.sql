			DELIMITER $$

			DROP PROCEDURE IF EXISTS proc_getSchEWebToPayrollUploadData$$

			CREATE PROCEDURE proc_getSchEWebToPayrollUploadData (IN 	p_payroll_batch_number INT,
		                                                                p_billing_contract_id INT,
		                                                                p_billing_contract_name VARCHAR(54)
		 													    )	    
		 														    

			BEGIN


			IF (p_billing_contract_id = 1) THEN /* Billing Type - Contract  */	

				create temporary table tmp

				SELECT   distinct          a.RegistrantId,
		                CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName,
		                HrId,
		                DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
		                a.TotalHours, 
		                
		        /*        CONCAT(TRIM(m.SchoolName),' (',d.DistrictName,')') as   SchoolName,  
		                CONCAT(TRIM(c.SchoolName),' (',d.DistrictName,')') as   SchoolNameDBN, */
						CONCAT(TRIM(c.SchoolName),' (',d.District<PERSON>ame,') ',c.ExtId ) as SchoolNameDBN,

						/*c.SchoolName as SchoolNameDBN,*/

		                d.DistrictName,
	 
		                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
		                ServiceTypeDesc,
		                p_billing_contract_name as BillingContractName,
		               n.BoroughCode  
		        FROM  WeeklyServices a, 
		              Registrants b,
		              SchSchools c,
		              SchDistricts d,
		              SchStudents e,
		              SchServiceTypes g,
		              SchBoroughs n 
		        WHERE PayrollBatchNumber =   p_payroll_batch_number
		        and   a.RegistrantId = b.id
		        and   a.SchoolId = c.Id 
		        AND   a.ServiceTypeId = g.Id
		        AND   c.DistrictId = d.Id
		        AND   a.StudentId = e.Id
                AND   d.BoroughId = n.Id
	            
	             
		        
		        ;
			 			
			 			
				SELECT RegistrantName,
				       HrId,
				       ServiceDate,
				       StartTime,
				       EndTime,
 				       SchoolNameDBN,
				       DistrictName,
				       ServiceTypeDesc,
				       BillingContractName,
				       SUM(TotalHours) as TotalHours,
				       BoroughCode
				FROM   tmp
				GROUP BY RegistrantName,
				      	 HrId,
				         ServiceDate,
				         SchoolNameDBN ;   	

			ELSE 
			
				create temporary table tmp1

				SELECT   distinct          a.RegistrantId,
		                CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName,
		                HrId,
		                DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
		                a.TotalHours, 
		                CONCAT( trim( e.LastName) , ', ', trim( e.FirstName)) as SchoolNameDBN,
		                /*'' as SchoolNameDBN, */
		                d.DistrictName,

		                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
		                ServiceTypeDesc,
		                p_billing_contract_name as BillingContractName,
		                n.BoroughCode 
		        FROM  WeeklyServices a, 
		              Registrants b,
		              SchSchools c,
		              SchDistricts d,
		              SchStudents  e,
		              SchServiceTypes g,
		               SchBoroughs n  
		        WHERE PayrollBatchNumber =   p_payroll_batch_number 
		        and   a.RegistrantId = b.id
		        and   a.StudentId = e.Id	
		        and   a.SchoolId = c.Id 
		        AND   c.DistrictId = d.Id
				AND   a.ServiceTypeId = g.Id
				 AND   d.BoroughId = n.Id
				 ;
			 			
			 			
				SELECT RegistrantName,
				       HrId,
				       ServiceDate,
				       StartTime,
				       EndTime,
				    /*   SchoolName, */
				       SchoolNameDBN,
				       DistrictName,
				       ServiceTypeDesc,
				       BillingContractName,
				       SUM(TotalHours) as TotalHours,
				       BoroughCode
				FROM   tmp1
				GROUP BY RegistrantName,
				      	 HrId,
				         ServiceDate,
				         SchoolNameDBN  ;   	



			END IF;  	         	
			 
				drop temporary table if exists tmp;
				drop temporary table if exists tmp1;
				 
				
			END $$

			DELIMITER ;		
			 