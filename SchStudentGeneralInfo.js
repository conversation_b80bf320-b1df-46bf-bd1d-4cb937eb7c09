Ext.define('EW.view.sch_student.SchStudentGeneralInfo', {
    extend: 'Ext.form.Panel',
    alias : 'widget.schstudentgeneralinfo',
	store: 'sch_student.selSchStudent',
	title: '',
	autoScroll: true,
	width: 700,
	maxWidth : 700,
	height: 300,
	minHeight: 300,
	margin: '5 0 0 5',
	padding: 5, 
	autoScroll: true,
	frame: true,	
	
	fieldDefaults: {
		labelAlign: 'left',
		labelWidth: 90,
		msgTarget: 'under'
	},
	
    initComponent: function() {
 		
		this.items = [
 
                    {
                        xtype: 'hiddenfield',
                        name : 'UserId'
                    },

                    {
                        xtype: 'hiddenfield',
                        name : 'SearchId'
                    },

					{   
						xtype: 'fieldcontainer',
						anchor: '100%',
						fieldLabel: 'ID',
						labelWidth: 50,
						labelStyle: 'font-size:100%;',
						layout: 'hbox',

						items: [
							{
								xtype: 'textfield',
								//fieldLabel: 'ID',
								//flex: 1,
								width: 50,
								style: 'font-size:90%;',
								labelStyle: 'font-size:90%;',
								name : 'Id',
								fieldStyle: 'background-color: #ddd; background-image: none; font-size: 90%;',
								readOnly : true
 							},
							{
								xtype: 'displayfield',
								value: ' ',
								margin: '0 0 0 3',
								width: 10	
							},
							{
								xtype: 'combo',
								labelWidth: 40,
								itemId: 'student-status',
								width: 120,
								name: 'StatusId',
								fieldStyle: 'font-size: 100%;',
								style: 'font-size:90%;',
								loadMask: true,
								editable: false,
								fieldLabel: 'Status',
								labelStyle: 'font-size:100%;',
								store: 'Status',
								mode: 'local',
								displayField: 'StatusDesc',
								valueField: 'id'
							},
							{
								xtype: 'displayfield',
								value: ' ',
								margin: '0 0 0 3',
								width: 10	
							},
							{
								xtype: 'textfield',
								fieldLabel: 'Ext ID',
								//flex: 1,
								width: 120,
								//style: 'font-size:90%;',
								labelWidth: 50,
								name : 'ExtId',
								fieldStyle: 'font-size: 90%;',
								labelStyle: 'font-size:100%;',
							},
							{
								xtype: 'displayfield',
								value: ' ',
								margin: '0 0 0 3',
								width: 10	
							},
							{
								xtype: 'textfield',
								fieldLabel: 'DOB',
								labelWidth: 50,
								width: 150,
								style: 'font-size:90%;',
								labelStyle: 'font-size:90%;',
								name : 'DateOfBirth',
								fieldStyle: 'background-color: #ddd; background-image: none; font-size: 90%;',
								readOnly : true
 							},
							
						]
						
                    },

					{   
						xtype: 'fieldset',
						//flex: 1,
						flex: 1,
						//layout: 'hbox',
						title: '<b>School</b>',
						defaults: {
							anchor: '100%',
							hideEmptyLabel: false
						},
						items: [
							{
								xtype: 'combobox',
								labelWidth: 50,
								itemId: 'student-school',
								flex: 1,
								name: 'SchoolId',
								fieldStyle: 'font-size: 100%;',
								style: 'font-size:90%;',
								emptyText: 'Select School', 
								selectOnFocus: true,
								autoSelect: true,
								forceSelection: true,
								triggerAction:'all',
								typeAhead: true , 
								fieldLabel: 'School',
								labelStyle: 'font-size:100%;',
								store: 'sch_school.SchSchools',
								queryMode: 'local',
								displayField: 'SchoolNameDisp',
								valueField: 'id',
								//allowBlank: false

							},

						]
					},	

					{   
						xtype: 'fieldcontainer',
						anchor: '100%',
						//style: 'font-size:90%;',
						//labelStyle: 'font-size:100%;',						
						//fieldLabel: 'Name (F,L,MD)',
						//fieldStyle: 'font-size: 90%;',
						layout: 'hbox',

						items: [
							
							{   
							    xtype: 'fieldset',
								//flex: 1,
								flex: 1,
								layout: 'hbox',
								title: '<b>Student Name</b>',
								defaults: {
									anchor: '100%',
									hideEmptyLabel: false
									},
								items: [{
										 xtype: 'textfield',
										name : 'FirstName',
										fieldLabel: 'First',
										fieldStyle: 'font-size: 90%;',
										labelWidth: 50,
										afterLabelTextTpl: '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>',
										labelStyle: 'font-size:100%;',
										flex: 2,
										allowBlank: false
									},
									{
										xtype: 'displayfield',
										value: ' ',
										margin: '0 0 0 3',
										width: 10	
									},
									
									{   
										xtype: 'textfield',
										name : 'LastName',
										fieldLabel: 'Last',
										fieldStyle: 'font-size: 90%;',
										labelStyle: 'font-size:100%;',
										labelWidth: 50,
										afterLabelTextTpl: '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>',
										flex: 2,
										allowBlank: false
									},
									{
										xtype: 'displayfield',
										value: ' ',
										margin: '0 0 0 3',
										width: 10	
									},
									
									{   
										xtype: 'textfield',
										name : 'MiddleInitial',
										fieldLabel: 'M.I.',
										labelStyle: 'font-size:90%;',
										fieldStyle: 'font-size: 90%;',
										labelWidth: 50,
										maxLength: 1,
										flex: 1
                                        //width: 50 										
										//anchor: '40%' 
									}]		
							}]
					},
					{   
						xtype: 'fieldset',
						//flex: 1,
						flex: 1,
						//layout: 'hbox',
						title: '<b>Address</b>',
						defaults: {
							anchor: '100%',
							hideEmptyLabel: false
						},
									
						items: [ // level 1 - Start
						{		
							xtype: 'fieldcontainer',
							anchor: '100%',
							layout: 'hbox',
							labelWidth: 0,
							
							items: [		
								
								{ 
									xtype: 'textfield',
									name : 'StreetAddress1',
									fieldLabel: 'Street',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									flex: 2,
									labelWidth: 50,
									afterLabelTextTpl: '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>',
									allowBlank: false
								},
								{
									xtype: 'displayfield',
									value: ' ',
									margin: '0 0 0 3',
									width: 10	
								},
								{
									xtype: 'textfield',
									name : 'StreetAddress2',
									labelWidth: 50,
									fieldLabel: 'Apt',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									flex: 1
									//fieldLabel: 'Address 2'
								}
							]	
						},
						{   
						xtype: 'fieldcontainer',
						anchor: '100%',
						labelWidth: 0,
						layout: 'hbox',

						items: [{
								xtype: 'textfield',
								name : 'City',
								fieldLabel: 'City',
								labelStyle: 'font-size:90%;',
								fieldStyle: 'font-size: 90%;',
								flex: 2,
								labelWidth: 50,
								afterLabelTextTpl: '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>',
								allowBlank: false
							},
							{
								xtype: 'displayfield',
								value: ' ',
								margin: '0 0 0 3',
								width: 10	
							},
							
							{
								xtype: 'statelist',
								id: 'sch-student-state',
								name : 'State',
								labelStyle: 'font-size:90%;',
								fieldStyle: 'font-size: 90%;',
								flex: 1.5,
								labelWidth: 50,
								//anchor: '50%',
								fieldLabel: 'State'
							},
							{
								xtype: 'displayfield',
								value: ' ',
								margin: '0 0 0 3',
								width: 10	
							},
							
							{  
								xtype: 'textfield',
								name : 'ZipCode',
								labelWidth: 50,
								fieldLabel: 'Zipcode',
								labelStyle: 'font-size:90%;',
								fieldStyle: 'font-size: 90%;',
								flex: 1.5,
								minLength: 5,
								maxLength: 10,
								afterLabelTextTpl: '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>',
								allowBlank: false		
							}]		
							
						},     						
						
						]	//Level 1 - End	
					},
					{
						xtype: 'fieldcontainer',
						anchor: '100%',
						layout: 'hbox',
						labelWidth: 0,
							
						items: [	                 

               
						{
							xtype: 'fieldset',
							flex: 4,
							layout: 'hbox',
							title: '<b>Phone</b>',
							defaults: {
								anchor: '100%',
								hideEmptyLabel: false
							},

							items: [{
									xtype: 'textfield',
									name : 'MobilePhone',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									fieldLabel: 'Mobile',
									//flex: 1,
									width: 150,
									labelWidth: 50,
									vtype: 'phone' 
								},
								{
									xtype: 'displayfield',
									value: ' ',
									margin: '0 0 0 3',
									width: 10	
								},
							
								{   
									xtype: 'textfield',
									name : 'HomePhone',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									fieldLabel: 'Home',
									//flex: 1,
									width: 150,
									labelWidth: 50,
									vtype: 'phone' 
								}	
							]		
								
						},
						{
									xtype: 'displayfield',
									value: ' ',
									margin: '0 0 0 3',
									width: 10	
						},
						{   
								xtype: 'fieldset',
								flex: 1,
								title: '<b>Rpt Group Id</b>',
								defaults: {
									anchor: '100%',
									hideEmptyLabel: false
								},
											
									items: [
									{	xtype: 'numberfield',
										name : 'ReportGroupId',
										labelStyle: 'font-size:90%;',
										fieldStyle: 'font-size: 90%;',
										minValue: 0,
										maxValue: 20,
										//width: 50,
										labelWidth: 0,							
									} 
									
								]
						},
					]	
					},
					{   
						xtype: 'fieldset',
						flex: 1,
						title: '<b>Guardian</b>',
						defaults: {
							anchor: '100%',
							hideEmptyLabel: false
						},
									
						items: [ // level 1 - Start
						{		
							xtype: 'fieldcontainer',
							anchor: '100%',
							layout: 'hbox',
							labelWidth: 0,
							
							items: [		
								
								{ 
									xtype: 'textfield',
									name : 'GuardianName',
									fieldLabel: 'Name',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									flex: 2,
									labelWidth: 50
								},
								{
									xtype: 'displayfield',
									value: ' ',
									margin: '0 0 0 3',
									width: 10	
								},
								{
									xtype: 'textfield',
									name : 'GuardianPhone',
									labelWidth: 50,
									fieldLabel: 'Phone',
									labelStyle: 'font-size:90%;',
									fieldStyle: 'font-size: 90%;',
									flex: 1
									//fieldLabel: 'Address 2'
								}
							]	
						},
						{   
						xtype: 'fieldcontainer',
						anchor: '100%',
						labelWidth: 0,
						layout: 'hbox',

						items: [{
								xtype: 'textfield',
								name : 'GuardianEmail',
								fieldLabel: 'Email',
								labelStyle: 'font-size:90%;',
								fieldStyle: 'font-size: 90%;',
								flex: 1,
								labelWidth: 50,
								vtype: 'email' 
							}
							]		
							
						},     						
						
						]	//Level 1 - End	
					},      
					{
						xtype: 'fieldcontainer',
						anchor: '100%',
						layout: 'hbox',
						labelWidth: 0,
							
						items: [

							{   
								xtype: 'fieldset',
								flex: 1,
								title: '<b>Medical Needs</b>',
								defaults: {
									anchor: '100%',
									hideEmptyLabel: false
								},
											
									items: [ // level 1 - Start
									{		
										xtype: 'textarea',
										name : 'MedicalNeeds',
										labelStyle: 'font-size:90%;',
										fieldStyle: 'font-size: 90%;',
										flex: 1,
										height: 60,
										labelWidth: 0
							
									} 
									
								]
							},
							{
									xtype: 'displayfield',
									value: ' ',
									margin: '0 0 0 3',
									width: 5	
								},
							{   
								xtype: 'fieldset',
								flex: 1,
								title: '<b>Comments</b>',
								defaults: {
									anchor: '100%',
									hideEmptyLabel: false
								},
											
									items: [ // level 1 - Start
									{		
										xtype: 'textarea',
										name : 'Comments',
										labelStyle: 'font-size:90%;',
										fieldStyle: 'font-size: 90%;',
										flex: 1,
										height: 60,
										labelWidth: 0
							
									} 
									
								]
							},
							
							
						]
					}		
        ];

		var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';    

		
		this.dockedItems = [{
            dock: 'bottom',
            xtype: 'toolbar',
			//ui: 'footer',
            items: [
			
			{
                text: 'Save',
				iconCls: 'icon-disk',
                action: 'save'
            },
			           {
                text: 'Add New Student',
				iconCls: 'icon-add',
                action: 'add'
            }

			
			]
        },
		];
        this.callParent(arguments);
    }
});