<?php 
	

  require_once("db_GetSetData.php");

  $conn = getCon();

  $ServiceTypeId = $_GET['ServiceTypeId'];

     //    $query ="SELECT a.RegistrantId as id,
     //    				a.RegistrantId,
		// 	       		CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName  
			 
		// 	 FROM 	ClientApprovedRegistrantsNonRest a,
		// 	        Registrants c,
		// 	        RegistrantTypes f,
		// 	        Clients b,
		// 	        SchServiceTypes d
			 
		// 	where   a.RegistrantId = c.Id 
		// 	AND   b.SchoolFL = '1'
		// 	AND   c.TypeId = f.Id 
		// 	AND   c.TypeId = d.RegistrantTypeId 
		// 	AND   d.Id = '{$ServiceTypeId}' 
		// 	ORDER BY c.LastName, c.FirstName  
            
     // "; 


       $query ="SELECT 
						    c.Id AS id,
						    c.Id AS RegistrantId,
						    CONCAT(TRIM(c.LastName),
						            ', ',
						            TRIM(c.FirstName),
						            ' (',
						            RegistrantTypeDesc,
						            ')') AS RegistrantName
						FROM
						    Registrants c,
						    RegistrantTypes f,
						    Clients b,
						    SchServiceTypes d
						WHERE
						    b.SchoolFL = '1' AND c.TypeId = f.Id
						        AND c.TypeId = d.RegistrantTypeId
						        AND d.Id = '{$ServiceTypeId}'
						        AND c.StatusId = '1'
						ORDER BY c.LastName , c.FirstName  
            
     "; 

	 $ret = getData ($conn, $query);
	  setDisConn($conn);

	  echo $ret;
	 

 
  
?>
