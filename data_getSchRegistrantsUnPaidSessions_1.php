<?php

	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


  require_once("db_login.php");

	// Database connection using PDO
	$dsn = "mysql:host=$db_hostname;dbname=$db_database;charset=utf8";
	$pdo = new PDO($dsn, $db_username, $db_password);
	$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

	try {
	    // Fetch parameters
	    $PayrollWeek = $_GET['PayrollWeek'];
	    $ParaTherapyTypeId = $_GET['ParaTherapyTypeId'];
	    $HrTypeId = $_GET['HrTypeId'];
	    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
	    $perPage = isset($_GET['perPage']) ? (int)$_GET['perPage'] : 100;
	    $offset = ($page - 1) * $perPage;

	    // Base query (common to all conditions)
	    $baseQuery = ""; // Common parts of your SQL query go here

	    // Conditional parts based on $ParaTherapyTypeId
	    if ($ParaTherapyTypeId == '23') {
	        $additionalCondition = "SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <=:PayrollWeek 
					                        AND   c.HrTypeId =  :HrTypeId
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId = '23'
					                        AND   a.ScheduleStatusId > 5
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-01-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
					                  	LIMIT :perPage OFFSET :offset
					                   "; 


					                  // Specific conditions for this case
	    } elseif ($ParaTherapyTypeId == '12') {
	        $additionalCondition = "SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <=:PayrollWeek 
					                        AND   c.HrTypeId =  :HrTypeId
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId != '23'
					                        AND   a.ScheduleStatusId > 5
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-01-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
					                  	LIMIT :perPage OFFSET :offset
					                  "; 
					                  	


					                  // Specific conditions for this case
	    } else {
	        $additionalCondition = "SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <=:PayrollWeek 
					                        AND   c.HrTypeId =  :HrTypeId
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId != '23'
					                        AND   a.ScheduleStatusId > 5
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-01-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
					                    LIMIT :perPage OFFSET :offset

					                     "; // Default conditions
	    }

	    // Combine the base query with the conditional parts
	    // $query = $baseQuery . $additionalCondition . " LIMIT :perPage OFFSET :offset";
	    $query = $baseQuery . $additionalCondition ;

	    // Prepare the SQL query
	    $stmt = $pdo->prepare($query);

	    // Bind the parameters
	    $stmt->bindParam(':PayrollWeek', $PayrollWeek, PDO::PARAM_STR);
	    $stmt->bindParam(':HrTypeId', $HrTypeId, PDO::PARAM_STR);
	    $stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);
	    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);

	    // Execute the query
	    $stmt->execute();

	    // Fetch the results
	    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

	    // Output the results
	    echo json_encode($results);

	} catch (PDOException $e) {
	    echo "Database error: " . $e->getMessage();
	} catch (Exception $e) {
	    echo "Error: " . $e->getMessage();
	}

?>
