<?php

    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);

    require_once("db_GetSetData.php");
    $conn = getCon();

    $query1 ="SELECT ApprovalEmail, ApprovalEmailPass FROM Company";
    $result1 =  mysqli_query($conn, $query1) or die("Error in Selecting " . mysqli_error($conn));

    while ($row1 = $result1->fetch_assoc()) {
        $username = $row1['ApprovalEmail'];
        $password = $row1['ApprovalEmailPass'];
    }
    setDisConn($conn);

    $hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
    $inbox = imap_open($hostname, $username, $password) or die('Cannot connect to Gmail: ' . imap_last_error());

    $emails = imap_search($inbox, 'UNSEEN');

    if ($emails) {
        rsort($emails);
        foreach ($emails as $email_number) {
            $overview = imap_fetch_overview($inbox, $email_number, 0);
            $subject = $overview[0]->subject;

            $message_plain = imap_fetchbody($inbox, $email_number, 1);

            // Check if the encoding property exists and then decode the email accordingly
            if (isset($overview[0]->encoding)) {
                switch ($overview[0]->encoding) {
                    case 1:  // 8bit encoding
                        $message_plain = imap_8bit($message_plain);
                        break;
                    case 2:  // binary encoding
                        $message_plain = imap_binary($message_plain);
                        break;
                    case 3:  // base64 encoding
                        $message_plain = imap_base64($message_plain);
                        break;
                    case 4:  // quoted-printable encoding
                        $message_plain = quoted_printable_decode($message_plain);
                        break;
                }
            }

            // echo "Subject: $subject\\n";
            // echo "Message: $message_plain\\n";
            // echo "-------------------------------\\n";
        
            $embeded_string = strstr($message_plain, '#s');

            echo 'embeded_string: '.$embeded_string.'</br>'; 
             
            $search_id = get_string_between($embeded_string, '#s', '$s');
            
            echo '$search_id: '.$search_id.'</br>'; 
  

         if ($search_id) { // search - start
  
            /* Generate Name for email copy uploaded file 
           =============================================*/
          $new_file_name =  generateRandomString();
          $new_file_path =  '../em/'.$new_file_name.'.eml';
          $db_new_file_name =  $new_file_name.'.eml';


   
            // Procoss RSA 7a Mandate - Start
            //============================




            $conn = getCon();

            $query ="UPDATE SchRsa7aFormSignatures 
                   SET  StatusId = '4',
                    ParentSignatureName =  'Verified via Email',  
                    ParentApprovalEmailFileName =   '{$db_new_file_name}',      
                    ParentSignatureTimeStamp = NOW()

                WHERE   SearchId = '{$search_id}'
                  ";

              echo 'RSA 7a  $query:'.$query.'</br>';      


            $result =  mysqli_query($conn, $query) or die
            ("Error in Selecting " . mysqli_error($conn));

            setDisConn($conn);





            echo '</br></br>Save email....</br></br>';  
            
            file_put_contents($new_file_path, $headers . "\n" . $message_plain);


 

        } // search - end


        }
    }

    imap_close($inbox);



    function get_string_between($string, $start, $end){
          $string = ' ' . $string;
          $ini = strpos($string, $start);
          if ($ini == 0) return '';
          $ini += strlen($start);
          $len = strpos($string, $end, $ini) - $ini;
          return substr($string, $ini, $len);
    }

   function generateRandomString($length = 15) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
          $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
   } 


?>
