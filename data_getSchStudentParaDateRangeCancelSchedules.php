<?php 

   require_once("db_GetSetData.php");

	$conn = getCon();

	$StudentId = $_GET['StudentId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


    $query = "	SELECT a.Id AS ScheduleId, 
						 		ScheduleStatusId, 
						 		ScheduleStatusDesc,
						 		TextColor,
				 		 		BackgroundColor,
								
						 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate, 
                                ServiceDate as ServiceDateSort,
						 		StartTime as StartTimeNum,
				 		  		EndTime as EndTimeNum,
						 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
								a.TotalHours, 
								a.WeekDay, 
								a.PayrollWeek,
								a.SchoolId,
								CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolName,						
	 							a.StudentId,
                                CONCAT(f.LastName, ', ', f.FirstName) as RegistrantName,	
								
								b.ServiceTypeDesc,  
								 
								CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) as UserName,
								 
								a.TransDate  
		 from WeeklyServices a,
		      SchServiceTypes b,
		      Registrants f,
		      SchDistricts c,
		      SchSchools d,
		      Users e,
		      SchStudents h,
		      ScheduleStatuses g,
		      SchSubSchools i 
		WHERE   a.StudentId = '{$StudentId}' 
		 				AND a.ServiceDate between  '$FromDate' and '$ToDate' 
					 	AND ScheduleStatusId = '7'   
					 	AND a.ServiceTypeId = b.Id
					 	AND ScheduleStatusId = g.Id	
					 	AND a.RegistrantId = f.Id	
					    AND f.TypeId = 23 
					    AND a.SchoolId = d.Id 
					    AND a.SchoolId = i.SchoolId
					    AND h.SubSchoolTypeId = i.SubSchoolTypeId 
					    AND d.DistrictId = c.Id
					    AND a.UserId = e.UserId
    					AND a.StudentId = h.Id 
                       ORDER BY ServiceDateSort, StartTimeNum	   
 	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	

/*
	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


	 
	$result = $rcr_transaction->getSchRegistrantDateRangeSchedules(	$RegistrantId, 
																	$FromDate,
																	$ToDate
																  );

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/
?>
