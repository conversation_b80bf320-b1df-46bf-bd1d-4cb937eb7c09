
<?php 
	
 
    require_once("db_GetSetData.php");

	$conn = getCon();	

	$FromDate  =  $_GET['FromDate'];
	$ToDate  =  $_GET['ToDate'];


    $query = "SELECT  	 c.Id as id,
    					 c.Id as RegistrantId,	
				         CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)) as RegistrantName 
 				 
				 from 	SchStudentParaUnBilledTransactions a, 
						WeeklyServices b,
				        Registrants c 
				         
				 where a.ScheduleId = b.Id
				 and   b.RegistrantId = c.Id
				 AND   a.StatusId = 0 
				 AND   b.ServiceDate between '{$FromDate}' and '{$ToDate}' "
				 ;

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 

  
?>

 