<?php


	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


	$clientId = "35dc1fe2-02d4-432f-a5d4-71e876f1c6b2";
	$responseType = "code";
	$action = "Login";
	$clientSecret = "qnCZuTw0ZCxGGSgVuOjzgI59";
	$username = "rcmhealthcare.api";
	$password = "Welcome123!";
	$redirectUri = "https://www.ewebstaffing.com/rcm/data/RedirectlBullhornAPI.php";




		// get Code
		// ===========

		$url_code = "https://auth-east.bullhornstaffing.com/oauth/authorize?client_id=" . urlencode($clientId) . "&response_type=" . urlencode($responseType) . "&action=" . urlencode($action) . "&username=" . urlencode($username) . "&password=" . urlencode($password) . "&redirect_uri=" . urlencode($redirectUri);

	    $code = '';

		// Initialize cURL
		$ch = curl_init();

		// Set cURL options
		curl_setopt($ch, CURLOPT_URL, $url_code);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

		// Execute the request
		$response = curl_exec($ch);

		// var_dump($response);

		// Check for errors
		if (curl_errno($ch)) {
		    echo "cURL error: " . curl_error($ch);
		} else {
		    // Decode the response

			
			$code = $response;
			// echo " code: $code<br><br>";
		}

		// Close the cURL session
		curl_close($ch);


		//=========
		// Get Token
		// ============

		$grantType = "authorization_code";
 
		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://auth.bullhornstaffing.com/oauth/token?grant_type='. $grantType. '&code=' . $code . '&client_id=' . $clientId .'&client_secret=' . $clientSecret .'&redirect_uri=' . $redirectUri,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		));

			$response1 = curl_exec($curl);

			$result1 = json_decode($response1, true);
			$access_token = $result1["access_token"];	

			// echo " access_token: $access_token<br><br>";


		//=============
		// Get REST URL
		// ============


		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://rest.bullhornstaffing.com/rest-services/login?version=2.0&access_token=' . $access_token,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		  // CURLOPT_HTTPHEADER => array(
		  //   'Cookie: BhRestToken=24058_7549830_631d563b-cc4b-48a5-892f-0d2759d0cde2'
		  // ),
		));

		$response2 = curl_exec($curl);


		$result2 = json_decode($response2, true);
		$BhRestToken = $result2["BhRestToken"];	
		$rest_url = $result2["restUrl"];	

		// echo " BhRestToken: $BhRestToken rest_ur:  $rest_url<br><br>";


        // ====================
        // Get Candidates data
 		//=====================   

		// $curl = curl_init();

		// $company_name = "DOE*";

		// curl_setopt_array($curl, array(
		// CURLOPT_URL =>   $rest_url.'search/Candidate?BhRestToken='.$BhRestToken . '&query=(companyName:'.$company_name .')&fields=id,companyName,submissions,phone,mobile,ssn,customInt10,occupation,specialties,employeeType,firstName,lastName,address,sendouts&start=0&count=50',
		//   CURLOPT_RETURNTRANSFER => true,	
		//   CURLOPT_ENCODING => '',
		//   CURLOPT_MAXREDIRS => 10,
		//   CURLOPT_TIMEOUT => 0,
		//   CURLOPT_FOLLOWLOCATION => true,
		//   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		//   CURLOPT_CUSTOMREQUEST => 'GET',
		// ));

		// $response3 = curl_exec($curl);

		// curl_close($curl);

		// // echo $response3;

		// $data = json_decode($response3, true);
		// //print_r ($result3["data"][0]) ;
 
		// foreach ($data['data'] as $row) {
		
		// 	if ($row['companyName'] == 'NYC DOE'){

		// 		// var_dump($row);
		// 		// echo '<br><br>';

		// 	    echo "companyName: " . $row['companyName'] . "<br>";
		// 	    echo " submissions: " . print_r($row['submissions']) . "<br>";
		// 	    echo " phone: " . $row['phone'] . "<br>";
		// 	    echo " mobile: " . $row['mobile'] . "<br>";
		// 	    echo " ssn: " . $row['ssn'] . "<br>";
		// 	    echo " HRID: " . $row['customInt10'] . "<br>";
		// 	    echo " occupation: " . $row['occupation'] . "<br>";
		// 	    echo " specialties: " . print_r($row['specialties']) . "<br>";
		// 	    echo " employeeType: " . $row['employeeType'] . "<br>";
		// 	    echo " firstName: " . $row['firstName'] . "<br>";
		// 	    echo " lastName: " . $row['lastName'] . "<br>";
		// 	    echo " address: " . print_r($row['address']) . "<br>";
			    
		// 	    $send_out_arr = $row['sendouts']; 
			    
		// 	    echo " # of sendouts: " . $send_out_arr['total'] . "<br>";

		// 	    echo " sendouts details: " . print_r($send_out_arr	) . "<br>";

		// 	    // echo " sendouts: " . print_r($send_out_arr) . "<br>";
		// 	    echo " ====================================<br>";

		// 	}

		// }

        // ====================
        // Get Recent Placements
 		//=====================   

		$curl = curl_init();

 
		curl_setopt_array($curl, array(
		CURLOPT_URL =>   $rest_url.'search/Placement?fields=id,candidate,jobOrder&query=dateAdded:[20230401%20TO%20*]&count=50',
		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));

		$response4 = curl_exec($curl);

		curl_close($curl);

		var_dump($response4);





?>

 