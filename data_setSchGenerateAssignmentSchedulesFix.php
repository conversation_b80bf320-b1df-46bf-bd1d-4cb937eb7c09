<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

/*
	$StartDate  =  $_POST['StartDate'];
	$EndDate  =  $_POST['EndDate'];
	$AssignmentId = $_POST['AssignmentId'];
	$UserId = $_POST['UserId'];
*/

	$AssignmentId = $_GET['AssignmentId'];
	$StartDate  =  $_GET['StartDate'];
	$EndDate  =  $_GET['EndDate'];


	//$AssignmentId = '9';
	//$StartDate  =  '2017-08-02';
	//$EndDate  =  '2017-08-06';
	/*======== Set End Date to Friday =======*/
	
	//$end_date_friday = new DateTime($EndDate);	
	//$datetime2  = $end_date_friday->modify('next friday');

 
	$datetime1 = date_create($StartDate);
	$datetime2 = date_create($EndDate);



	$interval = date_diff($datetime1, $datetime2);
	$days = $interval->format('%a');

	$ServiceDate = $StartDate;
	$ServiceDate  = date('Y-m-d', strtotime( $ServiceDate));	 
	$dw = date( "w", strtotime( $ServiceDate));
	if ($dw == 6) {
		$we_date = strtotime('Saturday', strtotime($ServiceDate));

	}   else { 
		
		$we_date = strtotime('next Saturday', strtotime($ServiceDate));

	}
	$PayrollWeek  = date('Y-m-d', $we_date);

	for ($x = 0; $x <= abs($days); $x++) {
    	
		if (($dw > 0) && (($dw < 6)) ) { // Mon through Fri - start 

			echo 'Service Date: '.$ServiceDate.' W.D.: '.$dw.' Payroll Week: '.$PayrollWeek.'</br>';  

		
			$query = "INSERT IGNORE INTO WeeklyServices
                   		 ( 
			              AssignmentId,
			              CLientId,
			              PayrollWeek,
			              StudentId,
			              ScheduleStatusId,
			              ServiceTypeId,
			              AssignmentTypeId, 
			              ConfirmationNumber,
			              RegistrantId,
			              ServiceDate,   
			              StartTime, 
			              EndTime,    
			              TotalHours , 
			              WeekDay,
			              SchoolId,
			              UserId,
			              TransDate 
			            ) 


					SELECT    a.Id as AssignmentId,
					          (SELECT Id  
					              FROM Clients
					            WHERE SchoolFL = '1' LIMIT 1),
					          '{$PayrollWeek}',
					          a.StudentId,
					          '7',
					          a.ServiceTypeId,
					          a.AssignmentTypeId,
					          a.ConfirmationNumber,
					          b.RegistrantId,
					          '{$ServiceDate}',
					          b.StartTime,
					          b.EndTime,
					          b.TotalHours,
					          d.WeekDay,
					          e.SchoolId,
					          '0',
					          NOW()
					          
					      FROM    SchStudentAssignmentHeader a,
					          SchStudentAssignmentDetails b,
					          Registrants c,
					          DaysOfWeek5 d,
					          SchStudents e
					      WHERE a.StatusId = '1'
					      AND  a.Id = '{$AssignmentId}' 
					      AND  b.WeekDayId = '{$dw}'
					      AND  a.Id = b.AssignmentId 
					      AND  b.RegistrantId = c.Id 
					      AND  b.WeekDayId = d.WeekDayId
					      AND  a.StudentId = e.Id     ";


					      $ret =  setData ($conn, $query);

		} // Mon through Fri - end 



 		
		/*==================*/

		$date  = strtotime("+1 day", strtotime( $ServiceDate));
		$ServiceDate  = date('Y-m-d', $date);	
		$dw = date( "w", strtotime( $ServiceDate));	
		if ($dw == 6) {
			$we_date = strtotime('Saturday', strtotime($ServiceDate));

		}   else { 
			
			$we_date = strtotime('next Saturday', strtotime($ServiceDate));

		}
		$PayrollWeek  = date('Y-m-d', $we_date);
		
	} 

 
								
 

setDisConn($conn);

echo $result;

?>
