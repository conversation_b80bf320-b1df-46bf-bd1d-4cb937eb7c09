<?php 
    
  

    require_once("db_GetSetData.php");
    $conn = getCon();

 
    $CallInDate = $_POST["CallInDate"];
    $CallInTimeId = $_POST["CallInTimeId"];
    $CallInTime = $_POST["CallInTime"];
    $CallInComments = $_POST['CallInComments'];
    $CallInStatus = $_POST["CallInStatus"];
    $ScheduledRNId = $_POST["ScheduledRNId"];
    $SchoolId = $_POST["SchoolId"];
    $UserId = $_POST['UserId'];
 
       
    if ($CallInTimeId) {

         $query = "UPDATE SchRNDailyCallInTimes       
             SET 
                 CallInTime     =  '{$CallInTime}',
                 CallInStatus   =  '{$CallInStatus}',
                 CallInComments =  '{$CallInComments}',

                 UserId = '{$UserId}'
           WHERE  Id =     '{$CallInTimeId}'
             ";


    } else {

         $query = "INSERT INTO SchRNDailyCallInTimes
                    ( 
                        
                        RegistrantId,
                        ServiceDate,
                        SchoolId,
                        CallInStatus,
                        CallInTime,
                        CallInComments,
                        UserId 
                    )
                    VALUES
                    ( 
                        '{$ScheduledRNId}',
                        '{$CallInDate}',
                        '{$SchoolId}',
                        '{$CallInStatus}',
                        '{$CallInTime}',
                        '{$CallInComments}',
                        '{$UserId}' 
                    )    
                ";


    }

   

        $ret =  setData ($conn, $query);        
 

    
  setDisConn($conn);
  // echo $ret;
  echo $query;
  
    
  
?>
 