<?php 
	

  require_once("db_GetSetData.php");

  $conn = getCon();

  // $ServiceDate = date('Y-m-d');
 $ServiceDate = $_GET['ServiceDate'];
  if (!$ServiceDate) {

    $ServiceDate = date('Y-m-d');


  }   

  $query = "SELECT a.Id as id,  
        CONCAT( trim( a.LastName) , ', ', trim( a.FirstName)) as RegistrantName 
        from Registrants a
        WHERE a.TypeId = 12
     and exists (
       select 1 from WeeklyServices b
             where a.Id = b.RegistrantId
             and b.ScheduleStatusId > 6
             and b.ServiceDate = '{$ServiceDate}' 
      )    
     Order bY a.LastName, a.FirstName
         "; 

       
  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  // echo $query;


?>

