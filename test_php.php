<?php
// Initial time in HH:MM:SS format
$initial_time = '12:30:00';

// Minutes to add
$minutes_to_add = 160.2;

// Convert minutes to seconds (1 minute = 60 seconds)
$seconds_to_add = $minutes_to_add * 60;

// Create a DateTime object from the initial time
$time = new DateTime($initial_time);

// Add the seconds to the time
$time->modify("+{$seconds_to_add} seconds");

// Display the updated time
echo $time->format('H:i:s');
?>
