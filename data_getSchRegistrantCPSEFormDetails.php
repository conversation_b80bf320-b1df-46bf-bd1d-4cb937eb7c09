<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
    $MandateId = $_GET['MandateId'];  
	$Year = $_GET['Year'];
	$Month = $_GET['Month'];
 

        $query = "  SELECT   DISTINCT DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                            a.ServiceDate as ServiceDateSort,
                            DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                            a.StartTime as StartTimeSort,
                            DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                            DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                            a.SessionGrpSize,
                        /*    
                            COALESCE((SELECT b.StatusId 
                                        FROM SchCPSEFormSignaturesDetails b
                                       WHERE a.Id = b.ScheduleId  LIMIT 1 
                            ),0) as CPSEDetailSignatuteStatus
                        */
                            'Undefined' as CPSEDetailSignatuteStatus
                        FROM    WeeklyServices a                   
                            Where RegistrantId= '{$RegistrantId}'
                            AND   MandateId = '{$MandateId}' 
                            AND   ScheduleStatusId = '8'
                            AND   YEAR(ServiceDate) = '{$Year}'
                            AND   MONTH(ServiceDate) = '{$Month}'                        
                    ORDER BY ServiceDateSort, StartTimeSort   ";


    


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

