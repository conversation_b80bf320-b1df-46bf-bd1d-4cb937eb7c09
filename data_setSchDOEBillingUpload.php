<?php
       
/* 
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
*/ 


    require_once('DB.php');
    include('db_login.php');
    

    $user_id = $_POST['UserId'];
    if (!$user_id) {
        $user_id = '1';
    }   

    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }


    //Portal Input Input File  
    //==========================================================
    $file_date = date('Y-m-d H:i:s').'-'.mt_rand();

    $inputFileName = '../uploads/doe_billing_upload_input_tr -'.$file_date.'.csv';

  
   if($ufile != none){ 
      
        $rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
        
    } else {
        
        $err_flag = '1';
        //print "Error uploading extracted file. Please try again!!! "; 
      
        $linecount = 0;
        echo  "{ success: true, transactions: '{$linecount}'}";

        Return ; 

    }
    
 

    $input_file_handle = fopen($inputFileName, "r");
  
    $outputFileName = '../uploads/doe_billing_upload_output_tr -'.$file_date.'.csv';
    $output_file_handle = fopen($outputFileName, "w");
     

    $out_File = '../uploads/doe_billing_upload_tr'.$file_date.'.txt';
    $fh = fopen($out_File, 'w') or die("can't open file");
    





   while (($row_xls = fgetcsv($input_file_handle)) !== FALSE) { // for foreach - start



            /* Check if Wrog file was selected 
             ====================================*/


                $cnt++;
        
        
                //=========================
                // Write Header
                //========================  
                
                if ($cnt == 1) {


                //$check_file = preg_replace('/[\x00-\x1F\x7F]/', '', $row_xls[0]);
                $row_xls[0] = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $row_xls[0]);
                //echo '$row_xls[0]: '.$row_xls[0].'</br>';     

                /* Check if Wrog file was selected 
                ====================================*/

                        if (($row_xls[0] != 'SRAP FISCAL YR') && ($row_xls[0] != 'SIAP FISCAL YR') && ($row_xls[0] != 'RSAP FISCAL YR')) {

                            $err_flag = '1';


                        } else {

                            fputcsv($output_file_handle, $row_xls);
                            $out_Line_str = implode("\t", $row_xls); 
                            $out_Line_str = $out_Line_str."\n";
                            fwrite($fh, $out_Line_str);


                            /* Write Heading 
                             ==================*/
                            $write_flag = '1';


                        }




                }


                if ($cnt != 1) { // cnt !=1 - srart  


                    //==============================
                    // Get Therapist Id
                    //==============================

                    $therapist_id = $row_xls[10];
                    $therapist_id = str_pad($therapist_id,9, '0', STR_PAD_LEFT);    

                    //==============================
                    // Get Student Id
                    //==============================
                    
                    $student_id = $row_xls[11];

                    //==============================
                    // Get Student Last Name 
                    //==============================

                    $student_last_name = $row_xls[13];


                    //==============================
                    // Get DOE Mand Freq 
                    //==============================

                    $doe_mand_freq = $row_xls[17];


                    //==============================
                    // Get DOE Mand Duration 
                    //==============================

                    $doe_mand_duration = $row_xls[19];


                    //==============================
                    // Get DOE Mand Group Size 
                    //==============================

                    $doe_mand_grp_size = $row_xls[20];





                    //==============================
                    // Get Service Code
                    //==============================

                    $doe_service_code = $row_xls[14];

                    $service_type_id = '0';
                    $group_fl = '0';
                    $para_service_type_id = '0';

                    /*=========  Get Group Service Type Id =========*/
                    

                    $query_grp = "SELECT Id as GroupServiceTypeId,
                                         SESISParaServiceTypeId  

                               FROM SchServiceTypes
                                WHERE DOEServiceTypeGrpId = '{$doe_service_code}'"; 
                    
                    $result_grp = $connection->query ($query_grp);
                    
                    

                    if ($result_grp->numRows() == 1) {  


                        $group_fl = '1'; // Group Mandate 

                        while ($row_grp =& $result_grp->fetchRow (DB_FETCHMODE_ASSOC)) {
                                $service_type_search = $row_grp['GroupServiceTypeId'];
                                $para_service_type_id = $row_grp['SESISParaServiceTypeId'];

                         }  
                    }    



                    if ($group_fl == '0') {




                        /*=========  Get Individual Service Type Id =========*/
                            
                       $query3 = "SELECT Id as IndServiceTypeId,
                                          SESISParaServiceTypeId    

                                     FROM SchServiceTypes
                                      WHERE DOEServiceTypeIndId = '{$doe_service_code}'
                         ";


                        $result3 = $connection->query ($query3);
                        


                        if ($result3->numRows() == 1) {  

 

                            
                            while ($row3 =& $result3->fetchRow (DB_FETCHMODE_ASSOC)) {
                                    $service_type_search = $row3['IndServiceTypeId'];
                                    $para_service_type_id = $row3['SESISParaServiceTypeId'];

                             }  
                        }    



                    }

                   //echo ' DOE Service Code: '.$doe_service_code.' $service_type_search: '.$service_type_search.' group_fl: '.$group_fl.'</br>';



                    if ($para_service_type_id == '0')  { //Exclude Para services - Start


                    //==============================
                    // Get Service Date
                    //==============================
                    
                    $service_date = date("Y-m-d", strtotime($row_xls[24]));


                    
                    //echo ' Therapist Id: '.$therapist_id.' Student ID: '.$student_id.' Service Code: '.$doe_service_code.' Service Date: '.$service_date.' Service Type Id: '.$service_type_id.'</br>';                 
                 
                    /*   
 
                       $query1 = "SELECT   a.Id as ScheduleId,
                                            DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
                                            DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
                                            a.Id as ServiceId,
                                            a.SessionGrpSize as GroupSize,
                                            a.SchoolId,
                                            CASE  a.MandateId 
                                            when  0 then (SELECT e.Id 
                                                            FROM   SchStudentMandates e 
                                                            WHERE  a.StudentId = e.StudentId
                                                            AND    a.ServiceTypeId = e.ServiceTypeId LIMIT 1
                                                         )
                                            else a.MandateId 
                                            END AS MandateId,
                                            a.SchoolId,
                                            a.TotalHours,
                                            a.ServiceTypeId  
         
                                    FROM    WeeklyServices a,
                                            Registrants b,
                                            SchStudents c,
                                            SchServiceTypes d
                                   WHERE ServiceDate = '{$service_date}'
                                    AND a.ScheduleStatusId = '8'
                                    AND a.BilledFL = 0 
                                    AND a.RegistrantId = b.Id
                                    AND a.StudentId = c.Id  
                                    AND a.ServiceTypeId = d.Id 
                                    AND FIND_IN_SET (a.ServiceTypeId, '{$service_type_search}') 
                                    AND b.ExtId = '{$therapist_id}'
                                    AND c.ExtId = '{$student_id}'";
               

                        }   elseif ($group_fl == '0') { // Individual Mandate
                        */

                        if ($group_fl == '0') { // Individual Mandate()    
 
                            $query1 = "SELECT   a.Id as ScheduleId,
                                                DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
                                                DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
                                                a.Id as ServiceId,
                                                a.SessionGrpSize as GroupSize,
                                                a.SchoolId,
                                                a.MandateId, 
                                                a.SchoolId,
                                                a.TotalHours,
                                                a.ServiceTypeId  
             
                                        FROM    WeeklyServices a,
                                                Registrants b,
                                                SchStudents c,
                                                SchServiceTypes d,
                                                SchStudentMandates e                             
                                        WHERE ServiceDate = '{$service_date}'
                                        AND a.ScheduleStatusId = '8'
                                        AND a.BilledFL = 0 
                                        AND a.RegistrantId = b.Id
                                        AND a.StudentId = c.Id  
                                        AND a.ServiceTypeId = d.Id 
                                        AND FIND_IN_SET (a.ServiceTypeId, '{$service_type_search}') 
                                        AND b.ExtId = '{$therapist_id}'
                                        AND c.ExtId = '{$student_id}'
                                        AND a.MandateId = e.Id
                                        AND e.SessionFrequency = '{$doe_mand_freq}'
                                        AND e.SessionLength = '{$doe_mand_duration}' 
                                        AND e.SessionGrpSize = '{$doe_mand_grp_size}'
                                        
                                           LIMIT 1 ";

                            
                            } else { // Group Mandate

                            $query1 = "SELECT   a.Id as ScheduleId,
                                                DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
                                                DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
                                                a.Id as ServiceId,
                                                a.SessionGrpSize as GroupSize,
                                                a.SchoolId,
                                                a.MandateId, 
                                                a.SchoolId,
                                                a.TotalHours,
                                                a.ServiceTypeId  
             
                                        FROM    WeeklyServices a,
                                                Registrants b,
                                                SchStudents c,
                                                SchServiceTypes d,
                                                SchStudentMandates e                             
                                        WHERE ServiceDate = '{$service_date}'
                                        AND a.ScheduleStatusId = '8'
                                        AND a.BilledFL = 0 
                                        AND a.RegistrantId = b.Id
                                        AND a.StudentId = c.Id  
                                        AND a.ServiceTypeId = d.Id 
                                        AND FIND_IN_SET (a.ServiceTypeId, '{$service_type_search}') 
                                        AND b.ExtId = '{$therapist_id}'
                                        AND c.ExtId = '{$student_id}'
                                        AND e.SessionFrequency = '{$doe_mand_freq}'
                                        AND e.SessionLength = '{$doe_mand_duration}' 
                                        AND e.SessionGrpSize = '{$doe_mand_grp_size}'
                                        AND a.MandateId = e.Id
                                          ";

                            }     

  
  

                    $result1 = $connection->query ($query1);
                   
                    if ($result1->numRows() == 1) { // numRows() == 1 - Start
                 
                 
                
                        if (DB::isError($result1)){
                            //die("Could not query the database:<br />$query ".DB::errorMessage($result1));
                            die($db->getMessage());
                        }

                        
                        while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {
                                $schedule_id = $row['ScheduleId'];
                                $start_time = $row['StartTime'];
                                $end_time = $row['EndTime'];
                                $service_id = $row['ServiceId'];
                                $mandate_id = $row['MandateId'];
                                $school_id = $row['SchoolId'];
                                $total_hours = $row['TotalHours'];
                                $service_type_id = $row['ServiceTypeId'];
                                $group_size = $row['GroupSize'];

                        }  
                        

                        if (!$group_size) {
                            $group_size = 1;
                        }

 
                    //echo ' Therapist Id: '.$therapist_id.' Student ID: '.$student_id.' Service Code: '.$doe_service_code.' Service Date: '.$service_date.' Service Type Id: '.$service_type_id.' Service Type Search: '.$service_type_search.'</br>';                 
                        
                        
                         
 
                            //=======================

                             //echo 'service_type_id: '.$service_type_id.'</br>';


                               
                            $bill_rate = getBillRate($connection, $mandate_id, $school_id, $service_type_id);
                            
                            // 40 Minutes correction 
                            //=======================
                        /*        
                            if ($total_hours == 0.67) {

                                $multiplier = 0.66666666667;
                            
                            } else {
                            */
                                $multiplier = $total_hours;


                         //   }

                            //$bill_amount = $bill_rate * $total_hours / $group_size;
                            $bill_amount = $bill_rate * $multiplier / $group_size;
                            $bill_amount = number_format($bill_amount,2,'.',',');
                            //=======================

                        
                    
                                                
                            
                            $row_xls[25] = 'P';
                            $row_xls[26] =  $group_size;
                            $row_xls[27] =  $start_time;
                            $row_xls[28] = $end_time;
                            $row_xls[29] = 'S';
                            $row_xls[32] = $bill_amount;
                                
                            $linecount++;
                            $i++;
                            $write_flag = '1';
                            
                            fputcsv($output_file_handle, $row_xls);
                            $out_Line_str = implode("\t", $row_xls); 
                            $out_Line_str = $out_Line_str."\n";
                            fwrite($fh, $out_Line_str);
                            //echo   $out_Line_str.'</br>'; 

                            //======== Set BilledFL ======== 

                            $query_bld_fl  = "UPDATE  WeeklyServices 
                                                 SET BilledFl = '1' 
                                                WHERE Id = '{$schedule_id}'
                                                ";
                            $result_bld_fl = $connection->query ($query_bld_fl);



                        


                         
                    }   // numRows() == 1 - End

                } //Exclude Para services - End                


            } // Cnt !=1  - end

            /* Wrong file selected error
             =====================================*/
            if ($err_flag == '1') {

                //echo "Wrong file was selected. Please try again!!!</br>"; 
                

                $linecount = 0;
                echo  "{ success: true, transactions: '{$linecount}'}";
                Return ; 

            }


   } // for foreach - end


    fclose($input_file_handle);
    fclose($output_file_handle);
    fclose($fh); 
    $connection->disconnect();


    $zip = new ZipArchive();
    $filename = "../uploads/eWebToVPortalZipFile.zip";
    $downloaded_filename = "eWebToVPortalZipFile.zip";

    if ($zip->open($filename, ZipArchive::CREATE)!==TRUE) {
        exit("cannot open <$filename>\n");
    }

    $zip->addFile($out_File,'eWebToVPortalUploadFile.txt');
    $zip->addFile($outputFileName, 'eWebToVPortalVerificationFile.csv');
    $zip->close();


    echo  "{ success: true, transactions: '{$linecount}'}";


 

 
    
    //=======================

    function getBillRate  ($connection, $mandate_id, $school_id, $service_type_id) {

        $query4 = "SELECT  BillRate
             from SchDistrictServiceDetails a,  
                 SchStudentMandates b,
                 SchSchools c

                    WHERE b.Id = '{$mandate_id}'
                    AND   c.Id = '{$school_id}'
                    AND   a.DistrictId = c.DistrictId
                    AND   a.BillingContractId = b.BillingContractId
                    AND   a.ServiceTypeId = '{$service_type_id}'   ";


 
        $result4 = $connection->query ($query4);

        //echo '$query4 :'.$query4.'</br>';        

        while ($row4 =& $result4->fetchRow (DB_FETCHMODE_ASSOC)) {
                $bill_rate = $row4['BillRate'];
        }  

        return $bill_rate;
 
    }    

  



?>