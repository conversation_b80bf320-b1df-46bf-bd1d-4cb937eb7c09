  /*=========================================*/

  DELIMITER $$

  DROP PROCEDURE IF EXISTS proc_setSchGenerateStudentAssignmentsFromMandate$$

  CREATE PROCEDURE proc_setSchGenerateStudentAssignmentsFromMandate (IN   p_mandate_id INT, p_student_id INT,  p_user_id INT ) 
                                     
  

BEGIN


  DECLARE v_Registrant_Id, v_Student_Id,  v_Para_Trans_Perc, v_Service_TypeId, v_SESIS_Para_Service_Type_Id INT;
  DECLARE v_Assignment_Id_1, v_Assignment_Id_2 INT; 
  DECLARE v_Search_Id_1, v_Search_Id_2 VARCHAR(45);
  DECLARE V_Para_Start_Time_Mon1, V_Para_End_Time_Mon1, V_Para_Start_Time_Mon2, V_Para_End_Time_Mon2 TIME;
  DECLARE V_Para_Start_Time_Tue1, V_Para_End_Time_Tue1, V_Para_Start_Time_Tue2, V_Para_End_Time_Tue2 TIME;
  DECLARE V_Para_Start_Time_Wed1, V_Para_End_Time_Wed1, V_Para_Start_Time_Wed2, V_Para_End_Time_Wed2 TIME;
  DECLARE V_Para_Start_Time_Thu1, V_Para_End_Time_Thu1, V_Para_Start_Time_Thu2, V_Para_End_Time_Thu2 TIME;
  DECLARE V_Para_Start_Time_Fri1, V_Para_End_Time_Fri1, V_Para_Start_Time_Fri2, V_Para_End_Time_Fri2 TIME;
  DECLARE V_Para_TotalHoursMon1, V_Para_TotalHoursMon2 DECIMAL(5,2);
  DECLARE V_Para_TotalHoursTue1, V_Para_TotalHoursTue2 DECIMAL(5,2);
  DECLARE V_Para_TotalHoursWed1, V_Para_TotalHoursWed2 DECIMAL(5,2);
  DECLARE V_Para_TotalHoursThu1, V_Para_TotalHoursThu2 DECIMAL(5,2);
  DECLARE V_Para_TotalHoursFri1, V_Para_TotalHoursFri2 DECIMAL(5,2);
  DECLARE V_Start_Date DATE;
  DECLARE v_Para1to1AMId, v_Para1to1PMId VARCHAR(16);


  

  /* Get Student ID   
    =================================================*/   

  SELECT   a.StudentId,
           a.RegistrantId,
           a.ParaTransportPerc,
           a.ServiceTypeId,

           a.ParaStartTimeMon1,
           a.ParaEndTimeMon1,
           a.ParaTotalHoursMon1,
           a.ParaStartTimeMon2,
           a.ParaEndTimeMon2,
           a.ParaTotalHoursMon2,
           a.ParaStartTimeTue1,
           a.ParaEndTimeTue1,
           a.ParaTotalHoursTue1,
           a.ParaStartTimeTue2,
           a.ParaEndTimeTue2,
           a.ParaTotalHoursTue2,

           a.ParaStartTimeWed1,
           a.ParaEndTimeWed1,
           a.ParaTotalHoursWed1,
           a.ParaStartTimeWed2,
           a.ParaEndTimeWed2,
           a.ParaTotalHoursWed2,
           a.ParaStartTimeThu1,
           a.ParaEndTimeThu1,
           a.ParaTotalHoursThu1,
           a.ParaStartTimeThu2,
           a.ParaEndTimeThu2,
           a.ParaTotalHoursThu2,

           a.ParaStartTimeFri1,
           a.ParaEndTimeFri1,
           a.ParaTotalHoursFri1,
           a.ParaStartTimeFri2,
           a.ParaEndTimeFri2,
           a.ParaTotalHoursFri2,
           b.SESISParaServiceTypeId,
           a.StartDate,
           b.Para1to1AMId,
           b.Para1to1PMId 


   INTO   v_Student_Id,
          v_Registrant_Id,
          v_Para_Trans_Perc,
          v_Service_TypeId,

          V_Para_Start_Time_Mon1,
          V_Para_End_Time_Mon1,
          V_Para_TotalHoursMon1,
          V_Para_Start_Time_Mon2,
          V_Para_End_Time_Mon2,
          V_Para_TotalHoursMon2,
          
          V_Para_Start_Time_Tue1,
          V_Para_End_Time_Tue1,
          V_Para_TotalHoursTue1,
          V_Para_Start_Time_Tue2,
          V_Para_End_Time_Tue2,
          V_Para_TotalHoursTue2,

           V_Para_Start_Time_Wed1,
           V_Para_End_Time_Wed1,
           V_Para_TotalHoursWed1,
           V_Para_Start_Time_Wed2,
           V_Para_End_Time_Wed2,
           V_Para_TotalHoursWed2,
          
           V_Para_Start_Time_Thu1,
           V_Para_End_Time_Thu1,
           V_Para_TotalHoursThu1,
           V_Para_Start_Time_Thu2,
           V_Para_End_Time_Thu2,
           V_Para_TotalHoursThu2,

           V_Para_Start_Time_Fri1,
           V_Para_End_Time_Fri1,
           V_Para_TotalHoursFri1,
           V_Para_Start_Time_Fri2,
           V_Para_End_Time_Fri2,
           V_Para_TotalHoursFri2,
          
           v_SESIS_Para_Service_Type_Id,
           V_Start_Date,
           v_Para1to1AMId,
           v_Para1to1PMId 

    FROM SchStudentMandates a, SchServiceTypes b
  WHERE a.Id = p_mandate_id
  AND   a.ServiceTypeId = b.Id ;
 

    
 
 
  IF (v_SESIS_Para_Service_Type_Id = 1) THEN /* 1 to 1 - Start   */
  

          /* Assignment Header  AM - Start*/
          /*================================*/
          
          SELECT rand()  INTO v_Search_Id_1 ;


          INSERT INTO SchStudentAssignmentHeader 
          ( 
          SearchId,
          StudentId,
          MandateId,
          ServiceTypeId,
          StatusId,
          StartDate,
          EndDate,
          UserId,
          TransDate)
           
        SELECT
          v_Search_Id_1,
          p_student_id,
          p_mandate_id,
          v_Para1to1AMId,
          '1',
          StartDate,
          EndDate,
          p_user_id,
          NOW()  
        FROM SchStudentMandates
        WHERE  Id = p_mandate_id  
          ;



          /*=======   Get Assignment ID =======*/

          SELECT Id INTO v_Assignment_Id_1
            FROM SchStudentAssignmentHeader
          WHERE SearchId = v_Search_Id_1 ;

          /*=======   Set Assignment Details - Mon AM=======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '1',
                  V_Para_Start_Time_Mon1,
                  V_Para_End_Time_Mon1,
                  V_Para_TotalHoursMon1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )   
 
            ;
           
  
          /*=======   Set Assignment Details - Tue AM=======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '2',
                  V_Para_Start_Time_Tue1,
                      V_Para_End_Time_Tue1,
                      V_Para_TotalHoursTue1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*=======   Set Assignment Details - Wed =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '3',
                  V_Para_Start_Time_Wed1,
                      V_Para_End_Time_Wed1,
                      V_Para_TotalHoursWed1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Thu =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '4',
                  V_Para_Start_Time_Thu1,
                      V_Para_End_Time_Thu1,
                      V_Para_TotalHoursThu1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Fri =======*/

            

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '5',
                  V_Para_Start_Time_Fri1,
                      V_Para_End_Time_Fri1,
                      V_Para_TotalHoursFri1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;



 





          /* Assignment Header  PM - Start*/
          /*================================*/
          
          SELECT rand()  INTO v_Search_Id_2 ;


          INSERT INTO SchStudentAssignmentHeader 
          ( 
          SearchId,
          StudentId,
          MandateId,
          ServiceTypeId,
          StatusId,
          StartDate,
          EndDate,
          UserId,
          TransDate)
           
        SELECT
          v_Search_Id_2,
          p_student_id,
          p_mandate_id,
          v_Para1to1PMId,
          '1',
          StartDate,
          EndDate,
          p_user_id,
          NOW()  
        FROM SchStudentMandates
        WHERE  Id = p_mandate_id  
          ;



          /*=======   Get Assignment ID =======*/

          SELECT Id INTO v_Assignment_Id_2
            FROM SchStudentAssignmentHeader
          WHERE SearchId = v_Search_Id_2 ;

          /*=======   Set Assignment Details - Mon PM=======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '1',
                  V_Para_Start_Time_Mon2,
                  V_Para_End_Time_Mon2,
                  V_Para_TotalHoursMon2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )   
 
            ;
           
  
          /*=======   Set Assignment Details - Tue AM=======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '2',
                  V_Para_Start_Time_Tue2,
                      V_Para_End_Time_Tue2,
                      V_Para_TotalHoursTue2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*=======   Set Assignment Details - Wed =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '3',
                  V_Para_Start_Time_Wed2,
                      V_Para_End_Time_Wed2,
                      V_Para_TotalHoursWed2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Thu =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '4',
                  V_Para_Start_Time_Thu2,
                      V_Para_End_Time_Thu2,
                      V_Para_TotalHoursThu2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Fri =======*/

            

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '5',
                  V_Para_Start_Time_Fri2,
                      V_Para_End_Time_Fri2,
                      V_Para_TotalHoursFri2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

 


    
  END IF; /* 1 to 1 - End   */

  /*=============================*/

 
  IF (v_SESIS_Para_Service_Type_Id = 2) THEN /* Transporation - Start   */

  SELECT rand()  INTO v_Search_Id_1 ;


          /*========== Bus - To School =========*/

          INSERT INTO SchStudentAssignmentHeader /* Assignment Header - To School Assignment  */
          ( 
          SearchId,
          StudentId,
          MandateId,
          ServiceTypeId,
          StatusId,
          StartDate,
          EndDate,
          UserId,
          TransDate)
           
        SELECT
          v_Search_Id_1,
          p_student_id,
          p_mandate_id,
          '8',
          '1',
          StartDate,
          EndDate,
          p_user_id,
          NOW()  
        FROM SchStudentMandates
        WHERE  Id = p_mandate_id  
          ;


        /*    SELECT SLEEP(5);   */


          /*=======   Get Assignment ID =======*/

          SELECT Id INTO v_Assignment_Id_1
            FROM SchStudentAssignmentHeader
          WHERE SearchId = v_Search_Id_1 ;

          /*=======   Set Assignment Details - Mon =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '1',
                  V_Para_Start_Time_Mon1,
                      V_Para_End_Time_Mon1,
                      V_Para_TotalHoursMon1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )   
 
            ;
           

  
          /*=======   Set Assignment Details - Tue =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '2',
                  V_Para_Start_Time_Tue1,
                      V_Para_End_Time_Tue1,
                      V_Para_TotalHoursTue1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*=======   Set Assignment Details - Wed =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '3',
                  V_Para_Start_Time_Wed1,
                      V_Para_End_Time_Wed1,
                      V_Para_TotalHoursWed1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Thu =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '4',
                  V_Para_Start_Time_Thu1,
                      V_Para_End_Time_Thu1,
                      V_Para_TotalHoursThu1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Fri =======*/

            

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_1,  
                  '5',
                  V_Para_Start_Time_Fri1,
                      V_Para_End_Time_Fri1,
                      V_Para_TotalHoursFri1,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*========== Bus - From School =========*/

        SELECT rand()  INTO v_Search_Id_2 ;
 

          INSERT INTO SchStudentAssignmentHeader /* Assignment Header - From School Assignment  */
          ( 
          SearchId,
          StudentId,
          MandateId,
          ServiceTypeId,
          StatusId,
          StartDate,
          EndDate,
          UserId,
          TransDate)
           
        SELECT
          v_Search_Id_2,
          p_student_id,
          p_mandate_id,
          '13',
          '1',
          StartDate,
          EndDate,
          p_user_id,
          NOW()  
        FROM SchStudentMandates
        WHERE  Id = p_mandate_id  
          ;


        /*    SELECT SLEEP(5);  */ 


          /*=======   Get Assignment ID =======*/

          SELECT Id INTO v_Assignment_Id_2
            FROM SchStudentAssignmentHeader
          WHERE SearchId = v_Search_Id_2 ;

          /*=======   Set Assignment Details - Mon =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '1',
                  V_Para_Start_Time_Mon2,
                  V_Para_End_Time_Mon2,
                  V_Para_TotalHoursMon2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )   
 
            ;
           

  
          /*=======   Set Assignment Details - Tue =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '2',
                  V_Para_Start_Time_Tue2,
                      V_Para_End_Time_Tue2,
                      V_Para_TotalHoursTue2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;


          /*=======   Set Assignment Details - Wed =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '3',
                  V_Para_Start_Time_Wed2,
                      V_Para_End_Time_Wed2,
                      V_Para_TotalHoursWed2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Thu =======*/

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '4',
                  V_Para_Start_Time_Thu2,
                      V_Para_End_Time_Thu2,
                      V_Para_TotalHoursThu2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;

          /*=======   Set Assignment Details - Fri =======*/

            

            
            INSERT into SchStudentAssignmentDetails   
                  (AssignmentId, 
                  WeekDayId,
                  StartTime,
                  EndTime,
                  TotalHours,
                  RegistrantId,
                  UserId,
                  TransDate )
              VALUES  (v_Assignment_Id_2,  
                  '5',
                  V_Para_Start_Time_Fri2,
                      V_Para_End_Time_Fri2,
                      V_Para_TotalHoursFri2,
                  v_Registrant_Id,  
                  p_user_id,
                  NOW() )  
 
            ;



  END IF; /* Transporation - End   */

  /*=============================*/ 

  select p_mandate_id as MandateId, V_Start_Date as StartDate;

 

END $$

  DELIMITER ;   

 