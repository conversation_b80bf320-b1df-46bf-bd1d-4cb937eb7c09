
<?php 
	
 
    require_once("db_GetSetData.php");

	$conn = getCon();


    $query = "	call   proc_getSchStudentParaExtendedHoursAuthorizations ()   ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 

/*
	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

 
	$result = $rcr_transaction->getSchStudentOpenParaMandates();


	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/	
?>

 