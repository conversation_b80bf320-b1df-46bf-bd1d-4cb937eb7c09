<?php
 

	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


    // Return Code
    // ==================
    $code = $_GET['code'];
    if ($code) {

    	 echo $code;  
    	 exit(); 

    }  


    // Return Auth Token
    // ==================
    $access_token = $_GET['access_token'];
    if ($code) {

         echo $access_token;  
         exit(); 

    }  


    // Return REST Token
    // ==================
    $BhRestToken = $_GET['BhRestToken'];
    if ($code) {

         echo $BhRestToken;  
         exit(); 

    }  

    // Return REST Results
    // ==================
    $start = $_GET['start'];
    if ($start) {

         echo $_GET['data'];  
         exit(); 

    }  




?> 



 