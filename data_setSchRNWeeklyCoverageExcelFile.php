<?php


    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');

    $RNLiaisonId = $_GET['RNLiaisonId'];
    $InclSchedules = $_GET['InclSchedules'];
    $ServiceDateFri = $_GET['ServiceDateFri'];
 
    // Set Mon - Fri Dates
    //=====================

    $date = new DateTime($ServiceDateFri);

    $date_mon = $date->modify('-4 days');
    $date_mon_unf =   $date_mon->format('Y-m-d');
    $date_mon_frm =   $date_mon->format('m/d/Y');


    $date_tue = $date->modify('+1 days');
    $date_tue_unf =   $date_tue->format('Y-m-d');
    $date_tue_frm =   $date_tue->format('m/d/Y');

    $date_wed = $date->modify('+1 days');
    $date_wed_unf =   $date_wed->format('Y-m-d');
    $date_wed_frm =   $date_wed->format('m/d/Y');

    $date_thu = $date->modify('+1 days');
    $date_thu_unf =   $date_thu->format('Y-m-d');
    $date_thu_frm =   $date_thu->format('m/d/Y');

    $date_fri = $date->modify('+1 days');
    $date_fri_unf =   $date_fri->format('Y-m-d');
    $date_fri_frm =   $date_fri->format('m/d/Y');

    $week_of = $date_mon_frm.' - '.$date_fri_frm;

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);

    foreach(range('A','U') as $columnID) {
        $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)
            ->setAutoSize(true);
    }


    $styleBorderAll = array(
      'borders' => array(
        'allborders' => array(
          'style' => PHPExcel_Style_Border::BORDER_THIN
        )
      )
    );

    $styleBorderTopLeftRight = array(
      'borders' => array(
        'top' => array('style' => PHPExcel_Style_Border::BORDER_THIN),
        'left' => array('style' => PHPExcel_Style_Border::BORDER_THIN),
        'right' => array('style' => PHPExcel_Style_Border::BORDER_THIN)

      ),
    );

    $styleBorderBottomLeftRight = array(
      'borders' => array(
        'bottom' => array('style' => PHPExcel_Style_Border::BORDER_THIN),
        'left' => array('style' => PHPExcel_Style_Border::BORDER_THIN),
        'right' => array('style' => PHPExcel_Style_Border::BORDER_THIN)

      ),
    );

    // Set Heading Boarders 
    //===================

    $objPHPExcel->getActiveSheet()->getStyle('A1:B1')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('A3:B3')->applyFromArray($styleBorderAll);


    $objPHPExcel->getActiveSheet()->getStyle('A6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('A7')->applyFromArray($styleBorderBottomLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('B6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('B7')->applyFromArray($styleBorderBottomLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('C6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('C7')->applyFromArray($styleBorderBottomLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('D6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('D7')->applyFromArray($styleBorderBottomLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('E6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('E7')->applyFromArray($styleBorderBottomLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('F6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('F7')->applyFromArray($styleBorderBottomLeftRight);

    $objPHPExcel->getActiveSheet()->getStyle('G6:H6')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('G7:H7')->applyFromArray($styleBorderAll);

    $objPHPExcel->getActiveSheet()->getStyle('I6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('I7')->applyFromArray($styleBorderBottomLeftRight);

    $objPHPExcel->getActiveSheet()->getStyle('J6:K6')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('J7:K7')->applyFromArray($styleBorderAll);

    $objPHPExcel->getActiveSheet()->getStyle('L6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('L7')->applyFromArray($styleBorderBottomLeftRight);

    $objPHPExcel->getActiveSheet()->getStyle('M6:N6')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('M7:N7')->applyFromArray($styleBorderAll);

    $objPHPExcel->getActiveSheet()->getStyle('O6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('O7')->applyFromArray($styleBorderBottomLeftRight);

    $objPHPExcel->getActiveSheet()->getStyle('P6:Q6')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('P7:Q7')->applyFromArray($styleBorderAll);

    $objPHPExcel->getActiveSheet()->getStyle('R6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('R7')->applyFromArray($styleBorderBottomLeftRight);

     $objPHPExcel->getActiveSheet()->getStyle('S6:T6')->applyFromArray($styleBorderAll);
    $objPHPExcel->getActiveSheet()->getStyle('S7:T7')->applyFromArray($styleBorderAll);

    $objPHPExcel->getActiveSheet()->getStyle('U6')->applyFromArray($styleBorderTopLeftRight);
    $objPHPExcel->getActiveSheet()->getStyle('U7')->applyFromArray($styleBorderBottomLeftRight);

    //================

    $objPHPExcel->getActiveSheet()->getStyle("A1:U7")->getFont()->setSize(16);
    $objPHPExcel->getActiveSheet()->getStyle("A1:U7")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->getStyle("A1:B1")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
         

    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 1, 'Agency Name'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, 1, 'RCM'); 

    $objPHPExcel->getActiveSheet()->getStyle("A3:B3")->getFont()->setSize(16);
    $objPHPExcel->getActiveSheet()->getStyle("A3:B3")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->getStyle("A3:B3")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('488AC7');


    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 3, 'Week Of'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, 3, $week_of ); 


    $objPHPExcel->getActiveSheet()->setCellValue('G5', 'MONDAY');
    $objPHPExcel->getActiveSheet()->setCellValue('J5', 'TUESDAY');
    $objPHPExcel->getActiveSheet()->setCellValue('M5', 'WEDNESDAY');
    $objPHPExcel->getActiveSheet()->setCellValue('P5', 'THURSDAY');
    $objPHPExcel->getActiveSheet()->setCellValue('S5', 'FRIDAY');



    $objPHPExcel->getActiveSheet()->getStyle("A6:B6")->getFont()->setBold( true );
    $objPHPExcel->getActiveSheet()->getStyle("A6:U6")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');

    $objPHPExcel->getActiveSheet()->setCellValue('G6',  $date_mon_frm);
    $objPHPExcel->getActiveSheet()->setCellValue('J6',  $date_tue_frm);
    $objPHPExcel->getActiveSheet()->setCellValue('M6',  $date_wed_frm);
    $objPHPExcel->getActiveSheet()->setCellValue('P6',  $date_thu_frm);
    $objPHPExcel->getActiveSheet()->setCellValue('S6',  $date_fri_frm);


    $objPHPExcel->getActiveSheet()->getRowDimension(7)->setRowHeight(25);

    $objPHPExcel->getActiveSheet()->getStyle("A7:F7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->getStyle("A7:F7")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValue('A7', 'Student Last Name');
    $objPHPExcel->getActiveSheet()->setCellValue('B7', 'Student First Name');
    $objPHPExcel->getActiveSheet()->setCellValue('C7', 'Student OSIS');
    $objPHPExcel->getActiveSheet()->setCellValue('D7', 'Assigned Nurse Name ');
    $objPHPExcel->getActiveSheet()->setCellValue('E7', 'Confirmation NO.');
    $objPHPExcel->getActiveSheet()->setCellValue('F7', 'Coverage Type');


    // Monday
    //============

    $objPHPExcel->getActiveSheet()->getStyle("G7:H7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('fa9a50');
    $objPHPExcel->getActiveSheet()->setCellValue('G7', 'Service Provided?');
    $objPHPExcel->getActiveSheet()->setCellValue('H7', 'If no service, state reason');

    $objPHPExcel->getActiveSheet()->getStyle("I7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->setCellValue('I7', 'Sub Nurse Name / Comments');

    // Tuesday
    //============

    $objPHPExcel->getActiveSheet()->getStyle("J7:K7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('fa9a50');
    $objPHPExcel->getActiveSheet()->setCellValue('J7', 'Service Provided?');
    $objPHPExcel->getActiveSheet()->setCellValue('K7', 'If no service, state reason');

    $objPHPExcel->getActiveSheet()->getStyle("L7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->setCellValue('L7', 'Sub Nurse Name / Comments');

    // Wednesday
    //============

    $objPHPExcel->getActiveSheet()->getStyle("M7:N7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('fa9a50');
    $objPHPExcel->getActiveSheet()->setCellValue('M7', 'Service Provided?');
    $objPHPExcel->getActiveSheet()->setCellValue('N7', 'If no service, state reason');

    $objPHPExcel->getActiveSheet()->getStyle("O7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->setCellValue('O7', 'Sub Nurse Name / Comments');

    // Thursday
    //============

    $objPHPExcel->getActiveSheet()->getStyle("P7:Q7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('fa9a50');
    $objPHPExcel->getActiveSheet()->setCellValue('P7', 'Service Provided?');
    $objPHPExcel->getActiveSheet()->setCellValue('Q7', 'If no service, state reason');

    $objPHPExcel->getActiveSheet()->getStyle("R7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->setCellValue('R7', 'Sub Nurse Name / Comments');

    // Friday
    //============

    $objPHPExcel->getActiveSheet()->getStyle("S7:T7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('fa9a50');
    $objPHPExcel->getActiveSheet()->setCellValue('S7', 'Service Provided?');
    $objPHPExcel->getActiveSheet()->setCellValue('T7', 'If no service, state reason');

    $objPHPExcel->getActiveSheet()->getStyle("U7")->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0');
    $objPHPExcel->getActiveSheet()->setCellValue('U7', 'Sub Nurse Name / Comments');

    //+++++++++++++++++++++++++++++++++++++


    $charset = 'utf8mb4';

    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNWeeklyReportData(?)");
    $stmt->execute([$InclSchedules]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);


    $StudentOsisNumber_Saved = '';
    $row = 7;
    foreach ($results as $result) {
        if ($result['StudentOsisNumber'] != $StudentOsisNumber_Saved) {

            $StudentOsisNumber_Saved = $result['StudentOsisNumber'];
            $row++;

            $cell_range = 'A'.$row.':U'.$row;
            $objPHPExcel->getActiveSheet()->getStyle($cell_range)->applyFromArray($styleBorderAll);
            $objPHPExcel->getActiveSheet()->getStyle($cell_range)->getFont()->setSize(11);


            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $result['LastName']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['FirstName']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['StudentOsisNumber']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['LongTermRnName']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['ConfirmationNumber']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['CoverageType']); 

        }

        // Monday
        //=============
        if ($result['DOW'] == '2') {

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['ServiceProvided']); 
 

            $objPHPExcel->getActiveSheet()
                        ->getStyle('H'.$row)
                        ->getFont()
                        ->getColor()
                        ->setRGB ('FF0000')  ;

            $objPHPExcel->getActiveSheet()
                        ->getStyle('K'.$row)
                        ->getFont()
                        ->getColor()
                        ->setRGB ('FF0000')  ;

            $objPHPExcel->getActiveSheet()
                        ->getStyle('M'.$row)
                        ->getFont()
                        ->getColor()
                        ->setRGB ('FF0000')  ;

            $objPHPExcel->getActiveSheet()
                        ->getStyle('P'.$row)
                        ->getFont()
                        ->getColor()
                        ->setRGB ('FF0000')  ;

            $objPHPExcel->getActiveSheet()
                        ->getStyle('T'.$row)
                        ->getFont()
                        ->getColor()
                        ->setRGB ('FF0000')  ;

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['ServiceNotProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['SubRNName']); 

        }

        // Tuesday
        //=============
        if ($result['DOW'] == '3') {

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['ServiceProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['ServiceNotProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(11, $row, $result['SubRNName']); 

        }
           
        // Wednesday
        //=============
        if ($result['DOW'] == '4') {

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['ServiceProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(13, $row, $result['ServiceNotProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(14, $row, $result['SubRNName']); 

        }

        // Thursday
        //=============
        if ($result['DOW'] == '5') {

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(15, $row, $result['ServiceProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(16, $row, $result['ServiceNotProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(17, $row, $result['SubRNName']); 

        }

        // Friday
        //=============
        if ($result['DOW'] == '6') {

            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(18, $row, $result['ServiceProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(19, $row, $result['ServiceNotProvided']); 
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['SubRNName']); 

        }

    }  

 
     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_weekly_report-".$DistrictId.".xlsx";

     $objWriter->save($out_File);

  

   $DownloadedFileName = 'rn_weely_coverage_report -'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;

   

?>
