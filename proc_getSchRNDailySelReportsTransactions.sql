DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchRNDailySelReportsTransactions$$

CREATE PROCEDURE proc_getSchRNDailySelReportsTransactions (
    IN p_incl_schedules VARCHAR(50000)
)
BEGIN

    DECLARE v_LongTermNurseId INT;

    CREATE TEMPORARY TABLE tmp_trans ENGINE=MEMORY
    SELECT 
        a.Id,
        a.ServiceTypeId,
        a.Assignment<PERSON>d,
        a.Service<PERSON>ate,
        a.StartTime,
        a.EndTime,
        a.TotalHours,
        a.<PERSON>Id,
        a.RegistrantId,
        a.<PERSON>d,
        a.<PERSON>,
        CASE a.ServiceTypeId
            WHEN '39' THEN 1
            WHEN '40' THEN 2
            WHEN '41' THEN 3
            WHEN '42' THEN 4
            WHEN '43' THEN 5
            WHEN '44' THEN 6
            WHEN '45' THEN 6
        END AS ServiceTypeGroupList,
        TRIM(c.SchoolName) AS SchoolName,
        c.<PERSON> AS SchoolDBN,
        c.<PERSON>,
        e<PERSON><PERSON>,
        CASE 
            WHEN e.RNLiaisonId IS NOT NULL THEN CONCAT(TRIM(g.LastName), '  ', TRIM(g.FirstName))
            ELSE 'Undefined...'
        END AS LiaisonName,
        TRIM(d.DistrictName) AS DistrictName,
        CONCAT(TRIM(b.LastName), ', ', TRIM(b.FirstName)) AS ScheduledRNName,
        c.RNSchoolTypeId,
        COALESCE(h.PostingStatusId, '0') AS PostingStatusId,
        CASE h.PostingStatusId
            WHEN '1' THEN 'Posted Verified'
            WHEN '2' THEN 'Posted Un-Verified'
            ELSE 'Unposted'
        END AS PostingStatusDesc,
        COALESCE(h.PostingStatusComments, '') AS PostingStatusComments
    FROM
        WeeklyServices a  
    JOIN
        Registrants b ON a.RegistrantId = b.Id
    JOIN
        SchSchools c ON a.SchoolId = c.Id
    JOIN
        SchDistricts d ON c.DistrictId = d.Id
    JOIN
        RegistrantTypes f ON b.TypeId = f.Id    
    LEFT JOIN 
        SchRNDistrictServiceLiaisons e ON e.DistrictId = c.DistrictId AND e.RNSchoolTypeId = c.RNSchoolTypeId AND e.ServiceTypeId = a.ServiceTypeId       
    LEFT JOIN  
        SchRNSchoolLiaisons g ON e.RNLiaisonId = g.Id   
    LEFT JOIN
        SchRNTransactionPostings h ON a.Id = h.ScheduleId  
    WHERE FIND_IN_SET(a.Id, p_incl_schedules);

    CREATE TEMPORARY TABLE tmp ENGINE=MEMORY
    SELECT   
        a.ServiceTypeId,
        a.SchoolId,
        a.AssignmentId,
        CASE  
            WHEN a.AssignmentId != 0 THEN 'Long'
            ELSE 'Short' 
        END AS AssignmentType, 
        CAST('' AS CHAR(10)) AS LongTermRnId, 
        CAST('' AS CHAR(512)) AS LongTermRnName, 
        a.Id AS ScheduleId,
        CONCAT(DATE_FORMAT(a.StartTime, '%l:%i %p'), '-', DATE_FORMAT(a.EndTime, '%l:%i %p'), ' (', a.TotalHours, ' Hrs)') AS ScheduleDesc,   
        a.ServiceDate, 
        a.RegistrantId AS ScheduledRNId,  
        a.ScheduledRNName,
        COALESCE(CONCAT(TRIM(c.LastName), ', ', TRIM(c.FirstName)), 'All') AS StudentName,
        a.StudentId,
        COALESCE(c.ExtId, '') AS StudentOsisNumber,
        a.DistrictId,
        a.DistrictName,
        a.RNLiaisonId,
        a.LiaisonName,
        a.SchoolName,
        a.SchoolDBN,
        CASE 
            WHEN CallInStatus IS NULL THEN 'Unconfirmed'
            ELSE CallInStatus
        END AS CallInStatus,
        d.Id AS CallInTimeId,
        COALESCE(DATE_FORMAT(d.CallInTime, '%l:%i %p'), '') AS CallInTime,
        COALESCE(CAST(d.CallInComments AS CHAR(512)), '') AS CallInComments,
        h.ServiceTypeDesc,
        a.ConfirmationNumber,
        a.ServiceTypeGroupList,
        a.RNSchoolTypeId,
        a.PostingStatusId,
        a.PostingStatusDesc,
        a.PostingStatusComments
    FROM tmp_trans a
    LEFT JOIN SchStudents c ON a.StudentId = c.Id 
    JOIN SchServiceTypes h ON a.ServiceTypeId = h.Id
    LEFT JOIN SchRNDailyCallInTimes d ON a.RegistrantId = d.RegistrantId AND a.ServiceDate = d.ServiceDate;

    UPDATE tmp a
    SET a.LongTermRnId = (
        SELECT b.RegistrantId
        FROM SchSchoolAssignmentDetails b
        WHERE a.AssignmentId = b.AssignmentId
        LIMIT 1	
    )
    WHERE a.AssignmentId != 0
    AND a.ServiceTypeId BETWEEN 39 AND 42;

    UPDATE tmp a
    SET a.LongTermRnId = (
        SELECT b.RegistrantId
        FROM SchStudentAssignmentDetails b
        WHERE a.AssignmentId = b.AssignmentId
        LIMIT 1	
    )
    WHERE a.AssignmentId != 0
    AND a.ServiceTypeId > 42;

    UPDATE tmp a, Registrants b
    SET a.LongTermRnName = CONCAT(TRIM(b.LastName), ', ', TRIM(b.FirstName)) 	   
    WHERE a.AssignmentId != 0
    AND a.LongTermRnId = b.Id;

    SET @s = CONCAT(
        "SELECT   
            ServiceTypeGroupList,
            ServiceTypeId,
            ScheduleId,
            ServiceDate,
            DistrictId,
            DistrictName,
            RNLiaisonId,
            LiaisonName,
            ScheduledRNId,
            SchoolId,
            SchoolName,
            SchoolDBN,
            CallInTime,
            CallInStatus,
            LongTermRnName,
            StudentName,
            StudentOsisNumber,
            GROUP_CONCAT(ScheduleDesc SEPARATOR ', ') AS 'ScheduleDesc',
            GROUP_CONCAT(ScheduleId SEPARATOR ', ') AS 'ScheduleIdList',
            ConfirmationNumber, 
            GROUP_CONCAT(ServiceTypeDesc SEPARATOR ', ') AS 'Placement',
            AssignmentType,
            CallInComments,
            PostingStatusId,
            PostingStatusDesc,
            PostingStatusComments
        FROM tmp"
    );

    SET @g = " GROUP BY ScheduledRNId, ServiceDate, ServiceTypeGroupList ORDER BY LiaisonName, ServiceDate";

    SET @s = CONCAT(@s, @g);

    PREPARE stmt FROM @s;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    DROP TEMPORARY TABLE IF EXISTS tmp;
    DROP TEMPORARY TABLE IF EXISTS tmp_trans;

END $$

DELIMITER ;
