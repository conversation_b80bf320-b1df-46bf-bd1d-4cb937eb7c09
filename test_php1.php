<?php
	/** Error reporting */
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);

	require_once('DB.php');
	include('db_login.php');

	/** PHPExcel */
	//include 'PHPExcel.php';

	include('../../phpexcel-1-8/Classes/PHPExcel.php');

	/** PHPExcel_Writer_Excel2007 */
	//include 'PHPExcel/Writer/Excel2007.php';
	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');

	
	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



// Create new PHPExcel object
//echo date('H:i:s') . " Create new PHPExcel object\n";
$objPHPExcel = new PHPExcel();

// Set properties
echo date('H:i:s') . " Set properties\n";
//$objPHPExcel->getProperties()->setCreator("Maarten Balliauw");
//$objPHPExcel->getProperties()->setLastModifiedBy("Maarten Balliauw");
//$objPHPExcel->getProperties()->setTitle("Office 2007 XLSX Test Document");
//$objPHPExcel->getProperties()->setSubject("Office 2007 XLSX Test Document");
//$objPHPExcel->getProperties()->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.");


// Add some data
echo date('H:i:s') . " Add some data\n";
$objPHPExcel->setActiveSheetIndex(0);
$objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Applicant Name');
$objPHPExcel->getActiveSheet()->SetCellValue('B1', 'HRID');
$objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Service Date');
$objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Hours');
$objPHPExcel->getActiveSheet()->SetCellValue('E1', 'School');



	$query = "SELECT  CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName,
			        HrId,
					DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
			        a.TotalHours,
			        SchoolName
			from  WeeklyServices a, 
			      Registrants b,
			      SchSchools c
			where a.ServiceDate between '2017-08-05' and '2017-08-19'
			and   a.ScheduleStatusId > 6
			and   a.RegistrantId = b.id
			and   HrId != 1212 
			and   HrTypeId = '1'   
			and   a.SchoolId = c.Id
			";


	$result = $connection->query ($query);

	

	echo ' # of Rows: '.$result->numRows().'</br>'; 

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	


            $row_num++;

            echo ' $row_num: '.$row_num.' Registrant: '.$row['RegistrantName'].'</br>';  
         
		    // Set Value of col=0 Applicant Name        
		    //$objWorkSheet->setCellValueExplicitByColumnAndRow(0, $row_num, $row['RegistrantName'],PHPExcel_Cell_DataType::TYPE_STRING) ;
    



	}	




// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";
$objPHPExcel->getActiveSheet()->setTitle('Employee');

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/eweb_to_payroll_para_upload.xlsx";

$objWriter->save($out_File);

// Echo done
echo date('H:i:s') . " Done writing file.\r\n";

		$connection->disconnect();


?>