<?php 


    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require_once("db_GetSetData.php");

	$conn = getCon();

   
    
    $Month = $_GET['Month'];
    $Year = $_GET['Year'];
    $Company = $_GET['Company'];

    $date = "'".$Year.'-'.$Month.'-1'."'";


    $outputFileName = '/var/www/clock-in/David/Statistics - Company D.csv';
    $output_file_handle = fopen($outputFileName, "a");


	$query = " SELECT 
			        MONTHNAME($date) as 'MonthName',
			        ServiceTypeDesc as 'Type',
			        count(distinct a.RegistrantId) as 'Providers',
			        count(distinct a.StudentId) as 'Students', 
			        sum(TotalHours) as 'Hours'
			        
			 
			 from WeeklyServices a, 
			      Registrants b,
			      SchServiceTypes c
			 where a.ScheduleStatusId >= 7
			 and year(a.ServiceDate) = '{$Year}'
			 and month(a.ServiceDate) = '{$Month}'
			 and a.RegistrantId = b.Id
			 and a.ServiceTypeId = c.Id
			 group by ServiceTypeDesc  


					 ";

	// $ret = getData ($conn, $query);

	// echo $ret;


	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));


	// var_dump($result);	

	while ($row = $result->fetch_assoc()) {
 
	        	$line = array();
	        	array_push($line, $Company);  
	        	array_push($line, $Year);  
	       	 	array_push($line, $row['MonthName']);  
	        	array_push($line, $row['Type']);  
 	        	array_push($line, $row['Providers']);  
 	        	array_push($line, $row['Students']);  
 	        	array_push($line, $row['Hours']);  	
  
  	            $result_csv  =  fputcsv($output_file_handle, $line);
	      

	}	



	fclose($output_file_handle);
	setDisConn($conn);

?>
