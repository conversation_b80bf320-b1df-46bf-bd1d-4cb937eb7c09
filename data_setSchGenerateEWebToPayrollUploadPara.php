<?php
/** Error reporting */
/*
    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);
*/

    require_once('db_login.php');
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

    try {
    	$conn = new PDO("mysql:host=$db_host;dbname=$db_database;charset=utf8", $db_username, $db_password);
    	$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch (PDOException $e) {
    	die("Connection failed: " . $e->getMessage());
    }

    $UserId = $_POST['UserId'] ?? '0';
    $Data = json_decode($_POST['Data'], true);
    $HrTypeId = $_POST['HrTypeId'] ?? '0';
    $BillingContractName = $_POST['BillingContractName'] ?? '';
    $SheetName = ($HrTypeId == '1') ? 'Employee' : '1099 Subcontractor';

    /*=========== Get/update Next Payroll Batch Number ================*/
    $query = "SELECT (MAX(Id) + 1) AS NextBatchNumber FROM SchPayrollBatchHeader";
    $next_payroll_batch_num = $conn->query($query)->fetchColumn() ?: 1;

    $query = "INSERT INTO SchPayrollBatchHeader (Id, BatchDate, BatchCount, BatchTypeId, UserId, TransDate) 
    VALUES (:Id, CURDATE(), 0, '1', :UserId, NOW())";
    $stmt = $conn->prepare($query);
    $stmt->execute(['Id' => $next_payroll_batch_num, 'UserId' => $UserId]);

    /*=========== Set Selected Transactions with the Next Payroll Batch Number ================*/
    $query = "UPDATE WeeklyServices SET PayrollBatchNumber = :BatchNum, PaidFL = '1', UserId = :UserId, TransDate = NOW() 
    WHERE Id = :ScheduleId";
    $stmt = $conn->prepare($query);
    foreach ($Data as $ScheduleId) {
    	$stmt->execute(['BatchNum' => $next_payroll_batch_num, 'UserId' => $UserId, 'ScheduleId' => $ScheduleId]);
    }

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);
    $sheet = $objPHPExcel->getActiveSheet();
    $headers = ['Applicant Name', 'HRID', 'Service Date', 'Start Time', 'End Time', 'Service Type', 'Billing Contract', 'Hours', 'Student', 'School Name', 'School Name DBN', 'District', 'Borough'];
    foreach ($headers as $key => $header) {
    	$sheet->setCellValueByColumnAndRow($key, 1, $header);
    }

    $query = "SELECT DISTINCT a.RegistrantId, CONCAT(TRIM(b.LastName), ', ', TRIM(b.FirstName)) AS RegistrantName, HrId,
    DATE_FORMAT(a.ServiceDate, '%m/%d/%Y') AS ServiceDate, DATE_FORMAT(StartTime, '%l:%i %p') AS StartTime,
    DATE_FORMAT(EndTime, '%l:%i %p') AS EndTime, a.TotalHours, 
    CONCAT(TRIM(m.SchoolName), ' (', d.DistrictName, ') ', c.ExtId) AS SchoolName,
    CONCAT(TRIM(c.SchoolName), ' (', d.DistrictName, ') ', c.ExtId) AS SchoolNameDBN,
    d.DistrictName, CONCAT(TRIM(e.LastName), ', ', TRIM(e.FirstName), ' (', e.ExtId, ')') AS StudentName,
    ServiceTypeDesc, n.BoroughCode 
    FROM WeeklyServices a
    JOIN Registrants b ON a.RegistrantId = b.id
    JOIN SchSchools c ON a.SchoolId = c.Id
    JOIN SchDistricts d ON c.DistrictId = d.Id
    JOIN SchStudents e ON a.StudentId = e.Id
    JOIN SchServiceTypes g ON a.ServiceTypeId = g.Id
    JOIN SchSubSchools m ON a.SchoolId = m.SchoolId AND e.SubSchoolTypeId = m.SubSchoolTypeId
    JOIN SchBoroughs n ON d.BoroughId = n.Id
    WHERE PayrollBatchNumber = :BatchNum";

    $stmt = $conn->prepare($query);
    $stmt->execute(['BatchNum' => $next_payroll_batch_num]);

    $linecount = 0;
    $row_num = 2;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    	$linecount++;
 
           // $district_number = preg_replace('/\D/', '', $row['DistrictName']);
            // Step 1: Remove the part of the string that starts with "CSE..."
            $stringWithoutCSE = preg_replace('/\sCSE.*/', '', $row['DistrictName']);

            // Step 2: Extract digits from the remaining string
            preg_match('/\d+/', $stringWithoutCSE, $matches);
            $district_number = $matches[0];

    	$data = [
    		$row['RegistrantName'], $row['HrId'], $row['ServiceDate'],
    		$row['StartTime'], $row['EndTime'], $row['ServiceTypeDesc'],
    		$BillingContractName, $row['TotalHours'], $row['StudentName'],
    		$row['SchoolName'], $row['SchoolNameDBN'], $district_number, $row['BoroughCode']
    	];

    	foreach ($data as $key => $value) {
    		$sheet->setCellValueByColumnAndRow($key, $row_num, $value);
    	}
    	$row_num++;
    }

    $query = "UPDATE SchPayrollBatchHeader SET BatchCount = :BatchCount WHERE Id = :BatchNum";
    $stmt = $conn->prepare($query);
    $stmt->execute(['BatchCount' => $linecount, 'BatchNum' => $next_payroll_batch_num]);

    $out_File = "../uploads/eweb_to_payroll_upload.xlsx";
    $objPHPExcel->getActiveSheet()->setTitle($SheetName);
    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    $objWriter->save($out_File);

    $conn = null;
    echo json_encode(["success" => true, "transactions" => $linecount]);
?>