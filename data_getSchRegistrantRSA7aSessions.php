<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
    $MandateId = $_GET['MandateId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
    $FormType = $_GET['FormType'];

    $FormType = trim($FormType);    

    if ($FormType == 'In-Person') {

        $query = "  SELECT   DISTINCT DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                            ServiceDate as ServiceDateSort,
                            DATE_FORMAT( ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                            StartTime as StartTimeSort,
                            DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                            DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                            SessionGrpSize

                        FROM    WeeklyServices                  
                            Where RegistrantId= '{$RegistrantId}'
                            AND   MandateId = '{$MandateId}' 
                            AND   ScheduleStatusId > '6'
                            AND   ServiceDate between  '{$FromDate}' and '{$ToDate}'  
                            AND   SessionDeliveryModeId = 'I' 
                    ORDER BY ServiceDateSort, StartTimeSort   ";

    } else {

            $query = "  SELECT   DISTINCT DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                                ServiceDate as ServiceDateSort,
                                DATE_FORMAT( ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                                StartTime as StartTimeSort,
                                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                                DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                                SessionGrpSize

                            FROM    WeeklyServices                  
                                Where RegistrantId= '{$RegistrantId}'
                                AND   MandateId = '{$MandateId}' 
                                AND   ScheduleStatusId > '6'
                                AND   ServiceDate between  '{$FromDate}' and '{$ToDate}'  
                                AND   SessionDeliveryModeId != 'I' 
                        ORDER BY ServiceDateSort, StartTimeSort   ";


    }



	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
    // echo $query;


?>

