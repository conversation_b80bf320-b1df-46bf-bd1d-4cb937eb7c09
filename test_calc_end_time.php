<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

$morningStartTime = "07:30:00";
$total_hours_str = '6.4';
$totalHours = (float)$total_hours_str;

// Convert total hours to seconds
$total_seconds = $totalHours * 3600;

// Add seconds to the start time
$end_time = date('H:i:s', strtotime($morningStartTime) + $total_seconds);

echo "End time: $end_time";

?>
