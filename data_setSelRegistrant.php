<?php 


	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$form_data = json_decode(file_get_contents('php://input'));

	$Id =  	$form_data->{'Id'};
	$SearchId =		$form_data->{'SearchId'};
	$StatusId =		$form_data->{'StatusId'};	
	$ExtId =		$form_data->{'ExtId'};
	$Gender =		$form_data->{'Gender'};
	$TypeId =		$form_data->{'TypeId'};
	$OrigTypeId =		$form_data->{'OrigTypeId'};
	
	$TerminationType =		$form_data->{'TerminationType'};
	$TerminationReason =		$form_data->{'TerminationReason'};
	$TerminationDate =		$form_data->{'TerminationDate'};
	
	$BirthDate =		$form_data->{'BirthDate'};

	$FirstName =		$form_data->{'FirstName'};
	$LastName =		$form_data->{'LastName'};
	$MiddleInitial =		$form_data->{'MiddleInitial'};

	
	$StreetAddress1 =		$form_data->{'StreetAddress1'};
	$StreetAddress2 =		$form_data->{'StreetAddress2'};
	$City =		$form_data->{'City'};
	$State =		$form_data->{'State'};
	$ZipCode =		$form_data->{'ZipCode'};
	$MobilePhone =		$form_data->{'MobilePhone'};
	$HomePhone =		$form_data->{'HomePhone'};
	
	$MobilePhoneProviderId =		$form_data->{'MobilePhoneProviderId'};
	$ClockId =		$form_data->{'ClockId'};

	$RNLicenseNumber = $form_data->{'RNLicenseNumber'};

	$Email =		$form_data->{'Email'};
	$UserId =		$form_data->{'UserId'}; 

	$FirstName = mysqli_real_escape_string($conn, $FirstName); 
	$LastName = mysqli_real_escape_string($conn, $LastName); 




		if(is_numeric($Id) ) { 	
	
            $query ="UPDATE Registrants 
							set StatusId =  '{$StatusId}', 
							TerminationType =  '{$TerminationType}',
							TerminationReason =  '{$TerminationReason}',
							TerminationDate =  '{$TerminationDate}',
							HireDate =  '{$HireDate}',
							Gender =  '{$Gender}',
							TypeId =  '{$TypeId}',
							ExtId =  '{$ExtId}',
							FirstName =  '{$FirstName}',
							LastName	 =  '{$LastName}',
							MiddleInitial	 =  '{$MiddleInitial}',
							StreetAddress1	 =  '{$StreetAddress1}',
							StreetAddress2	 =  '{$StreetAddress2}',
							City	 =  '{$City}',
							State	 =  '{$State}',
							ZipCode	 =  '{$ZipCode}',
							MobilePhone	 =  '{$MobilePhone}',
							HomePhone	 =  '{$HomePhone}',
							MobilePhoneProviderId	 =  '{$MobilePhoneProviderId}',
							Email	 =  '{$Email}',
							ClockId	 =  '{$ClockId}',
							RNLicenseNumber	 =  '{$RNLicenseNumber}',
							UserId =  '{$UserId}',
							TransDate = NOW()
						where Id = '{$Id}'  ";
        } else {
           $query ="INSERT into Registrants  
							(StatusId, 
                            SearchId, 		
                            ExtId,								
							TerminationType,
							TerminationReason,
							TerminationDate,
							BirthDate,
							Gender,
							TypeId,
							FirstName,
							LastName,
							MiddleInitial,
							StreetAddress1,
							StreetAddress2,
							City,
							State,
							ZipCode,
							MobilePhone,
							HomePhone,
							MobilePhoneProviderId,
							Email,
							ClockId,
							RNLicenseNumber,
							UserId,
							TransDate )
			values 	('{$StatusId}',  
					'{$SearchId}',	
					'{$ExtId}',
					'{$TerminationType}',
					'{$TerminationReason}',						
					'{$TerminationDate}',
					'{$BirthDate}',
					'{$Gender}',
					
					'{$TypeId}',  
					'{$FirstName}',  
					'{$LastName}',  
					'{$MiddleInitial}',  
					'{$StreetAddress1}',
					'{$StreetAddress2}',
					'{$City}', 
					'{$State}', 
					'{$ZipCode}', 
					'{$MobilePhone}', 
					'{$HomePhone}', 
					'{$MobilePhoneProviderId}', 
					'{$Email}',
					'{$ClockId}',
					 {$RNLicenseNumber}',
					'{$UserId}',
					NOW()  )";
						            

		}		


	$ret =  setData ($conn, $query);   			



	//=====================================================
	// Reset Cred. Items if Registrant's Type of changes
	//=====================================================


	if ((is_numeric($Id)) && ($TypeId != $OrigTypeId)) {	

		 $query1 = " Call proc_setRegistrantTypeCredItemsChange ( '{$Id}',
            										   			  '{$OrigTypeId}',
            													  '{$TypeId}',
            													  '{$UserId}' ) ";		

	$ret1 =  setData ($conn, $query1);   			



	}


	//=====================================================
	// Attach "DSP" Specilaty for new "DSP" Registrant 
	//=====================================================


	if (!is_numeric($Id)) {

		 $query2 = " INSERT INTO RegistrantAttchedSpecialties
			            ( RegistrantId,
			                SpecialtyId,
			                Userid,
			                TransDate
			              )
		 				
		 				SELECT  a.Id,
		 						'1',
								'{$UserId}',
								 NOW() 	
						FROM Registrants a
					WHERE a.SearchId = '{$SearchId}'	 ";		 	 						 
		 		

		$ret2 =  setData ($conn, $query2);   			



	}

	//=====================================================
	// Add Client Apporvals   
	//=====================================================


	if ((is_numeric($Id)) && ($StatusId == '1') ) {

		 $query3 = " Call proc_RegistrantUpdateClientApprovals ( '{$Id}',
            													  '{$UserId}' )	 ";		 	 						 
		 		

		$ret3 =  setData ($conn, $query3);   			



	}

	setDisConn($conn);
	
	// echo $ret;
 	
	echo $query;

?>


