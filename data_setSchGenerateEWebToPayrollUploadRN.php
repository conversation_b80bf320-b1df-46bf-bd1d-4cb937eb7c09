<?php
/** Error reporting */
/*
    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);
*/

    require_once('db_login.php');
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

    try {
    	$conn = new PDO("mysql:host=$db_host;dbname=$db_database;charset=utf8", $db_username, $db_password);
    	$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch (PDOException $e) {
    	die("Connection failed: " . $e->getMessage());
    }

    $UserId = $_POST['UserId'] ?? '0';
    $Data = json_decode($_POST['Data'], true);
    $HrTypeId = $_POST['HrTypeId'] ?? '0';
    $BillingContractId = $_POST['BillingContractId'] ?? '1';
    $BillingContractName = $_POST['BillingContractName'] ?? '';
    $SheetName = ($HrTypeId == '1') ? 'Employee' : '1099 Subcontractor';

    $summary_type = ($BillingContractId == '1') ? 'School Name' : 'Student';
    $school_dbn_title = ($BillingContractId == '1') ? 'School Name DBN' : '';

    $query = "SELECT (MAX(Id) + 1) AS NextBatchNumber FROM SchPayrollBatchHeader";
    $next_payroll_batch_num = $conn->query($query)->fetchColumn() ?: 1;

    $query = "INSERT INTO SchPayrollBatchHeader (Id, BatchDate, BatchCount, BatchTypeId, UserId, TransDate) 
    VALUES (:Id, CURDATE(), 0, '3', :UserId, NOW())";
    $stmt = $conn->prepare($query);
    $stmt->execute(['Id' => $next_payroll_batch_num, 'UserId' => $UserId]);

    $query = "UPDATE WeeklyServices SET PayrollBatchNumber = :BatchNum, PaidFL = '1', UserId = :UserId, TransDate = NOW() 
    WHERE Id = :ScheduleId";
    $stmt = $conn->prepare($query);
    foreach ($Data as $ScheduleId) {
    	$stmt->execute(['BatchNum' => $next_payroll_batch_num, 'UserId' => $UserId, 'ScheduleId' => $ScheduleId]);
    }

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);
    $sheet = $objPHPExcel->getActiveSheet();
    $headers = ['Applicant Name', 'HRID', 'Service Date', 'Start Time', 'End Time', 'District', 'Service Type', 'Billing Contract', 'Hours', 'School Name DBN', 'Borough', 'RN License #','Confirmation #'];
    foreach ($headers as $key => $header) {
    	$sheet->setCellValueByColumnAndRow($key, 1, $header);
    }

    $query = "CALL proc_getSchEWebToPayrollUploadRNData(:BatchNum, :BillingContractId, :BillingContractName)";
    $stmt = $conn->prepare($query);
    $stmt->execute(['BatchNum' => $next_payroll_batch_num, 'BillingContractId' => $BillingContractId, 'BillingContractName' => $BillingContractName]);

    $linecount = 0;
    $row_num = 2;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    	$linecount++;
        $processedString = preg_split('/\s*CSE.*/', $row['DistrictName'])[0];
        $district_number = preg_replace('/\D/', '', $processedString);
   	$data = [
    		$row['RegistrantName'], $row['HrId'], $row['ServiceDate'],
    		$row['StartTime'], $row['EndTime'], $district_number,
    		$row['ServiceTypeDesc'], $row['BillingContractName'], $row['TotalHours'],
    		$row['SchoolNameDBN'], $row['BoroughCode'], 
            $row['RNLicenseNumber'],$row['ConfirmationNumber']
    	];
    	foreach ($data as $key => $value) {
    		$sheet->setCellValueByColumnAndRow($key, $row_num, $value);
    	}
    	$row_num++;
    }

    $query = "UPDATE SchPayrollBatchHeader SET BatchCount = :BatchCount WHERE Id = :BatchNum";
    $stmt = $conn->prepare($query);
    $stmt->execute(['BatchCount' => $linecount, 'BatchNum' => $next_payroll_batch_num]);

    $out_File = "../uploads/eweb_to_payroll_upload.xlsx";
    $objPHPExcel->getActiveSheet()->setTitle($SheetName);
    $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
    $objWriter->save($out_File);

    $conn = null;
    echo json_encode(["success" => true, "transactions" => $linecount]);
?>
