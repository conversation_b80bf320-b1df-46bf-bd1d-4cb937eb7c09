<?php

	require_once('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

	try {
		$conn = new PDO("mysql:host=$db_host;dbname=$db_database;charset=utf8", $db_username, $db_password);
		$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	} catch (PDOException $e) {
		die("Connection failed: " . $e->getMessage());
	}

	$UserId = $_POST['UserId'] ?? '0';
	$Data = json_decode($_POST['Data'], true) ?? [];
	$HrTypeId = $_POST['HrTypeId'] ?? '1';
	$BillingContractId = $_POST['BillingContractId'] ?? '1';
	$BillingContractName = $_POST['BillingContractName'] ?? '';

	$SheetName = ($HrTypeId == '1') ? 'Employee' : '1099 Subcontractor';
	$summary_type = ($BillingContractId == '1') ? 'School Name' : 'Student';
	$school_dbn_title = ($BillingContractId == '1') ? 'School Name DBN' : '';

	try {
		$stmt = $conn->query("SELECT (MAX(Id) + 1) as NextBatchNumber FROM SchPayrollBatchHeader");
		$next_payroll_batch_num = $stmt->fetch(PDO::FETCH_ASSOC)['NextBatchNumber'] ?? 1;

		$stmt = $conn->prepare("INSERT INTO SchPayrollBatchHeader (Id, BatchDate, BatchCount, BatchTypeId, UserId, TransDate) VALUES (?, CURDATE(), 0, 2, ?, NOW())");
		$stmt->execute([$next_payroll_batch_num, $UserId]);

		$stmt = $conn->prepare("UPDATE WeeklyServices SET PayrollBatchNumber = ?, PaidFL = 1, UserId = ?, TransDate = NOW() WHERE Id = ?");
		foreach ($Data as $ScheduleId) {
			$stmt->execute([$next_payroll_batch_num, $UserId, $ScheduleId]);
		}

		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		$sheet = $objPHPExcel->getActiveSheet();

		$headers = ['Applicant Name', 'HRID', 'Service Date', 'Start Time', 'End Time', 'District', 'Service Type', 'Billing Contract', 'Hours', 'School Name DBN','Borough'];
		$col = 'A';
		foreach ($headers as $header) {
			$sheet->setCellValue($col . '1', $header);
			$col++;
		}

		$stmt = $conn->prepare("CALL proc_getSchEWebToPayrollUploadData(?, ?, ?)");
		$stmt->execute([$next_payroll_batch_num, $BillingContractId, $BillingContractName]);

		$linecount = 0;
		$row_num = 2;
		while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
			$linecount++;
			$district_number = preg_replace('/\D/', '', $row['DistrictName']);

			$sheet->setCellValue("A$row_num", $row['RegistrantName'])
			->setCellValue("B$row_num", $row['HrId'])
			->setCellValue("C$row_num", $row['ServiceDate'])
			->setCellValue("D$row_num", $row['StartTime'])
			->setCellValue("E$row_num", $row['EndTime'])
			->setCellValue("F$row_num", $district_number)
			->setCellValue("G$row_num", $row['ServiceTypeDesc'])
			->setCellValue("H$row_num", $row['BillingContractName'])
			->setCellValue("I$row_num", $row['TotalHours'])
			->setCellValue("J$row_num", $row['SchoolNameDBN']) 
			->setCellValue("K$row_num", $row['BoroughCode']);

			$row_num++;
		}

		$stmt = $conn->prepare("UPDATE SchPayrollBatchHeader SET BatchCount = ? WHERE Id = ?");
		$stmt->execute([$linecount, $next_payroll_batch_num]);

		$conn = null;

		$sheet->setTitle($SheetName);
		$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
		$out_File = "../uploads/eweb_to_payroll_upload.xlsx";
		$objWriter->save($out_File);

		echo json_encode(["success" => true, "transactions" => $linecount]);
	} catch (PDOException $e) {
		die("Error: " . $e->getMessage());
	}
?>