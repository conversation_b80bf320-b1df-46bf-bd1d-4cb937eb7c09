/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchRNDistrictServiceLiaisons$$

	CREATE PROCEDURE proc_getSchRNDistrictServiceLiaisons (IN  p_district_id INT  )
																 
															 
															 
															 	 

	BEGIN

	DECLARE v_RN_SchoolTypeId INT;  
	DECLARE v_RN_SchoolTypeDesc VARCHAR(18);  

	DECLARE done INT DEFAULT 0;



	DECLARE cur1 Cursor  FOR
      SELECT    Id as RNSchoolTypeId,
                RNSchoolTypeDesc
                
       from  SchRNSchoolTypes
			ORDER By Id desc				 
		;
       
	declare continue handler for not found set done := true;   


		create temporary table tmp
		(
			 
				DistrictId INT,
				RNSchoolTypeId INT,
				RNSchoolTypeDesc VARCHAR(18),
				ServiceTypeId INT,
				ServiceTypeDesc VARCHAR(45),
				RNLiaisonId INT,
				RNLiaisonName VARCHAR(256),
				UserId INT, 
				UserName VARCHAR(96),  
				TransDate VARCHAR(16)	


		);		 
		 	

	OPEN cur1;
	
	read_loop: LOOP
 
       FETCH cur1 INTO v_RN_SchoolTypeId, v_RN_SchoolTypeDesc;
   
		IF done THEN
			LEAVE read_loop;
		END IF;
	
	INSERT INTO tmp (
		DistrictId,
		RNSchoolTypeId,
		RNSchoolTypeDesc,
		ServiceTypeId,
		ServiceTypeDesc
         
	)	

	SELECT p_district_id,	
		   v_RN_SchoolTypeId,
		   v_RN_SchoolTypeDesc,
		   ServiceTypeId,
		   ServiceTypeDesc 

		   from  SchRNServiceTypesView	;
	 	   

	END LOOP;
    CLOSE cur1;

    	/* Populate RNLiaisonId from SchRNDistrictServiceLiaisons tale	 */ 	
    	/*===============================================*/

   	    update tmp a, SchRNDistrictServiceLiaisons b 

   	        SET a.RNLiaisonId = b.RNLiaisonId,
   	            a.UserId = b.UserId,
   	            a.TransDate = DATE_FORMAT( b.TransDate, '%m-%d-%Y %r' )
   	        WHERE a.DistrictId = b.DistrictId
   	        AND   a.RNSchoolTypeId = b.RNSchoolTypeId
   	        AND   a.ServiceTypeId = b.ServiceTypeId  ;   

  		/* Populate RNLiaisonName if RNLiaisonId is set  	 */ 	
    	/*===============================================*/

   	    update tmp a, SchRNSchoolLiaisons b 

   	        SET a.RNLiaisonName = CONCAT(b.FirstName,' ',b.LastName) 
   	        WHERE a.RNLiaisonId is not null  
   	        and a.RNLiaisonId = b.Id;
 
 		/* Populate UserName if UserId is set  	 */ 	
    	/*===============================================*/

   	    update tmp a, Users b 

   	        SET a.UserName = CONCAT(b.LastName,', ',b.FirstName) 
   	        WHERE a.UserId is not null  
   	        and a.UserId = b.Userid ;
 

    	select   
				DistrictId,
				RNSchoolTypeId,
				RNSchoolTypeDesc,
				ServiceTypeId,
				ServiceTypeDesc,
				COALESCE(RNLiaisonId, '')  as RNLiaisonId,
				COALESCE(RNLiaisonName, '')  as RNLiaisonName,
				COALESCE(UserId, '')  as UserId, 
				COALESCE(UserName, '')  as UserName,  
				COALESCE(TransDate, '')  as TransDate
    	from tmp; 


		drop temporary table if exists tmp;





		END $$

		DELIMITER ;		 
	    
	     