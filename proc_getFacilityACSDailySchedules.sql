

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getFacilityACSDailySchedules$$

CREATE PROCEDURE proc_getFacilityACSDailySchedules (IN p_facility_id int, p_service_date date)  

BEGIN

 	
	/*============================================*/
	
	create temporary table tmp engine=memory

 	SELECT  a.Id AS ScheduleId, 
			ScheduleStatusId, 
			ScheduleStatusDesc,
			TextColor,
			BackgroundColor,
			a.WorkOrderId,
			a.ClientId, 
			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
			ServiceDate as ServiceDateOrg,
			StartTime as StartTimeNum,
			EndTime as EndTimeNum,
			DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
			DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
			PayrollWeek,  
			ShiftId,
			LunchHour,
			TotalHours, 
			WeekDay, 
			RegistrantId, 
			ServiceTypeId,
			
			'                                                         ' as RegistrantName,
			'                               ' as RegistrantTypeDesc, 
			'                               ' as ServiceTypeDesc, 
			'                               ' as SpecialtiesList, 
			RegistrantTypeId,
			ClientUnitId, 
			COALESCE((SELECT UnitName FROM ClientUnits c
				WHERE a.ClientUnitId = c.Id), '') as UnitName,
			ClientConfFL,
			RegistrantConfFL,
			ScheduleOrigBy,
            000.0 as HoursScheduled,  			
			CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
			DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) AS TransDate
		FROM 	WeeklyServices a, 
				Users e,
                ScheduleStatuses g								
			Where 	a.ClientId= p_facility_id 
			AND  	a.ServiceDate =  p_service_date  
			AND 	a.UserId = e.UserId
			AND 	ScheduleStatusId = g.Id
			AND     ScheduleStatusId in (0,7)
				ORDER BY  StartTime; 	

	/* Set Employee Name */
	/*================================*/
	Update  tmp a,  Registrants b, RegistrantTypes f
		Set a.RegistrantName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' )	
         Where a.Registrantid IS NOT NULL 
          AND a.RegistrantId = b.Id
	AND b.TypeId = f.Id ;


	/* Set Non-Gotham Employee Name Display Colors  */
	/*============================================*/
	Update  tmp a,  RegistrantTypes f
		Set BackgroundColor = 'grey'
	WHERE   a.RegistrantTypeId = f.Id
	AND NonGothamRegistrantFL = '1'
	AND ScheduleStatusId > 6 ;

	/* Set Requested Employee Type*/
	/*================================*/
	Update  tmp a,  RegistrantTypes f
		Set  a.RegistrantTypeDesc = f.RegistrantTypeDesc
         Where  a.RegistrantTypeId = f.Id
	  ;

	/* Set Service Type Description*/
	/*================================*/
	Update  tmp a,  ServiceTypes f
		Set  a.ServiceTypeDesc = f.ServiceTypeDesc
         Where  a.ServiceTypeId = f.Id
	  ;

	
	
	Select 	b.*,
			(	SELECT
	
	CASE count(*) 
	WHEN 0 THEN ''
		ELSE group_concat( (CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' )) SEPARATOR ', ' )
	END   

						FROM 	ClientApprovedRegistrantsNonRest a, 
								RegistrantVerifiedAvailability d,  
								Registrants c,
								RegistrantTypes f
							
						   
						WHERE a.ClientId = p_facility_id
						AND a.ClientUnitId = b.ClientUnitId
						AND a.ServiceTypeId = b.ServiceTypeId
						AND a.RegistrantId = c.Id
						AND a.RegistrantId = d.RegistrantId 
						AND d.ServiceDate = p_service_date
						AND d.ShiftId = b.ShiftId	
						AND c.TypeId = f.Id
						AND a.Status = '1' 
						AND NOT EXISTS (SELECT 1 FROM WeeklyServices g
										  WHERE a.RegistrantId = g.RegistrantId
										  AND g.ServiceDate = p_service_date
										  AND g.ShiftId = b.ShiftId)			
					Order By c.LastName, c.FirstName	

				) as AvailableRegistrants 	

	from tmp b
	Order By ServiceDateOrg, StartTimeNum;
	
	drop temporary table if exists tmp;
	 
	
END $$

DELIMITER ;	