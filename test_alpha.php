  
<?php
    
	/** Include path **/
	set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');
	
	include 'PHPExcel/IOFactory.php';
	require_once('DB.php');
	include('db_login.php');
	

	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
	$query = "SELECT SchoolSeasonEndDate
		FROM SchSchoolYear			 
		WHERE CurrentYearFL = '1'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$EndDate = $row['SchoolSeasonEndDate'];
	}	
	 
 


	$inputFileType = 'Excel5';
	$inputFileName = '../uploads/mandates.xls';

	$objReader = new PHPExcel_Reader_Excel5();
	$objReader->setReadDataOnly(true);
	$objPHPExcel = $objReader->load($inputFileName);

	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
		$cnt = 0;	
		$linecount = 0;	
		
		foreach ($sheetData as &$row) {

			$m_rec = substr(($row["A"]),0,1);

			if ($m_rec == '2') {

				/* Student External ID
				 ===================*/

				$student_ext_id = $row["A"];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row["B"];


				/* Student First Name
				 ===================*/

				$student_first_name = $row["C"];


				/* Student DOB
				============================= */
				
				$dob = $row["H"];

				$dob = $dob - 1;
				$timestamp = mktime(0,0,0,1,$dob,1900);
				$dob_str = date("Y-m-d",$timestamp);



				/* Student School ID
				 ===================*/

				$student_school_id = $row["I"];



				/* Student Service Type Desc
				 ================================*/

				$student_serv_type_desc = $row["O"];


				/* Student Language
				 ================================*/

				$student_language = $row["Q"];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row["R"];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row["S"];


				/* Mandate Service Freq
				 ================================*/

				$mandate_serv_freq = $row["T"];
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Duration
				 ================================*/

				$mandate_serv_dur = $row["U"];
				$mandate_serv_dur_num = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Start Date
				============================= */
				
				$start_date = $row["AD"];

				$start_date = $start_date - 1;
				$timestamp = mktime(0,0,0,1,$start_date,1900);
				$start_date_str = date("Y-m-d",$timestamp);


				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = $row["Z"];



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result = $connection->query ($query);

				if (DB::isError($result)){
					die("Could not query the database:<br />$query ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT INTO SchStudents
						(ExtId, 
						 SearchId, 
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'1',
							'0',
							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}
				} // End 1	
					
			 
				//=======================
				// Add New Mandate
				//=======================
					$query2 = "INSERT INTO SchStudentMandates
						(	
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							UserId,
							TransDate
						) 
						SELECT   
						 
						 	'0',
							'{$student_ext_id}',
							COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),
							'1',
							Id,
							'{$StartDate}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							'{$user_id}',
							now()  
						FROM SchServiceTypes
					WHERE ServiceTypeDesc like '{$student_serv_type_desc}' ";

						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

		        if (DB::isError($result3)){
		            die("Could not query the database:<br />$query3 ".DB::errorMessage($result));
		        }



				/*=========================*/


				echo $student_ext_id.' '.$student_last_name.' '.$dob_str.' '.$student_serv_freq_num.' '.$student_serv_dur_num.'</br>';

			}


			

		}	

	$connection->disconnect();
 


?>