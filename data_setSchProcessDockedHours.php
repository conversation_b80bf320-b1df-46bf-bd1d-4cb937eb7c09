<?php

    // error_reporting(E_ALL);
    // ini_set('display_errors', TRUE);
    // ini_set('display_startup_errors', TRUE);


    // echo 'Step 1 <br>';

    // Path to the CSV file
    $csvFilePath = '../rn_reports/RCM_Healthcare_Docked_Hours_01_17_2024.csv';

    // Open the CSV file for reading
    if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
        // Read each line of the file
        
        $x = 0;

        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            
            $x++;
            
            if ($x > 1) {

                // $data is an array of columns for the current row
                $num = count($data);
                
                // Example: Print each column of the current row
                for ($c=0; $c < $num; $c++) {
                    
  
                    switch ($c) {
                        case 1:
                      
                        $conf_number = $data[$c];  
                        break;
                        
                        case 2:
                      
                        $school_dbn = $data[$c];  
                        break;
   
                        case 5:
                      
                        $rn_name = $data[$c];  
                        break;

                        case 6:
                      
                        $service_date = $data[$c];  
                        break;

                        case 7:
                      
                        $docked_hours = $data[$c];  
                        break;

                        case 8:
                      
                        $setup_hours = $data[$c];  
                        break;

                        case 9:
                      
                        $comments = $data[$c];  
                        break;


                    }

                }    

           echo  "conf num: $conf_number school dbn:  $school_dbn rn name:  $rn_name serv date: $service_date dcoked hours: $docked_hours setup hours: $setup_hours coments: $comments<br>";  
 
            }

             
        }
        // Close the file
        fclose($handle);
    }

?>
