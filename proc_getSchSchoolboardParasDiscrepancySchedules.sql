/*=========================================*/

        DELIMITER $$

        DROP PROCEDURE IF EXISTS proc_getSchSchoolboardParasDiscrepancySchedules$$

        CREATE PROCEDURE proc_getSchSchoolboardParasDiscrepancySchedules (IN    
                                                                p_from_date DATE, 
                                                                p_to_date DATE,
                                                                p_discr_type_id char(1), 
                                                                p_service_type_id_list VARCHAR(16),
                                                                p_registrant_id INT,
                                                                p_student_name VARCHAR(45)  
                                                                 
                                                            )  
                                                                 

                                                          
                                                           
                                                            
        BEGIN
        

                create temporary table tmp_trans engine=memory
                
                 SELECT  c.FirstName, c.LastName,   a.RegistrantId,
                           DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                           WeekDay, 
                           a.StartTime as act_start_time,
                           b.StartTime as sch_start_time,
                           a.EndTime as act_end_time,
                           b.EndTime as sch_end_time,
                           DATE_FORMAT( a.StartTime, '%l:%i %p' ) as ActStartTime,
                           DATE_FORMAT( b.StartTime, '%l:%i %p' ) as SchedStartTime,
                           DATE_FORMAT( a.EndTime, '%l:%i %p' ) as ActEndTime,
                           DATE_FORMAT( b.EndTime, '%l:%i %p' ) as SchedEndTime,
                           a.TotalHours as ActTotalHours,
                           b.TotalHours as SchedTotalHours,

                      COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
                        FROM SchSchools f, SchDistricts g
                        WHERE a.SchoolId = f.Id
                        AND   f.DistrictId = g.Id),'') as SchoolName,   

                       CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName,  
                           CONCAT( trim( d.LastName) , ', ', trim( d.FirstName)  ) as RegistrantName, 
                           ServiceTypeDesc
                 
                   
                   FROM WeeklyServices a,  
                        SchStudentAssignmentDetails b, 
                        SchStudents c,
                        Registrants d,
                        SchServiceTypes e
                    WHERE a.ServiceDate between   p_from_date and p_to_date
                     AND a.AssignmentId = b.AssignmentId
                     AND a.StudentId = c.Id
                     AND ScheduleStatusId = 8
                     AND b.WeekDayId = DATE_FORMAT(a.ServiceDate, '%w')
                     AND ((a.StartTime != b.StartTime) || (a.EndTime != b.EndTime))
                     AND a.RegistrantId = d.Id 
                     AND a.ServiceTypeId = e.Id
                     AND d.TypeId = 23 
                     
              ;

 



     

 
        SET @q = CONCAT("  FirstName,
                           LastName,   
                           RegistrantId,
                           ServiceDate, 
                           WeekDay, 
                           act_start_time,
                           sch_start_time,
                           act_end_time,
                           sch_end_time,
                           StartTime,
                           ActStartTime,
                           SchedStartTime,
                           ActEndTime,
                           SchedEndTime,
                           ActTotalHours,
                           SchedTotalHours,
                           SchoolName,   
                           StudentName,  
                           RegistrantName, 
                           ServiceTypeDesc
                                   

     
        from tmp 
 
         ");


            SET @g = CONCAT(" ORDER BY StudentName, ServiceDate, StartTime
            ");

     
            /* Filter By Discepancy Type - Early Start or Late End */ 
            /*=====================*/
            IF (p_discr_type_id = '1')  THEN 

                SET @s = CONCAT(@q, ' Where (act_start_time < sch_start_time) || (act_end_time > sch_end_time) ', @g);                      

            END IF;


           /* Filter By Discepancy Type - Late Start or Early End */ 
            /*=====================*/
            IF (p_discr_type_id = '1')  THEN 

                SET @s = CONCAT(@q, ' Where (act_start_time > sch_start_time) || (act_end_time < sch_end_time) ', @g);                      

            END IF;

  
            /* Filter By Service Type*/ 
            /*=====================*/
            IF (p_service_type_id_list is not null)    THEN 

                SET @s = CONCAT(@q, ' Where ServiceTypeId in (', p_service_type_id_list,')', @g);                      

            END IF;

            /* Filter By Para*/ 
            /*=====================*/
            IF (p_registrant_id is not null)    THEN 

                SET @s = CONCAT(@q, ' Where RegistrantId = ', p_registrant_id, @g);                         

            END IF;

            /* Filter By Student #*/ 
            /*=====================*/
            IF (p_student_name is not null)    THEN 

                SET @s = CONCAT(@q, ' Where StudentName like ','"%', p_student_name,'%"',@g);                      

            END IF;

   

            PREPARE stmt FROM @s;
            EXECUTE stmt ;
            DEALLOCATE PREPARE stmt;

            -- select @s;

        drop temporary table if exists tmp;
        drop temporary table if exists tmp_trans;


        END $$

        DELIMITER ;      
        
         