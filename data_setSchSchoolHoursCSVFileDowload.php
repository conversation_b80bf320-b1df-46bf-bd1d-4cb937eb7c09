<?php
 

 	include 'db_login.php';


	try {
	    // Connect to the database using PDO
	    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password);
 
	    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

	    // Define your query
	    $sql = "
	        SELECT 
	            a.Id AS SchoolHoursId,
	            CASE 
	                WHEN c.SchoolName IS NOT NULL THEN c.SchoolName
	                ELSE d.SchoolName
	            END AS 'School Name',   
	            CASE a.SubSchoolTypeId 
	                WHEN '0' THEN 'Elementary'
	                WHEN '1' THEN 'Middle'
	                WHEN '2' THEN 'High'
	            END AS 'School Sub-Type',  
	            b.WeekDay,
	            CASE   
	                WHEN a.StartTime THEN DATE_FORMAT(a.StartTime, '%l:%i %p') 
	                ELSE DATE_FORMAT('1900-01-01 07:00:00', '%l:%i %p')  
	            END AS StartTimeFrm,
	            CASE   
	                WHEN a.EndTime THEN DATE_FORMAT(a.EndTime, '%l:%i %p') 
	                ELSE DATE_FORMAT('1900-01-01 15:00:00', '%l:%i %p')
	            END AS EndTimeFrm,
	            CASE   
	                WHEN a.TotalHours THEN a.TotalHours 
	                ELSE '8.00'
	            END AS TotalHours
	        FROM SchSchoolHours a
	        JOIN SchSchools d ON a.SchoolId = d.Id 
	        JOIN SchSubSchools c ON a.SchoolId = c.SchoolId AND a.SubSchoolTypeId = c.SubSchoolTypeId 
	        RIGHT JOIN DaysOfWeek5 b ON a.WeekDayId = b.WeekDayId AND b.WeekDayId NOT IN (0,6)
	        ORDER BY a.SchoolId, a.SubSchoolTypeId
	    ";

	    // Execute the query
	    $stmt = $conn->prepare($sql);
	    $stmt->execute();

	    // Fetch all results
	    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

	    // Define the CSV filename
	    $filename = 'school_hours_report.csv';

	    // Set headers for file download
	    header('Content-Type: text/csv');
	    header('Content-Disposition: attachment; filename="' . $filename . '"');
	    header('Pragma: no-cache');
	    header('Expires: 0');

	    // Open PHP output stream for writing CSV data
	    $output = fopen('php://output', 'w');

	    // Write the headers to the CSV file
	    if (!empty($results)) {
	        fputcsv($output, array_keys($results[0]));
	    }

	    // Write the data to the CSV file
	    foreach ($results as $row) {
	        fputcsv($output, $row);
	    }

	    // Close the output stream
	    fclose($output);

	} catch (PDOException $e) {
	    echo "Error: " . $e->getMessage();
	}

	// Close the database connection
	$conn = null;
?>
