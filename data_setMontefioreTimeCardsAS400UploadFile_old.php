<?php


	/** Include path **/
	set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');
	
	include 'PHPExcel/IOFactory.php';


	
 	
	//====================================================
/*	
   if($ufile != none){ 
      
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../uploads/Montefiore.xls");
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}
*/	
	//====================================================	
	
	//AS400 Input File  
	//==========================================================
	$out_File = "../uploads/Montefiore_to_400.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");

	
	$inputFileType = 'Excel5';
	$inputFileName = '../uploads/Montefiore.xls';

	$objReader = new PHPExcel_Reader_Excel5();
	$objReader->setReadDataOnly(true);
	$objPHPExcel = $objReader->load($inputFileName);


	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
		$cnt = 0;	
		$linecount = 0;	
		
		foreach ($sheetData as &$row) {
 	

			$cnt++;
			
			
			$m_rec = substr(($row["A"]),0,3);
			if ($m_rec == 'A99') {
				
				$linecount++;
			
				//====================================
				// Get Monte Employee ID
				//====================================

				$ezid = substr(($row["A"]),3,4);	
			
				//====================================
				// Get Service Date
				//====================================
				
				//$serv_date = $row["D"];
				
				$date = $row["D"];

				$date = $date - 1;
				$timestamp = mktime(0,0,0,1,$date,1900);
				$serv_date = date("Y-m-d",$timestamp);
				
			
				//====================================
				// Get Shift Code
				//====================================
			
				$start_time = $row["E"];

				$date_arr = date_parse_from_format("g:i A", $start_time); 

			   switch ($date_arr[hour] ) {  		  
	  			 
					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "23") and ($date_arr[hour] <= "24")):  
					$shift_code = "1";   
					break;

					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "0") and ($date_arr[hour] < "7")):  
					$shift_code = "1";   
					break;
					
					// Employee Shift - 2 (Day)
					case (($date_arr[hour] >= "7") and ($date_arr[hour] < "15")):  
					$shift_code = "2";   
					break;

		        
					// Employee Shift - 3 (Evening)
					case (($date_arr[hour] >= "15") and ($date_arr[hour] < "23")):  
					$shift_code = "3";   
					break; 
					
					
				} 					
				
				//====================================
				// Get Hours Worked
				//====================================
				
				$hours_worked = (float)$row["G"];
				
				$client = '';
				$dept = '';			
				
				$input_dept = substr($row["L"], 5, 5);

				// Client Code - MNNO
				//==============================================
			
				if ($input_dept == '30046')  {
					$client = 'MNNO';
					$dept = '3S';
				}	
				
				if ($input_dept == '30523')  {
					$client = 'MNNO';
					$dept = 'ICU';
				}	

				if ($input_dept == '30526')   {
					$client = 'MNNO';
					$dept = 'N5N';
				}	

				if ($input_dept == '30541')  {
					$client = 'MNNO';
					$dept = 'N4S';
				}	
			

				if ($input_dept == '30542')   {
					$client = 'MNNO';
					$dept = 'N4N';
				}	

				/* 08-15-2014
				====================*/
				
				if ($input_dept == '30549')   {
					$client = 'MNNO';
					$dept = 'DIALYS';
				}	
				
				
				
				/*==================*/
				
				if ($input_dept == '30551')   {
					$client = 'MNNO';
					$dept = 'N6E';
				}	
			
				/* 03-21-2014
				====================*/
				
				if ($input_dept == '30063')   {
					$client = 'MNNO';
					$dept = 'N3D';
				}	
				
				/*==================*/
				
				
				if ($input_dept == '30556')   {
					$client = 'MNNO';
					$dept = 'N4E';
				}	

				if ($input_dept == '30559')   {
					$client = 'MNNO';
					$dept = '7S';
				}	
			
				if ($input_dept == '30562')   {
					$client = 'MNNO';
					$dept = '3E';
				}	

				if ($input_dept == '30571')   {
					$client = 'MNNO';
					$dept = 'ED';
				}	

				if ($input_dept == '30558')   {
					$client = 'MNNO';
					$dept = 'N3N';
				}	
				
			// Client Code - MNCP
			//==============================================
			
			if ($input_dept == '10521')  {
				$client = 'MNCP';
				$dept = 'F3CSIC';
			}	

			if ($input_dept == '10522')   {
				$client = 'MNCP';
				$dept = 'F3SICU';
			}	

			if ($input_dept == '10523')   {
				$client = 'MNCP';
				$dept = 'F2MICU';
			}	

			if ($input_dept == '10524')   {
				$client = 'MNCP';
				$dept = 'F2CCU';
			}	

			if ($input_dept == '10526')   {
				$client = 'MNCP';
				$dept = 'F6A';
			}	
			
			if ($input_dept == '10527')   {
				$client = 'MNCP';
				$dept = 'NW4';
			}	

			if ($input_dept == '10531')   {
				$client = 'MNCP';
				$dept = 'N7AE';
			}	

			/* 08-15-2014
			====================*/
			
			if ($input_dept == '10533')   {
				$client = 'MNCP';
				$dept = 'NURSIN';
			}	

			
			/*==================*/
			
			if ($input_dept == '10535')   {
				$client = 'MNCP';
				$dept = 'F7B';
			}	

			if ($input_dept == '10536')   {
				$client = 'MNCP';
				$dept = 'F7AW';
			}	
			
			if ($input_dept == '10540')   {
				$client = 'MNCP';
				$dept = 'NW7';
			}	

			if ($input_dept == '10541')  {
				$client = 'MNCP';
				$dept = 'NW8';
			}	

			if ($input_dept == '10542')   {
				$client = 'MNCP';
				$dept = 'NW3';
			}	

			if ($input_dept == '10544')   {
				$client = 'MNCP';
				$dept = 'K5';
			}	

			if ($input_dept == '10546')   {
				$client = 'MNCP';
				$dept = 'NW6';
			}	

			if ($input_dept == '10547')   {
				$client = 'MNCP';
				$dept = 'NW5';
			}	

			if ($input_dept == '10548')  {
				$client = 'MNCP';
				$dept = 'N6B';
			}	

			if ($input_dept == '10551')  {
				$client = 'MNCP';
				$dept = 'NW2';
			}	

			if ($input_dept == '10553')  {
				$client = 'MNCP';
				$dept = 'K4';
			}	

			if ($input_dept == '10554')   {
				$client = 'MNCP';
				$dept = 'K7';
			}	

			if ($input_dept == '10555')  {
				$client = 'MNCP';
				$dept = 'NW1';
			}	

			if ($input_dept == '10555')   {
				$client = 'MNCP';
				$dept = 'NW1';
			}	

			if ($input_dept == '10556')   {
				$client = 'MNCP';
				$dept = 'K6';
			}	
			
			if ($input_dept == '10557')   {
				$client = 'MNCP';
				$dept = 'CH10EP';
			}	

			if ($input_dept == '10559')   {
				$client = 'MNCP';
				$dept = 'K2';
			}	

			if ($input_dept == '10561')   {
				$client = 'MNCP';
				$dept = 'CH8';
			}	

			if ($input_dept == '10562')   {
				$client = 'MNCP';
				$dept = 'CH9';
			}	

			if ($input_dept == '10563')   {
				$client = 'MNCP';
				$dept = 'CH6';
			}	

			if ($input_dept == '10564')   {
				$client = 'MNCP';
				$dept = 'CH10PC';
			}	

			if ($input_dept == '10571')   {
				$client = 'MNCP';
				$dept = 'ED';
			}	

			if ($input_dept == '10572')  {
				$client = 'MNCP';
				$dept = 'EDPEDS';
			}	

			// Client Code - MNEI
			//==============================================
			
			if ($input_dept == '60046')  {
				$client = 'MNEI';
				$dept = '6S';
			}	

			if ($input_dept == '60056')  {
				$client = 'MNEI';
				$dept = '5S';
			}	

			if ($input_dept == '60063')  {
				$client = 'MNEI';
				$dept = '6N';
			}	

			if ($input_dept == '60130')  {
				$client = 'MNEI';
				$dept = '5N';
			}	

			if ($input_dept == '60500')  {
				$client = 'MNEI';
				$dept = 'NURSNG';
			}	

			if ($input_dept == '60523')  {
				$client = 'MNEI';
				$dept = '4W-ICU';
			}	

			if ($input_dept == '60524')  {
				$client = 'MNEI';
				$dept = '4W-CCU';
			}	

			if ($input_dept == '60526')  {
				$client = 'MNEI';
				$dept = '8S';
			}	
			
			if ($input_dept == '60533')  {
				$client = 'MNEI';
				$dept = '2E';
			}	

			if ($input_dept == '60536')  {
				$client = 'MNEI';
				$dept = '11N';
			}	

			if ($input_dept == '60542')  {
				$client = 'MNEI';
				$dept = '9N';
			}	

			if ($input_dept == '60543')  {
				$client = 'MNEI';
				$dept = '1S-7S';
			}	
			
			if ($input_dept == '60544')  {
				$client = 'MNEI';
				$dept = '10S';
			}	

			if ($input_dept == '60545')  {
				$client = 'MNEI';
				$dept = '7N';
			}	

			if ($input_dept == '60546')  {
				$client = 'MNEI';
				$dept = '2N';
			}	

			if ($input_dept == '60548')  {
				$client = 'MNEI';
				$dept = '8N';
			}	
			
			if ($input_dept == '60550')  {
				$client = 'MNEI';
				$dept = '9S';
			}	

			if ($input_dept == '60551')  {
				$client = 'MNEI';
				$dept = '11S';
			}	
			
			if ($input_dept == '60571')  {
				$client = 'MNEI';
				$dept = 'ED';
			}	

			if ($input_dept == '60578')  {
				$client = 'MNEI';
				$dept = 'PURPLE';
			}	
			
			//==============================================				
				//=====================================

				$client = trim($client);
				$client = str_pad($client,6, ' ', STR_PAD_RIGHT);
				$dept = str_pad($dept,6, ' ', STR_PAD_RIGHT);
				$name = str_pad($line_of_text[1],20, ' ', STR_PAD_RIGHT);
				$serv_date = date("mdY",strtotime($serv_date));
				$serv_date = str_pad($serv_date,10, ' ', STR_PAD_RIGHT);
				$shift_code = str_pad($shift_code,2, ' ', STR_PAD_RIGHT);
				$hours_worked = sprintf("%01.2f", $hours_worked);
				$hours_worked = str_replace(".", "", $hours_worked);
				$hours_worked = str_pad($hours_worked,4, '0', STR_PAD_LEFT);

			/*	
				echo (' Client: '.$client);
				echo (' Dept: '.$dept);
				echo (' Last Name: '.$name);
				echo (' Id: '.$ezid);
				echo (' Date Worked: '.$serv_date);
				echo (' Start Time: '.$start_time);
				echo (' Start Hour: '.$date_arr[hour]);
				echo (' Shift Code: '.$shift_code);
				echo (' Hours Worked: '.$hours_worked.'</br>');
			*/	
				$out_Line = $client." ".$dept." ".$ezid." ".$serv_date." ".$shift_code." ".$hours_worked." EOL"."\n";
				fwrite($fh, $out_Line);
				//echo $out_Line.'</br>';	

				$client = '';
				$dept = '';
			
			
			
			}
			
		}		 
	fclose($fh);		 


	$msg = "";
	$msg = $msg."Total Transactions To Be Uploaded: ".$linecount;


	$GLOBALS['ReturnMsg'] = "{ success: true, transactions: '{$linecount}'}";	

	//echo  "{ success: true, transactions: '{$linecount}'}";

 	

	if ($linecount > 0) {


	   	header('Content-Type: application/octet-stream');
	    header('Content-Disposition: attachment; filename='.basename($out_File));
	    header('Expires: 0');
	    header('Cache-Control: must-revalidate');
	    header('Pragma: public');
	    header('Content-Length: ' . filesize($out_File));
	    readfile($out_File);
	    exit;

	/*	  
		header('Content-Description: File Transfer');
	    header('Content-Type: application/octet-stream');
	    header('Content-Disposition: attachment; filename='.basename($out_File));
	    header('Content-Transfer-Encoding: binary');
	    header('Expires: 0');
	    header('Cache-Control: must-revalidate');
	    header('Pragma: public');
	    header('Content-Length: ' . filesize($out_File));
	    ob_clean();
	    flush();
	    readfile($out_File);

	*/    
	 	
	}
 	
 	  



	//===============================//
	
 
	
	
?>