<?php
       
/*   
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
*/   


    require_once('DB.php');
    include('db_login.php');
    

    $user_id = $_POST['UserId'];
    if (!$user_id) {
        $user_id = '1';
    }   

    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);
    $InclIds =  implode(",",$Data);


    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }


    //Portal Input Input File  
    //==========================================================

    $inputFileName = '../uploads/doe_billing_upload_input.csv';

 

    $input_file_handle = fopen($inputFileName, "r");
  
    $outputFileName = '../uploads/doe_billing_upload_output_ext_hrs.csv';
    $output_file_handle = fopen($outputFileName, "w");
     
 

    $write_heading_fl = 0;

    /*======================== Write Heading - Start ==================*/ 

 
    while (($row_xls = fgetcsv($input_file_handle)) !== FALSE) {  


            if ($write_heading_fl == 0) {

             fputcsv($output_file_handle, $row_xls);
             $write_heading_fl = 1;

            }

 

 
    }
 
    /*======================== Write Heading - End ==================*/ 

    
    /*======================== Process Selected Transactions - Start ==================*/ 
 


                        $query = " SELECT TransCSVData from  SchStudentParaUnBilledTransactions  
                                  WHERE      FIND_IN_SET(Id, '{$InclIds}')" ; 

 
                        //echo  "$query: ".$query.'</br>';         

 
                        $result = $connection->query ($query);
                        
                              
                        while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
                         
                            $trans_csv_data = $row['TransCSVData'];
                            
                            $array = explode(',', $trans_csv_data);


                            fputcsv($output_file_handle, $array);

                         

                         }  
                            
 

                        $query1 = 'UPDATE  SchStudentParaUnBilledTransactions 
                                     SET StatusId = "1"   
                                  WHERE      FIND_IN_SET(Id, "1,2,3")' ; 

 
 
                        $result1 = $connection->query ($query1);


 

    $filename = "../uploads/eWebToVPortalExtdHrsZipFile.zip";
    unlink($filename);
  

    $zip = new ZipArchive();
    $downloaded_filename = "eWebToVPortalExtdHrsZipFile.zip";

    if ($zip->open($filename, ZipArchive::CREATE)!==TRUE) {
        exit("cannot open <$filename>\n");
    }

    $zip->addFile($outputFileName, 'eWebToVPortalExtdHrsVerificationFile.csv');
    $zip->close();



    fclose($input_file_handle);
    fclose($output_file_handle);
    $connection->disconnect(); 

  
   echo  "{ success: true, query: '{$query}'}";

  

?>