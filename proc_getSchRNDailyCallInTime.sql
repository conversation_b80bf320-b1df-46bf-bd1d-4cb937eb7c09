	

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchRNDailyCallInTime$$

	CREATE PROCEDURE proc_getSchRNDailyCallInTime (IN   p_registrant_id INT,
														p_service_date DATE) 
															 

	BEGIN


			DECLARE v_MonDate, v_TueDate, v_WedDate, v_ThuDate, v_FriDate, v_PayrollWeek DATE ; 
			DECLARE v_SchoolClientId INT ; 

	IF (p_registrant_id != '')    THEN 

	 		create temporary table tmp engine=memory
			
	select   
	        a.Id as ScheduleId,
	        CONCAT(DATE_FORMAT( a.StartTime, '%l:%i %p' ),'-',(DATE_FORMAT( a.EndTime, '%l:%i %p' )),' (',a.TotalHours,' Hrs)') as ScheduleDesc,   
	        -- DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,   
	        a.ServiceDate, 
			a.RegistrantId,  
			CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
			COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)),'All') as StudentName,
			TRIM(i.SchoolName) as SchoolName,
			TRIM (DistrictName) as DistrictName,
			COALESCE(DATE_FORMAT( d.CalIInTime, '%l:%i %p' ),'Not Reported') as CalIInTime,
			d.Id as CallInTimeId,
			COALESCE(CAST(d.CallInComments as char(512)),'') as CallInComments

 	from WeeklyServices a
 	       JOIN
 	     Registrants b on a.RegistrantId = b.Id
 	       JOIN
		 RegistrantTypes f ON b.TypeId = f.Id
 	       LEFT JOIN
 	     SchStudents c on a.StudentId = c.Id 
 	       LEFTJOIN
         SchServiceTypes h ON a.ServiceTypeId = h.Id
		   JOIN 	           
		SchSchools i ON a.SchoolId = i.Id
		   JOIN
		SchDistricts j ON i.DistrictId = j.Id 
		   LEFT JOIN
		SchRNDailyCallInTimes d on a.RegistrantId = d.RegistrantId
		                       and a.ServiceDate = d.ServiceDate   

	WHERE a.RegistrantId = 	p_registrant_id
	AND   a.ServiceDate = p_service_date
		;
	
	ELSE	

	 		create temporary table tmp engine=memory
			
		select   
		        a.Id as ScheduleId,
		        CONCAT(DATE_FORMAT( a.StartTime, '%l:%i %p' ),'-',(DATE_FORMAT( a.EndTime, '%l:%i %p' )),' (',a.TotalHours,' Hrs)') as ScheduleDesc,   
		        -- DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,   
		        a.ServiceDate, 
				a.RegistrantId,  
				CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
				COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)),'All') as StudentName,
				TRIM(i.SchoolName) as SchoolName,
				TRIM (DistrictName) as DistrictName,
				COALESCE(DATE_FORMAT( d.CalIInTime, '%l:%i %p' ),'Not Reported') as CalIInTime,
				COALESCE(CAST(d.CallInComments as char(512)),'') as CallInComments

	 	from WeeklyServices a
	 	       JOIN
	 	     Registrants b on a.RegistrantId = b.Id
	 	       JOIN
			 RegistrantTypes f ON b.TypeId = f.Id
	 	       LEFT JOIN
	 	     SchStudents c on a.StudentId = c.Id 
	 	       JOIN
	         SchServiceTypes h ON a.ServiceTypeId = h.Id
			   JOIN 	           
			SchSchools i ON a.SchoolId = i.Id
			   JOIN
			SchDistricts j ON i.DistrictId = j.Id 
			   LEFT JOIN
			SchRNDailyCallInTimes d on a.RegistrantId = d.RegistrantId
			                       and a.ServiceDate = d.ServiceDate   

		WHERE   a.ServiceDate = p_service_date
		AND b.TypeId = 12;
	
	END IF;


	select  
			group_concat( ScheduleId SEPARATOR ', ' ) as ScheduleIdList,
			group_concat( ScheduleDesc SEPARATOR ', ' ) as ScheduleDesc,
			RegistrantName,
			StudentName,
			DistrictName,
			CalIInTime,
			CallInComments 

	from tmp 
       GROUP BY RegistrantId, ServiceDate
	;	


	drop temporary table if exists tmp;



	END $$

	DELIMITER ;		