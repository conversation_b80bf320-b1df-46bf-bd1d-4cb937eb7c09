<?php

 
	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php'); 

	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	$logo_image = 'doelogo.jpg';
	
	$GLOBALS['Logo'] = $logo_image;

	$GLOBALS['report_date'] = $argv[1];
	$GLOBALS['DistrictId'] = $argv[2];
	$GLOBALS['Email'] = $argv[3];

	$system_date = new DateTime($GLOBALS['report_date']);

	$GLOBALS['report_date_frm'] = $system_date->format('m/d/Y');

	 


	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "SELECT CompanyName
				FROM Company ";
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['CompanyName'] = $row['CompanyName'];
	}


 
	 
class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


	//Page header
	function Header()
	{
		
		
	
		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-25);
		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	//$pdf->AddPage();
	$pdf->SetFont('Arial','B',9);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
    

 	$query1 = "SELECT DISTINCT	c.DistrictId,
						DistrictName,
						CONCAT( trim( b.LiaisonFirstName) , ' ', trim( b.LiaisonLastName)) as LiaisonName,  
						b.Fax
				FROM 	WeeklyServices a, 
						SchDistricts b,
						SchSchools c,
						SchServiceTypes d  
					WHERE AssignmentTypeId = '1'
					AND ServiceDate = '{$GLOBALS['report_date']}'
					AND c.DistrictId =  '{$GLOBALS['DistrictId']}'    
					AND a.ScheduleStatusId  not in (2,3,4)
					AND a.ServiceTypeId = d.Id
					AND ServiceCategoryId = '0'
					AND a.SchoolId = c.Id 
					AND c.DistrictId = b.Id	";
	
	$result1 = $connection->query ($query1);

	



	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {


		$GLOBALS['DistrictId'] = $row1['DistrictId'];
		$GLOBALS['DistrictName'] = $row1['DistrictName'];
		$GLOBALS['LiaisonName'] = $row1['LiaisonName'];
		$GLOBALS['Fax'] = $row1['Fax'];



		//if ($write_header == '') {

			Custom_Header($pdf);
			//$write_header = '1';

		//}


			$query = "SELECT 	
								SchoolName, 
								 
								(SELECT COALESCE(CONCAT( trim( d.LastName) , ', ', trim( d.FirstName)  ), '')
													FROM Registrants d  
													WHERE a.Registrantid = d.Id) as RegistrantName,
								CASE a.ScheduleStatusId 
									WHEN '7' THEN 'Secured'
								ELSE 'Unsecured'
								END AS Results,
								CASE a.ScheduleStatusId 
									WHEN '7' THEN DATE_FORMAT( '{$GLOBALS['sql_today']}', '%m/%d/%Y' )
								ELSE DATE_FORMAT( a.TransDate, '%m/%d/%Y' ) 
								END AS AssignedDate,

								BoroughName,
								SchoolTypeDesc
							FROM WeeklyServices a,
								 SchDistricts b,
								 SchSchools c,
								 SchBoroughs d,
								 SchSchoolTypes e,
								 SchServiceTypes g		
							WHERE AssignmentTypeId = '1'
							AND ServiceDate = '{$GLOBALS['report_date']}'
							AND a.ScheduleStatusId  not in (2,3,4)
							AND b.Id = '{$GLOBALS['DistrictId']}'
							AND c.DistrictId = b.Id
							AND a.SchoolId = c.Id 
							AND b.BoroughId = d.Id
							AND c.SchoolTypeId = e.id
							AND a.ServiceTypeId = g.Id
							AND ServiceCategoryId = '0'";
			
			$result = $connection->query ($query);

			$i = 0;			
				
			while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) { // Students - Start

			//for ($i=0; $i < 5; $i++) { 
					# code...

				++$i;

				if ($i == 4) {

					
					$pdf->AddPage();
					//$write_header = '';
					Custom_Header($pdf);
					//$write_header = '1';

					$i = 1;

				} 


				// Line 1  - Start 

				$pos_y = $GLOBALS['y'] + 5;

				// BoroughName
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(10);
				$pdf->MultiCell(20,5,$row['BoroughName'],'L','L',0);
				
				$GLOBALS['y']=$pdf->GetY();
						

				// District
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(30);
				$pdf->MultiCell(30,5,$GLOBALS['DistrictName'],'L','L',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// School
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(60);
				$pdf->MultiCell(50,5,$row['SchoolName'],'L','L',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// School Type
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(110);
				$pdf->MultiCell(25,5,$row['SchoolTypeDesc'],'L','L',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Nurse
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(135);
				$pdf->MultiCell(50,5,$row['RegistrantName'],'L','L',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Date Assigned
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(185);
				$pdf->MultiCell(25,5,$row['AssignedDate'],'L','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y;
				}


				// Results
				//==============

				$pdf->SetY($pos_y);
				$pdf->SetX(210);
				$pdf->MultiCell(20,5,$row['Results'],'LR','C',0);

				$curr_y = $GLOBALS['y'];
				$curr_y=$pdf->GetY();

				if ($curr_y > $GLOBALS['y']) {

					$GLOBALS['y'] = $curr_y; 
				}



				$pdf->Line(10, $y+1 , 230, $GLOBALS['y']+1);





				$pdf->Ln(1);

			//}	
		


		} 	// Students - End

	//=============	

// email stuff (change data below)
$to = $GLOBALS['Email']; 
$from = "<EMAIL>"; 
$subject = "School Long Term Daily Report for: ".$GLOBALS['report_date_frm']; 
$message = '<p>Attached to this email is the report for School District: '.$GLOBALS['DistrictName'].'</p>';

// a random hash will be necessary to send mixed content
$separator = md5(time());

// carriage return type (we use a PHP end of line constant)
$eol = PHP_EOL;

// attachment name
$filename = 'DailySchoolLongTermReport-'.$GLOBALS['DistrictName'].'.pdf';

// encode data (puts attachment in proper format)
$pdfdoc = $pdf->Output("", "S");
$attachment = chunk_split(base64_encode($pdfdoc));

// main header
$headers  = "From: ".$from.$eol;
$headers .= "MIME-Version: 1.0".$eol; 
$headers .= "Content-Type: multipart/mixed; boundary=\"".$separator."\"";

// no more headers after this, we start the body! //

$body = "--".$separator.$eol;
$body .= "Content-Transfer-Encoding: 7bit".$eol.$eol;
$body .= "Daily School District Long Term Report.".$eol;

// message
$body .= "--".$separator.$eol;
$body .= "Content-Type: text/html; charset=\"iso-8859-1\"".$eol;
$body .= "Content-Transfer-Encoding: 8bit".$eol.$eol;
$body .= $message.$eol;

// attachment
$body .= "--".$separator.$eol;
$body .= "Content-Type: application/octet-stream; name=\"".$filename."\"".$eol; 
$body .= "Content-Transfer-Encoding: base64".$eol;
$body .= "Content-Disposition: attachment".$eol.$eol;
$body .= $attachment.$eol;
$body .= "--".$separator."--";

// send message


mail($to, $subject, $body, $headers);

echo 'End of PHP';

	//=============
	}	
	   


	function Custom_Header($pdf)
	{
		

		$pdf->AddPage();


		$pdf->SetY(10.00125);
		$pdf->SetX(10.00125);


		$pdf->Image($GLOBALS['Logo'],20,8,50);


	 	
		$pdf->Ln(2);
		$pdf->Cell(70,4,'',0,0,'L');

		$pdf->SetFont('Arial','B',18);
		$pdf->Cell(100,6,'NEW YORK DEPARTMENT OF',0,1,'L');
		$pdf->Cell(70,4,'',0,0,'L');
		$pdf->Cell(100,6,'HEALTH AND MENTAL HYGIENE',0,1,'L');

		$pdf->SetFont('Times','B',14);

		$pdf->Ln(30);
		$pdf->Cell(40,6,'REPORT TYPE: SCHOOLS (LONG TERM) DAILY SERVICES',0,1,'L');

		$pdf->Ln(10);
		$pdf->Cell(100,6,'Company: '.$GLOBALS['CompanyName'],0,0,'L');
		$pdf->Cell(40,6,'Date: '.$GLOBALS['report_date_frm'],0,1,'L');
		$pdf->Cell(100,6,'District Liaison: '.$GLOBALS['LiaisonName'],0,0,'L');
		$pdf->Cell(40,6,'Fax: '.$GLOBALS['Fax'],0,1,'L');



		$pdf->Ln(10);
		//Set Table Header
		$pdf->SetFont('Times','B',10);
		$pdf->Cell(20,5,'Borough',1,0,'C');
		$pdf->Cell(30,5,'District',1,0,'C');
		$pdf->Cell(50,5,'School',1,0,'C');
		$pdf->Cell(25,5,'School Type',1,0,'C');
		$pdf->Cell(50,5,'Nurse',1,0,'C');
		$pdf->Cell(25,5,'Date Assigned',1,0,'C');
		$pdf->Cell(20,5,'Results',1,0,'C');
		$pdf->Ln(1);
 

		$y = $pdf->GetY();

	
		$GLOBALS['y'] = $y;


	
		$pdf->Ln(10);
	 	

		
	}

	//+++++++++++++++++++++++++++++++++++++++++++++++++
	 
	
	//$pdf->Output();
	 
	$connection->disconnect();
	echo 'End Of PHP';
	 	
?>
