

<?php


 /*
file_put_contents('../uploads/vizient_to_400.txt',
    preg_replace(
        '~[\r\n]+~',
        "\r\n",
        trim(file_get_contents('../uploads/vizient_to_400_1.txt'))
    )
);                  
*/

copy('../uploads/vizient_to_400.txt','../uploads/vizient_to_400_1.txt');

$out_File = "../uploads/vizient_to_400_1.txt";
$fh = fopen($out_File, 'w') or die("can't open file 1");


$in_File = fopen("../uploads/vizient_to_400.txt", "r") or die("Unable to open file!");
// Output one line until end-of-file




while(!feof($in_File)) {
  
  $f_line = fgets($in_File);	  
  if ($f_line) {

	  $i++;	
	  echo  $i.' - ' . $f_line . "<br>";
	  fwrite($fh, $f_line);	

  }

}


fclose($in_File);
fclose($out_File);


?>

  