<?php
	/** Error reporting */
  	
	ini_set('memory_limit', '-1');
 
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
   

	require_once('DB.php');
	include('db_login.php');

 
	include('../../phpexcel-1-8/Classes/PHPExcel.php');

 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


    $Month = $_GET['Month'];
    $Year = $_GET['Year'];
    $SchoolId = $_GET['SchoolId'];
    $RegistrantId = $_GET['RegistrantId'];
  

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


 


	$objPHPExcel = new PHPExcel();

	 
	$objPHPExcel->setActiveSheetIndex(0);
	$objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Provider Name');
	$objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Session Date');
	$objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Start Time');
	$objPHPExcel->getActiveSheet()->SetCellValue('D1', 'End Time');
	$objPHPExcel->getActiveSheet()->SetCellValue('E1', 'Mandate Desc.');
	$objPHPExcel->getActiveSheet()->SetCellValue('F1', 'Delivery Mode');
	$objPHPExcel->getActiveSheet()->SetCellValue('G1', 'Contract');
	
	$objPHPExcel->getActiveSheet()->SetCellValue('H1', 'Bill Amount');
	$objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Student Name');
	$objPHPExcel->getActiveSheet()->SetCellValue('J1', 'School');
	$objPHPExcel->getActiveSheet()->SetCellValue('K1', 'School DBN');
	$objPHPExcel->getActiveSheet()->SetCellValue('L1', 'Service Type');
	$objPHPExcel->getActiveSheet()->SetCellValue('M1', 'Comments');


 


	$query = "call proc_getSchRegistrantsUnBilledSessions ( '{$Month}', '{$Year}', '{$SchoolId}', '{$RegistrantId}'  )";      



	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 1;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$linecount++;


            $row_num++;

            $SchoolName = $connection->escapeSimple($row['SchoolName']); 
            $SchoolNameDBN = $connection->escapeSimple($row['SchoolNameDBN']); 

         
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['RegistrantName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['ServiceDate']);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['StartTime']);
		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['EndTime']);
		    $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['MandateDesc']);

		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $row['SessionDeliveryModeDesc']);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['BillingContractDesc']);

		    $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['BillAmount']);
		    $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['StudentName']);
		    
		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $SchoolName);
		    $objPHPExcel->getActiveSheet()->setCellValue('K'.$row_num, $SchoolNameDBN);

		    $objPHPExcel->getActiveSheet()->setCellValue('L'.$row_num, $row['ServiceTypeDesc']);
 

 

	}	


		$connection->disconnect();
 


 

	/*=========================================================================*/

// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";

$SheetName = 'Un-Billed Sessions';
$objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/eweb_unbilled_sessions_listing.xlsx";

$objWriter->save($out_File);

/*==============================*/
   $DownloadedFileName = 'eWebStaffing-UnbilledSessionsFile-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    //unlink($out_File);    

    exit;


/*==============================*/



// Echo done
//echo  "{ success: true, transactions: '{$linecount}'}";
//echo  $query4  ;

   

?>