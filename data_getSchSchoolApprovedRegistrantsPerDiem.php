	<?php 
	
  require_once("db_GetSetData.php");

  $conn = getCon();

  
  $RegistrantId = $_GET['RegistrantId'];
  $ServiceDate = $_GET['ServiceDate'];
  $StartTime = $_GET['StartTime'];

  
  if ($RegistrantId != 0) {
  
	  $query = "SELECT 
	    a.Id AS id,
	    a.Id AS RegistrantId,
	    CONCAT(TRIM(a.LastName),
	            ', ',
	            TRIM(a.FirstName),
	            ' (',
	            RegistrantTypeDesc,
	            ')') AS RegistrantName
	FROM
	    Registrants a,
	    RegistrantTypes f
	WHERE
	    a.TypeId = f.Id 
	    AND a.TypeId = 12
			   
			ORDER BY a.LastName , a.FirstName
  "; 


  } else {

	  $query = "SELECT 
	    a.Id AS id,
	    a.Id AS RegistrantId,
	    CONCAT(TRIM(a.LastName),
	            ', ',
	            TRIM(a.FirstName),
	            ' (',
	            RegistrantTypeDesc,
	            ')') AS RegistrantName
	FROM
	    Registrants a,
	    RegistrantTypes f
	WHERE
	    a.TypeId = f.Id 
	    AND a.TypeId = 12
			and not exists (
			  select 1 from  WeeklyServices e 
			    where a.Id = e.RegistrantId
			    and e.ScheduleStatusId > 6
			    and e.ServiceDate = '{$ServiceDate}'  
			    AND '{$StartTime}' >= e.StartTime 
			    and '{$StartTime}' < e.EndTime 
			)        
	        
	ORDER BY a.LastName , a.FirstName
  "; 


  }



  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;

  
?>
