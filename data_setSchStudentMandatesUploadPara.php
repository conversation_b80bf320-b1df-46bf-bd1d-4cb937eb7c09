  
<?php
    
    ini_set("memory_limit","-1");
 
/* 
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
 
*/ 

	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	//==============
	// Billing Contract Id

	$billing_contract_id = $_POST['BillingContractId'];
	if (!$billing_contract_id) {
		$billing_contract_id = '1';
	}	

	//==============
	// School Season Id

	$school_season_id = $_POST['SchoolYearId'];


	//===============

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
		$query = "SELECT SchoolSeasonEndDate
			FROM SchSchoolYear			 
			WHERE Id = '{$school_season_id}'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$EndDate = $row['SchoolSeasonEndDate'];

	}	
	 
	/* Upload New File 
	 =============================================*/
		

	$inputFileName = '../uploads/mandates.csv';

  
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    
	    Return ; 

	}
   
 
	$file_handle = fopen("../uploads/mandates.csv", "r");
	
	ini_set("auto_detect_line_endings", true);

	$cnt = 0;	
	$linecount = 0;	

   while (($row = fgetcsv($file_handle)) !== FALSE) { // while - start

   			$cnt++;


			$m_rec = substr(($row[4]),0,1);

			if ((($m_rec == '1') || ($m_rec == '2') || ($m_rec == '8')) && (strstr($row[18], 'Para - '))) { // m_rec/Para Only- Start


				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = trim($row["30"]);


				/*======= Test Mandate Type - Start =======*/
				$mandate_type = '';


				if ($mandate_status_desc == 'Receiving') {  

					$mandate_type = 'rcv'; //  
				}

				if (strstr($mandate_status_desc, 'Awaiting') || ($mandate_status_desc == 'Reason for Delay Needed')) {  

					$mandate_type = 'awt'; // Mand Status
				}


				if ($mandate_type != '') { // Mandate Status Defined - Start	

				/*======= Test Mandate Type - End =======*/


				/* Student External ID
				 ===================*/

				$student_ext_id = $row[4];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row[5];
				$student_last_name = $connection->escapeSimple($student_last_name); 

				/* Student First Name
				 ===================*/

				$student_first_name = $row[6];
				$student_first_name = $connection->escapeSimple($student_first_name); 


				/* Student DOB
				============================= */
				
     			$dob_str = date("Y-m-d", strtotime($row[11]));
			 	$diff = abs(strtotime($dob_str) - strtotime(date("Y-m-d")));
				$years = floor($diff / (365*60*60*24));


				$sub_school_type_id = 0;

				if ($years >= 11 && $years <= 13) {

					$sub_school_type_id = 1;

				}

				if ($years > 13) {

					$sub_school_type_id = 2;

				}




				/* Student School ID
				 ===================*/

				$school_ext_id = '%'.$row[12].'%';
				$mandate_school_id = $row[12];

 				$school_id = 0;
				
 				if ($mandate_school_id) {


					$query_school_id = " SELECT Id as SchoolId 
								from SchSchools 
								  WHERE ExtId != '' 
								  AND ExtId like '{$school_ext_id}' LIMIT 1	 
					";
					
					$result_school_id = $connection->query ($query_school_id);
					if (DB::isError($result_school_id)){
				                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_id));
				    }

					while ($row_school_id =& $result_school_id->fetchRow (DB_FETCHMODE_ASSOC)) {
						$school_id = $row_school_id['SchoolId'];
	 

					}	


 				} else {

 					$mandate_school_id = 0;

 				}




				/* Student Service Type Desc
				 ================================*/

				$mandate_serv_type_desc = $row[18];

				//==================================
				// Get eWeb Service Type 
				//==================================
			 	
				$service_type_id = ''; 

				$query_ser_id = " SELECT  	Id as ServiceTypeId, 
									        SESISServiceDurType,
									        DOEServiceTypeIndId,
              								DOEServiceTypeGrpId,
              								SESISParaServiceTypeId  
						   FROM SchServiceTypes	
   						where SESISServiceTypeDesc  like '%{$mandate_serv_type_desc}%' LIMIT 1 	 
				";
				
				$result_ser_id = $connection->query ($query_ser_id);
				if (DB::isError($result)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
			    }

				while ($row_ser_id =& $result_ser_id->fetchRow (DB_FETCHMODE_ASSOC)) {
					$service_type_id = $row_ser_id['ServiceTypeId'];
					$service_dur_type = $row_ser_id['SESISServiceDurType'];
					$service_type_ind = $row_ser_id['DOEServiceTypeIndId'];
					$service_type_grp = $row_ser_id['DOEServiceTypeGrpId'];
					$sesis_para_service_type_id = $row_ser_id['SESISParaServiceTypeId'];


				}	



				/*================================*/

				/* Student Language
				 ================================*/

				$student_language = $row[20];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row[21];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row[22];


				/* Mandate Service Freq
				 ================================*/

				$mandate_serv_freq = $row[23];
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Duration
				 ================================*/




				$mandate_serv_dur = $row[24];
				$mandate_serv_dur = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);
				$para_transport_perc = $mandate_serv_dur;

				/* Service Duration for PARAs
                 ============================*/

 
				$mandate_serv_dur_num =  8  * ($mandate_serv_dur / 100); // Save in Student Mandate
				$mandate_serv_dur_min = $mandate_serv_dur_num * 60;
				$para_fl = '1';

 				 


				/*===============================*/

				/* Mandate Service Start/First Attend Date Date
				============================= */
							
    			
				if ($mandate_type == 'rcv') {

	    			$mandate_start_date = date("Y-m-d", strtotime($row[36]));
    				$mandate_first_attend_date = $mandate_start_date;


				} else {

	    			$mandate_start_date = date("Y-m-d", strtotime($row[34]));
    				$mandate_first_attend_date = $mandate_start_date;


				}

 
				/* Provider Name
				 ================================*/

				
				$mandate_provider_name = $row[28];

				//==================================
				// Get Provider (Registrant)  Id
				//==================================
			 	
				$registrant_id = '0'; 

				
				if ($mandate_provider_name) {


					$mandate_provider_name = $connection->escapeSimple($mandate_provider_name); 

					$query_reg_id = "SELECT COALESCE((SELECT Id from Registrants 
								  WHERE  UPPER(Concat(TRIM(FirstName), ' ', TRIM(LastName)))  LIKE UPPER('%{$mandate_provider_name}%') LIMIT 1),0)   as RegistrantId 
					";
					
					$result_reg_id = $connection->query ($query_reg_id);
					if (DB::isError($result_reg_id)){
				                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
				    }

					while ($row_reg_id =& $result_reg_id->fetchRow (DB_FETCHMODE_ASSOC)) {
						$registrant_id = $row_reg_id['RegistrantId'];


					}	


				}

 
				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = trim($row["30"]);



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query5 = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result5 = $connection->query ($query5);

				if (DB::isError($result5)){
					die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result5->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT IGNORE INTO SchStudents
						(ExtId, 
						 SearchId, 
						 SubSchoolTypeId,
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 	
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'{$sub_school_type_id}',
							'1',
							'{$school_id}',
							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
				 } 

				 else {


				 $query1 = "UPDATE SchStudents   
					        set SchoolId =  '{$school_id}',
					            SubSchoolTypeId = '{$sub_school_type_id}'
					      
					      WHERE ExtId = trim('{$student_ext_id}')
					      and  ((SchoolId !=  '{$school_id}') ||  (SubSchoolTypeId !=  '{$sub_school_type_id}'))
					       
					          "; 		

				}	          

					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}

 
				 




				// End 1	


			//================================ 
			//  Check if Mandate Already Exists  
			//================================ 
		 	

			
			if (strlen($mandate_first_attend_date) > 10) {

				$mandate_first_attend_date = substr($mandate_first_attend_date,0,10);	

			}

	 
				$query4 =  "SELECT 	a.Id as MandateId,
									a.RegistrantId, 
									a.AssignmentGeneratedFL,
									a.StartDate,
									a.EndDate 
							FROM SchStudentMandates  a, SchSchoolYear b
						WHERE  a.StudentExtId = '{$student_ext_id}'
 	 					AND  a.DOEServiceTypeDesc like '{$mandate_serv_type_desc}'		 					
 	                    AND a.StatusId = '1' 
                        AND '{$mandate_start_date}' between SchoolSeasonStartDate and SchoolSeasonEndDate
                        AND  a.StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate

	                 ORDER BY a.StartDate DESC LIMIT 1
	                    ";


 

 			$result4 = $connection->query ($query4);

		 	
			if (DB::isError($result4)){
						die("Could not query the database:<br />$query4 ".DB::errorMessage($result));
			}			
		 	
			$mandate_exists =& $result4->numRows();			
		 

			/*====== Check for if Mandate has to be "Cloned" ======*/
		 	if (($mandate_exists == '1') && ($mandate_type == 'rcv')) { 

				while ($row_clone =& $result4->fetchRow (DB_FETCHMODE_ASSOC)) {
					$curr_mandate_id = $row_clone['MandateId'];
					$curr_registrant_id = $row_clone['RegistrantId'];
					$curr_assign_gen_fl = $row_clone['AssignmentGeneratedFL'];
					$curr_start_date = $row_clone['StartDate'];
					$curr_end_date = $row_clone['EndDate'];


				}	

				/*======= Clone Mandate =======*/
				if (($curr_assign_gen_fl == '1') && ($registrant_id != '0') && ($registrant_id !=  $curr_registrant_id) && ($mandate_start_date > $curr_start_date)) {


				    $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchStudentParaMandateClone.php?MandateId=".$curr_mandate_id;
				    $url = $url.'&RegistrantId='.$registrant_id.'&StartDate='.$mandate_start_date.'&EndDate='.$EndDate;
				    
				    //$output = file_get_contents($url);
					$curl = curl_init();
				   
				    curl_setopt($curl, CURLOPT_URL, $url);
				    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
				    curl_setopt($curl, CURLOPT_HEADER, false);

				    // execute and return string (this should be an empty string '')
				    $str = curl_exec($curl);

				    //echo '$str :'.$str;
				    
				    curl_close($curl);


				}

		 	}
 			/*=====================================================*/



			/*====== Mandate Search ID ======*/
			$search_id  = rand();


			/*==========  Get School Hours for for Para (Non-Transportation) type Services  ===========*/		

		  	
			if ($sesis_para_service_type_id == '1') { // Non-Transportation Para Mandate Default Hours - Start 


  

				/*=== Mon Hours - Start ===*/
				
			 
				$query_school_hrs = "CALL proc_setSchParaAdjustedHours ('{$school_id}', 
																		'{$sub_school_type_id}',
																		'1',
																		'{$para_transport_perc}'
  										 								)";  
  
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_mon1 = $row_school_hrs['SchoolStartTimeAM'];	
					$para_end_time_mon1 = $row_school_hrs['SchoolEndTimeAM'];	
					$para_total_hours_mon1 = $row_school_hrs['SchoolHoursAM'];	
					$para_start_time_mon2 = $row_school_hrs['SchoolStartTimePM'];	
					$para_end_time_mon2 = $row_school_hrs['SchoolEndTimePM'];	
					$para_total_hours_mon2 = $row_school_hrs['SchoolHoursPM'];	


				}	

				$result_school_hrs->free();
				$connection->disconnect();


			$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    if (DB::isError($connection)){
				$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    }

				/*=== Mon Hours - End ===*/

				/*=== Tue Hours - Start ===*/
 

				$query_school_hrs = "CALL proc_setSchParaAdjustedHours ('{$school_id}', 
																		'{$sub_school_type_id}',
																		'2',
																		'{$para_transport_perc}' 
  										 								)";  

				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_tue1 = $row_school_hrs['SchoolStartTimeAM'];	
					$para_end_time_tue1 = $row_school_hrs['SchoolEndTimeAM'];	
					$para_total_hours_tue1 = $row_school_hrs['SchoolHoursAM'];	
					$para_start_time_tue2 = $row_school_hrs['SchoolStartTimePM'];	
					$para_end_time_tue2 = $row_school_hrs['SchoolEndTimePM'];	
					$para_total_hours_tue2 = $row_school_hrs['SchoolHoursPM'];	

				}	

				$result_school_hrs->free();
				$connection->disconnect();


			$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    if (DB::isError($connection)){
				$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    }

				/*=== Tue Hours - End ===*/

				/*=== Wed Hours - Start ===*/
 
				$query_school_hrs = "CALL proc_setSchParaAdjustedHours ('{$school_id}', 
																		'{$sub_school_type_id}',
																		'3',
																		'{$para_transport_perc}'
  										 								)";  

				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_wed1 = $row_school_hrs['SchoolStartTimeAM'];	
					$para_end_time_wed1 = $row_school_hrs['SchoolEndTimeAM'];	
					$para_total_hours_wed1 = $row_school_hrs['SchoolHoursAM'];	
					$para_start_time_wed2 = $row_school_hrs['SchoolStartTimePM'];	
					$para_end_time_wed2 = $row_school_hrs['SchoolEndTimePM'];	
					$para_total_hours_wed2 = $row_school_hrs['SchoolHoursPM'];	

				}	

				$result_school_hrs->free();
				$connection->disconnect();


			$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    if (DB::isError($connection)){
				$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    }

				/*=== Wed Hours - End ===*/

				/*=== Thu Hours - Start ===*/
 
				$query_school_hrs = "CALL proc_setSchParaAdjustedHours ('{$school_id}', 
																		'{$sub_school_type_id}',
																		'4',
																		'{$para_transport_perc}'
  										 								)";  

				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_thu1 = $row_school_hrs['SchoolStartTimeAM'];	
					$para_end_time_thu1 = $row_school_hrs['SchoolEndTimeAM'];	
					$para_total_hours_thu1 = $row_school_hrs['SchoolHoursAM'];	
					$para_start_time_thu2 = $row_school_hrs['SchoolStartTimePM'];	
					$para_end_time_thu2 = $row_school_hrs['SchoolEndTimePM'];	
					$para_total_hours_thu2 = $row_school_hrs['SchoolHoursPM'];	


				}	

				$result_school_hrs->free();
				$connection->disconnect();


			$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    if (DB::isError($connection)){
				$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    }

				/*=== Thu Hours - End ===*/

				/*=== Fri Hours - Start ===*/
 
				$query_school_hrs = "CALL proc_setSchParaAdjustedHours ('{$school_id}', 
																		'{$sub_school_type_id}',
																		'5',
																		'{$para_transport_perc}'
  										 								)";  

				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_fri1 = $row_school_hrs['SchoolStartTimeAM'];	
					$para_end_time_fri1 = $row_school_hrs['SchoolEndTimeAM'];	
					$para_total_hours_fri1 = $row_school_hrs['SchoolHoursAM'];	
					$para_start_time_fri2 = $row_school_hrs['SchoolStartTimePM'];	
					$para_end_time_fri2 = $row_school_hrs['SchoolEndTimePM'];	
					$para_total_hours_fri2 = $row_school_hrs['SchoolHoursPM'];	


				}	

				$result_school_hrs->free();
				$connection->disconnect();


				/*=== Fri Hours - End ===*/


			$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    if (DB::isError($connection)){
				$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
		    }

				/*=== Fri Hours - End ===*/


			$query2 = "INSERT INTO SchStudentMandates
						(	
							SearchId,
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId, 
							DOEServiceTypeDesc,
							DOEFirstAttendDate,
							DOEStartDate,
							DOEProvider,
							DOESchoolId,
							BillingContractId,
							ParaTransportPerc,

					       ParaStartTimeMon1,
					       ParaEndTimeMon1,
					       ParaTotalHoursMon1,
					       ParaStartTimeTue1,
					       ParaEndTimeTue1,
					       ParaTotalHoursTue1,
			           	   ParaStartTimeWed1,
			               ParaEndTimeWed1,
			               ParaTotalHoursWed1,
			               ParaStartTimeThu1,
			               ParaEndTimeThu1,
			               ParaTotalHoursThu1,
			               ParaStartTimeFri1,
			               ParaEndTimeFri1,
			               ParaTotalHoursFri1,


				           ParaStartTimeMon2,
					       ParaEndTimeMon2,
					       ParaTotalHoursMon2,
					       ParaStartTimeTue2,
					       ParaEndTimeTue2,
					       ParaTotalHoursTue2,
			           	   ParaStartTimeWed2,
			               ParaEndTimeWed2,
			               ParaTotalHoursWed2,
			               ParaStartTimeThu2,
			               ParaEndTimeThu2,
			               ParaTotalHoursThu2,
			               ParaStartTimeFri2,
			               ParaEndTimeFri2,
			               ParaTotalHoursFri2,

							UserId,
							TransDate
						) 

						VALUES (
						    '{$search_id}',
						 	'0',
							'{$student_ext_id}',
 							'{$school_id}',
							'1',
							'{$service_type_id}',

							'{$registrant_id}',
							
							'{$mandate_first_attend_date}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN '{$service_type_ind}'
								ELSE '{$service_type_grp}'
							END,
							'{$mandate_serv_type_desc}',
							'{$mandate_first_attend_date}',
							'{$mandate_start_date}',
							
							'{$mandate_provider_name}',
							'{$mandate_school_id}',
							'{$billing_contract_id}',
							'{$para_transport_perc}',

							'{$para_start_time_mon1}',
							'{$para_end_time_mon1}',
							'{$para_total_hours_mon1}',


							'{$para_start_time_tue1}',
							'{$para_end_time_tue1}',
							'{$para_total_hours_tue1}',

							'{$para_start_time_wed1}',
							'{$para_end_time_wed1}',
							'{$para_total_hours_wed1}',

							'{$para_start_time_thu1}',
							'{$para_end_time_thu1}',
							'{$para_total_hours_thu1}',

							'{$para_start_time_fri1}',
							'{$para_end_time_fri1}',
							'{$para_total_hours_fri1}',


							'{$para_start_time_mon2}',
							'{$para_end_time_mon2}',
							'{$para_total_hours_mon2}',


							'{$para_start_time_tue2}',
							'{$para_end_time_tue2}',
							'{$para_total_hours_tue2}',

							'{$para_start_time_wed2}',
							'{$para_end_time_wed2}',
							'{$para_total_hours_wed2}',

							'{$para_start_time_thu2}',
							'{$para_end_time_thu2}',
							'{$para_total_hours_thu2}',

							'{$para_start_time_fri2}',
							'{$para_end_time_fri2}',
							'{$para_total_hours_fri2}',

							'{$user_id}',
							now()  

							)";


 			} // Non-Transportation Para Manndate Default Hours - End


			if ($sesis_para_service_type_id == '2') { // Transportation Para Manndate Default Hours - Start 


				if ($para_transport_perc == '20') {

					$to_school_start = '06:00:00';
					$to_school_end = '08:00:00';
					$to_school_hrs = '2.0';

					$from_school_start = '15:00:00';
					$from_school_end = '17:00:00';
					$from_school_hrs = '2.0';

				} else {

					$to_school_start = '06:00:00';
					$to_school_end = '12:00:00';
					$to_school_hrs = '6.0';

					$from_school_start = '13:00:00';
					$from_school_end = '17:00:00';
					$from_school_hrs = '4.0';


				}	

				/*====== Mon Transportation Hours ======*/
				$para_start_time_mon1 = $to_school_start;	
				$para_end_time_mon1 = $to_school_end;	
				$para_total_hours_mon1 = $to_school_hrs;	

				$para_start_time_mon2 = $from_school_start;	
				$para_end_time_mon2 = $from_school_end;	
				$para_total_hours_mon2 = $from_school_hrs;	


		        /*====== Tue Transportation Hours ======*/
		        $para_start_time_tue1 = $to_school_start; 
		        $para_end_time_tue1 = $to_school_end; 
		        $para_total_hours_tue1 = $to_school_hrs; 

		        $para_start_time_tue2 = $from_school_start; 
		        $para_end_time_tue2 = $from_school_end; 
		        $para_total_hours_tue2 = $from_school_hrs; 


		        /*====== Wed Transportation Hours ======*/
		        $para_start_time_wed1 = $to_school_start; 
		        $para_end_time_wed1 = $to_school_end; 
		        $para_total_hours_wed1 = $to_school_hrs; 

		        $para_start_time_wed2 = $from_school_start; 
		        $para_end_time_wed2 = $from_school_end; 
		        $para_total_hours_wed2 = $from_school_hrs; 


		        /*====== Thu Transportation Hours ======*/
		        $para_start_time_thu1 = $to_school_start; 
		        $para_end_time_thu1 = $to_school_end; 
		        $para_total_hours_thu1 = $to_school_hrs; 

		        $para_start_time_thu2 = $from_school_start; 
		        $para_end_time_thu2 = $from_school_end; 
		        $para_total_hours_thu2 = $from_school_hrs; 


		        /*====== Fri Transportation Hours ======*/
		        $para_start_time_fri1 = $to_school_start; 
		        $para_end_time_fri1 = $to_school_end; 
		        $para_total_hours_fri1 = $to_school_hrs; 

		        $para_start_time_fri2 = $from_school_start; 
		        $para_end_time_fri2 = $from_school_end; 
		        $para_total_hours_fri2 = $from_school_hrs; 


			$query2 = "INSERT INTO SchStudentMandates
						(	SearchId,
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId, 
							DOEServiceTypeDesc,
							DOEFirstAttendDate,
							DOEStartDate,
							DOEProvider,
							DOESchoolId,
							BillingContractId,
							ParaTransportPerc,

					       ParaStartTimeMon1,
					       ParaEndTimeMon1,
					       ParaTotalHoursMon1,
					       ParaStartTimeTue1,
					       ParaEndTimeTue1,
					       ParaTotalHoursTue1,
			           	   ParaStartTimeWed1,
			               ParaEndTimeWed1,
			               ParaTotalHoursWed1,
			               ParaStartTimeThu1,
			               ParaEndTimeThu1,
			               ParaTotalHoursThu1,
			               ParaStartTimeFri1,
			               ParaEndTimeFri1,
			               ParaTotalHoursFri1,


			                 ParaStartTimeMon2,
			                 ParaEndTimeMon2,
			                 ParaTotalHoursMon2,
			                 ParaStartTimeTue2,
			                 ParaEndTimeTue2,
			                 ParaTotalHoursTue2,
		                     ParaStartTimeWed2,
		                     ParaEndTimeWed2,
		                     ParaTotalHoursWed2,
		                     ParaStartTimeThu2,
		                     ParaEndTimeThu2,
		                     ParaTotalHoursThu2,
		                     ParaStartTimeFri2,
		                     ParaEndTimeFri2,
		                     ParaTotalHoursFri2,

							UserId,
							TransDate
						) 

						VALUES (
							 '{$search_id}',
						 	'0',
							'{$student_ext_id}',
 							'{$school_id}',
							'1',
							'{$service_type_id}',

							'{$registrant_id}',
							
							'{$mandate_first_attend_date}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN '{$service_type_ind}'
								ELSE '{$service_type_grp}'
							END,
							'{$mandate_serv_type_desc}',
							'{$mandate_first_attend_date}',
							'{$mandate_start_date}',
							
							'{$mandate_provider_name}',
							'{$mandate_school_id}',
							'{$billing_contract_id}',
							'{$para_transport_perc}',

							'{$para_start_time_mon1}',
							'{$para_end_time_mon1}',
							'{$para_total_hours_mon1}',


							'{$para_start_time_tue1}',
							'{$para_end_time_tue1}',
							'{$para_total_hours_tue1}',

							'{$para_start_time_wed1}',
							'{$para_end_time_wed1}',
							'{$para_total_hours_wed1}',

							'{$para_start_time_thu1}',
							'{$para_end_time_thu1}',
							'{$para_total_hours_thu1}',

							'{$para_start_time_fri1}',
							'{$para_end_time_fri1}',
							'{$para_total_hours_fri1}',

                            '{$para_start_time_mon2}',
                            '{$para_end_time_mon2}',
                            '{$para_total_hours_mon2}',


                            '{$para_start_time_tue2}',
                            '{$para_end_time_tue2}',
                            '{$para_total_hours_tue2}',

                            '{$para_start_time_wed2}',
                            '{$para_end_time_wed2}',
                            '{$para_total_hours_wed2}',

                            '{$para_start_time_thu2}',
                            '{$para_end_time_thu2}',
                            '{$para_total_hours_thu2}',

                            '{$para_start_time_fri2}',
                            '{$para_end_time_fri2}',
                            '{$para_total_hours_fri2}',


							'{$user_id}',
							now()  

							)";




 			} // Transportation Para Mandate Default Hours - End
			
 		 
			//================================================
			// Generate  Mandates 
			//================================================
			

			if (($mandate_exists == 0) && ($mandate_type != '') && ($service_type_id))  { // Mandate not exists - Start
			 


						
						
						$result2 = $connection->query($query2);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			

						/*======  If Registrant ID ASSIGNED======*/

						
					 	

						if ($registrant_id != 0) {

							/*====== Get New Mandate ID======*/

						 	$query_mand_id = " SELECT  	a.Id as MandateId,
						 	                            b.Id as StudentId  
												   FROM SchStudentMandates a, SchStudents b	
	   										  	WHERE a.SearchId  =   '{$search_id}'  
	   										  	AND   b.Extid = '{$student_ext_id}'  	 
							";
					  
							$result_mand_id = $connection->query($query_mand_id);
							if (DB::isError($result_mand_id)){
						                die("Could not query the database(NEW MANDATE):<br />$query_mand_id ".DB::errorMessage($result_mand_id));
						    }
					 
					 
							while ($row_mand_id =& $result_mand_id->fetchRow (DB_FETCHMODE_ASSOC)) {
									$mandate_id = $row_mand_id['MandateId'];
									$student_id = $row_mand_id['StudentId'];

							}	
					 
							/*====== Create Weekly Assignments for New Mandate======*/

							if ($mandate_type == 'rcv')  { // Mandate not exists - Start

					 		
				                 $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchGenerateStudentAssignmentsFromMandate.php?MandateId=".$mandate_id.'&StudentId='.$student_id;
				                 
								$curl = curl_init();
							   
							    curl_setopt($curl, CURLOPT_URL, $url);
							    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
							    curl_setopt($curl, CURLOPT_HEADER, false);

							    // execute and return string (this should be an empty string '')
							    $str = curl_exec($curl);

							    //echo '$str :'.$str;
							    
							    curl_close($curl);

				                 //$output = file_get_contents($url);
				            }     


						}
					 	

 						/*=================================*/
				} 

				/*===================================================================*

				} // Mandate not exists - End	        
		 
				/*=========================*/

			  } // Mandate Status Defined - End 	
			 	
			 
			} // m_rec - end


		} // while - end	


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result3)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result3));
			        }

				//========================================================
				// Updade Student Students School Info 
				//========================================================
				
				$query4 = "UPDATE WeeklyServices a,
					        SchStudentAssignmentHeader b,
					        SchStudentMandates c 
					  set   a.SchoolId = c.SchoolId 

					where a.ScheduleStatusId > 6
					and a.ServiceDate >= '2019-09-01' 
					and a.AssignmentId = b.Id 
					and b.MandateId = c.Id 
					and a.SchoolId != c.SchoolId 
					and c.SchoolId != '0' 
					and a.ServiceTypeid < 39
					  "; 		
 			
			
				$result4 = $connection->getAll($query4, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result4)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result4));
			        }

				// //========================================================
				// // Updade School ID within Student Profile Info 
				// //========================================================
				
				// $query5 = "UPDATE SchStudents a, SchStudentMandates b
				// 	        set a.SchoolId =  b.SchoolId
					      
				// 	      where a.Id = b.StudentId
				// 	      and  year(b.EndDate) >= '2020'
				// 	      and  b.StatusId = '1'
				// 	      and  a.SchoolId !=  b.SchoolId
				// 	      and  b.SchoolId != '0'
				// 	          "; 		
 			
			
				// $result5 = $connection->getAll($query5, DB_FETCHMODE_ASSOC);

			 //        if (DB::isError($result4)){
			 //            die("Could not query the database:<br />$query5 ".DB::errorMessage($result5));
			 //        }


				// //========================================================
				// // Updade Sub-School ID within Student Profile Info 
				// //========================================================
				
				// $query5 = "UPDATE SchStudents a, SchStudentMandates b
				// 	        set a.SubSchoolTypeId =  b.SchoolId
					      
				// 	      where a.Id = b.StudentId
				// 	      and  year(b.EndDate) >= '2020'
				// 	      and  b.StatusId = '1'
				// 	      and  a.SchoolId !=  b.SchoolId
				// 	      and  b.SchoolId != '0'
				// 	          "; 		
 			
			
				// $result5 = $connection->getAll($query5, DB_FETCHMODE_ASSOC);

			 //        if (DB::isError($result4)){
			 //            die("Could not query the database:<br />$query5 ".DB::errorMessage($result5));
			 //        }



	$connection->disconnect();
 
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

?>