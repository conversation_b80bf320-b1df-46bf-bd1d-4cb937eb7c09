<?php 
   
    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
    
    $charset = 'utf8mb4';

 
   $FromDate = $_GET['FromDate'];
   $ToDate = $_GET['ToDate'];
   $ParaDiscrTypeId = $_GET['ParaDiscrTypeId'];
   $ServiceTypeIdList = $_GET['ServiceTypeIdList'];
   $RegistrantId = $_GET['RegistrantId'];
   $StudentName = $_GET['StudentName'];

  
   // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchSchoolboardParasDiscrepancySchedules(?,?,?,?,?,?)");
    $stmt->execute([ $FromDate, 
                     $ToDate,
                     $ParaDiscrTypeId, 
                     $ServiceTypeIdList,
                     $RegistrantId,
                     $StudentName


                  ]);

  
  $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

  $pdo=null;


    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


 
    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A1:K1")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValue('A1', 'Student');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', 'Date');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', 'W.D.');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', "Start Time (Sched.)");
    $objPHPExcel->getActiveSheet()->setCellValue('E1', 'Start Time (Act.)');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', "End Time (Sched.)");
    $objPHPExcel->getActiveSheet()->setCellValue('G1', "End Time (Act.)");
    $objPHPExcel->getActiveSheet()->setCellValue('H1', 'Hrs (Sched.)');
    $objPHPExcel->getActiveSheet()->setCellValue('I1', 'Hrs (Act.)');
    $objPHPExcel->getActiveSheet()->setCellValue('J1', "Service Type");
    $objPHPExcel->getActiveSheet()->setCellValue('K1', "Para");
  
  
   

     $row = 2;
    foreach ($results as $result) {


       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $result['StudentName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['ServiceDate']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['WeekDay']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['SchedStartTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['ActStartTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['SchedEndTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['ActEndTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['SchedTotalHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['ActTotalHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['ServiceTypeDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['RegistrantName']); 
 
        $row++;
    }  

   $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../hr/discrepancy_schedules.xlsx";

     $objWriter->save($out_File);

   $DownloadedFileName = 'discrepancy_schedules.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;

 


  
?>

