<?php 

 	require_once("db_GetSetData.php");

 

	$conn = getCon();


	$RegistrantId = $_GET['RegistrantId'];
	$PayrollWeek = $_GET['PayrollWeek'];


	$query = "	SELECT  DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDateFrm,
						DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
				 		DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
						FORMAT((a.TotalHours * 60), 0) as TotalHours,
						group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
						group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
						
						SessionGrpSize,
						a.RegistrantId,

						COALESCE((SELECT DATE_FORMAT( h.TransDate, '%m/%d/%Y' ) FROM SchRegistrantSignedTimeCards h
									WHERE a.RegistrantId = h.RegistrantId
									AND a.PayrollWeek = h.PayrollWeek ),' ') as SignedTimeCardDate 

 
				FROM 	WeeklyServices a, SchStudents b
							WHERE a.RegistrantId = '{$RegistrantId}' 
							AND   a.PayrollWeek =  '{$PayrollWeek}'  
							AND   a.ScheduleStatusId > 5
							AND   b.Id = a.StudentId
				GROUP BY a.ServiceDate	, a.StartTime, a.EndTime, a.TotalHours	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 
  
?>
