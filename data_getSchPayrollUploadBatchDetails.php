<?php 

  require_once("db_GetSetData.php");

  $conn = getCon();

  $PayrollBatchNumber = $_GET['PayrollBatchNumber'];
  $BatchTypeId = $_GET['BatchTypeId'];


  if ($BatchTypeId == '3') {

	  $query = "Call  proc_getSchEWebToPayrollUploadRNData ('{$PayrollBatchNumber}','','' )  ";  	 


  } else {

	  $query = "Call  proc_getSchEWebToPayrollUploadData ('{$PayrollBatchNumber}','','' )  ";  	 


  }



  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;


 
?>
