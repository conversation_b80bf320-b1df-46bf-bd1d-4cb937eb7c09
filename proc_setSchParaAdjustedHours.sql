
      

  /*=========================================*/

  DELIMITER $$

  DROP PROCEDURE IF EXISTS proc_setSchParaAdjustedHours$$

  CREATE PROCEDURE proc_setSchParaAdjustedHours ( p_school_id INT,  
                          p_sub_school_id INT,
                          p_week_day_id INT,
                          p_para_perc int
                          )  

  BEGIN
 
    DECLARE v_TotalHours DEC(5,2);


    SELECT  round((a.TotalHours - .5) * (p_para_perc / 100),2)     

     into v_TotalHours
      FROM  SchSchoolHours a 
        RIGHT JOIN DaysOfWeek5 b
              ON a.WeekDayId = b.WeekDayId
              AND a.SchoolId = p_school_id  
              AND a.SubSchoolTypeId = p_sub_school_id  
             WHERE b.WeekDayId = p_week_day_id ;   


    IF (v_TotalHours IS NULL) THEN
      
      SET v_TotalHours = round(8 * (p_para_perc / 100),2) ;

    END IF;

    IF (v_TotalHours >= 6) THEN          

    create temporary table tmp

      SELECT     
               CASE   
                WHEN a.StartTime THEN a.StartTime
                ELSE '07:00:00'
              END AS SchoolStartTimeAM,

              "12:00:00" AS SchoolEndTimeAM,
              "12:30:00" AS SchoolStartTimePM,


              CASE  
                WHEN a.EndTime THEN a.EndTime 
                ELSE '15:00:00'
              END AS SchoolEndTimePM,

              CASE   
                WHEN a.TotalHours THEN (a.TotalHours - .5) 
                ELSE '7.50'
              END AS SchoolTotalHours,
             000.00 as AdjHours,
             '00:00:00' as AdjEndTime,


             000.00 SchoolHoursAM,
             000.00 SchoolHoursPM

             FROM  SchSchoolHours a 

              RIGHT JOIN DaysOfWeek5 b
              ON a.WeekDayId = b.WeekDayId
              AND a.SchoolId = p_school_id  
              AND a.SubSchoolTypeId = p_sub_school_id  
             WHERE b.WeekDayId = p_week_day_id ;

  
    UPDATE tmp
       SET SchoolHoursAM = TIME_TO_SEC(TIMEDIFF(SchoolEndTimeAM, SchoolStartTimeAM)) / 3600;


    UPDATE tmp
       SET SchoolHoursPM = v_TotalHours - SchoolHoursAM;


    UPDATE tmp
       SET SchoolEndTimePM = ADDTIME(SchoolStartTimePM, SEC_TO_TIME(SchoolHoursPM *3600));

    UPDATE tmp
       SET SchoolEndTimePM = SEC_TO_TIME(FLOOR((TIME_TO_SEC(SchoolEndTimePM)+450)/900)*900);


    UPDATE tmp
       SET SchoolHoursPM = TIME_TO_SEC(TIMEDIFF(SchoolEndTimePM, SchoolStartTimePM)) / 3600;



/*
     UPDATE tmp
         SET AdjHours = TIME_TO_SEC(TIMEDIFF(SchoolEndTimePM, SchoolStartTimePM)) / 3600;


     UPDATE tmp
        SET AdjHours = round(AdjHours * (p_para_perc / 100),2);    

 
    UPDATE tmp
       SET AdjEndTime = ADDTIME(SchoolStartTimePM, SEC_TO_TIME(AdjHours *3600));

    UPDATE tmp
       SET AdjEndTime = SEC_TO_TIME(FLOOR((TIME_TO_SEC(AdjEndTime)+450)/900)*900);
    

    UPDATE tmp
       SET SchoolHoursPM = AdjHours,
           SchoolEndTimePM =  AdjEndTime ;
*/


/*
    UPDATE tmp
       SET SchoolHoursPM = SEC_TO_TIME(FLOOR((TIME_TO_SEC(AdjEndTime)+450)/900)*900);
 
  
/*
    UPDATE tmp
       SET SchoolHoursPM = TIME_TO_SEC(TIMEDIFF(SchoolEndTimePM, SchoolStartTimePM)) / 3600;
 
 
     UPDATE tmp
        SET    SchoolHoursPM = round(AdjEndTime * (p_para_perc / 100),2) 

 
       ;        
 */


    SELECT     

    v_TotalHours  as TotalHours,
    SchoolStartTimeAM,
    SchoolEndTimeAM,
    SchoolHoursAM,

    SchoolStartTimePM,
    SchoolEndTimePM,
    SchoolHoursPM 
  

    from tmp;

    
  ELSE
  

create temporary table tmp

    SELECT     
             CASE   
              WHEN a.StartTime THEN a.StartTime
              ELSE '07:00:00'
            END AS SchoolStartTime,

            CASE  
              WHEN a.EndTime THEN a.EndTime 
              ELSE '15:00:00'
            END AS SchoolEndTime,

            CASE   
              WHEN a.TotalHours THEN a.TotalHours 
              ELSE '8.00'
            END AS SchoolTotalHours,
           000.00 as AdjHours,
           '00:00:00' as AdjEndTime
            

           FROM  SchSchoolHours a 

            RIGHT JOIN DaysOfWeek5 b
            ON a.WeekDayId = b.WeekDayId
            AND a.SchoolId = p_school_id  
            AND a.SubSchoolTypeId = p_sub_school_id  
             WHERE b.WeekDayId = p_week_day_id ;

 
    UPDATE tmp
       SET AdjHours = round(SchoolTotalHours * (p_para_perc / 100),2);        

    UPDATE tmp
       SET AdjEndTime = ADDTIME(SchoolStartTime, SEC_TO_TIME(AdjHours *3600));

    UPDATE tmp
       SET AdjEndTime = SEC_TO_TIME(FLOOR((TIME_TO_SEC(AdjEndTime)+450)/900)*900);


    UPDATE tmp
       SET AdjHours = TIME_TO_SEC(TIMEDIFF(AdjEndTime, SchoolStartTime)) / 3600;
    

    SELECT    
             SchoolStartTime as SchoolStartTimeAM,
             AdjEndTime as SchoolEndTimeAM,
             AdjHours as SchoolHoursAM,
             '' as SchoolStartTimePM,
             '' as choolEndTimePM,
             '' as SchoolHoursPM 

    from tmp;


  

    END IF;

        drop temporary table if exists tmp;


  END $$
    
   

  DELIMITER ; 

   