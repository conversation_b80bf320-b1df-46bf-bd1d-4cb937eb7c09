		

		/*=========================================*/

		DELIMITER $$

		DROP PROCEDURE IF EXISTS proc_getSchRNBillingTransactions$$

		CREATE PROCEDURE proc_getSchRNBillingTransactions (IN    
																													p_from_date DATE, 
																												 	p_to_date DATE,
																												 	
																											 	  p_billing_isse_cat_id INT,
																												 	p_school_id INT, 
																												 	p_serv_type_id INT,
																												 	p_rn_id INT,
																												 	p_liaison_id INT,
																												 	p_posting_status_id INT,
																												 	p_conf_number VARCHAR(45),
																												 	p_student_name VARCHAR(45) 
																												
																												)  
															 	 



 

		BEGIN

DECLARE v_LongTermNurseId INT ; 

		
		 		create temporary table tmp_trans engine=memory
		 						  SELECT 
							a.Id,
							a.ServiceTypeId,
							a.AssignmentId,
							a.ServiceDate,
							a.StartTime,
							a.EndTime,
							a.TotalHours,
							a.PaidFl,
							a.BilledFl,
							a.StudentId,
							a.RegistrantId,
							a.SchoolId,
							a.ConfirmationNumber
						FROM
							WeeklyServices a  
								JOIN
							Registrants b ON a.RegistrantId = b.Id
						WHERE
							a.ServiceDate BETWEEN p_from_date AND p_to_date
								AND a.ScheduleStatusId >= 7
								-- AND a.PaidFl = 0
								AND a.ServiceTypeId >= 39
								AND b.TypeId = 12	


			  ;

				



		/*============*/			
		 		create temporary table tmp engine=memory
				
			select   
					a.ServiceTypeId,
					case a.ServiceTypeId
	                  when '39' then 1
	                  when '40' then 2
	                  when '41' then 3
	                  when '42' then 4
	                  when '43' then 5
	                  when '44' then 6
	                  when '45' then 6
				    end as ServiceTypeIdgGoup,
		            
		            a.AssignmentId,
		            case  
		             when a.AssignmentId !=  0 Then 'Long'
		             else 'Short' 
		            end as AssignmentType, 
		            CAST('' AS CHAR(10)) AS LongTermRnId, 
		            CAST('' AS CHAR(512)) AS LongTermRnName, 

			        a.Id as ScheduleId,
			        CONCAT(DATE_FORMAT( a.StartTime, '%l:%i %p' ),'-',(DATE_FORMAT( a.EndTime, '%l:%i %p' )),' (',a.TotalHours,' Hrs)') as ScheduleDesc,   
			        a.ServiceDate, 
					a.RegistrantId as ScheduledRNId,  
					CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as ScheduledRNName,
					COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)),'All') as StudentName,
					a.StudentId,
					COALESCE(c.ExtId,'') as StudentOsisNumber,
					a.SchoolId,
					TRIM(i.SchoolName) as SchoolName,
					i.Extid as SchoolDBN,
					COALESCE(i.SesisSchoolId,'') as SesisSchoolId,
					i.DistrictId,
						 
					CASE
                      when a.PaidFl = '1'then a.TotalHours
                      else ''
                    end as PaidHours,  
					
					CASE
                      when a.BilledFl = '0'then a.TotalHours
                      else ''
                    end as BilledHours,  

					CASE 
	                  when e.RNLiaisonId is not null then CONCAT( trim( g.LastName) , '  ', trim( g.FirstName))
	                  else 'Undefined...'
	            	end as LiaisonName,

					TRIM (DistrictName) as DistrictName,
					CASE 
	                  when CallInStatus is null then 'Unconfirmed'
	                  else CallInStatus
	            	end as CallInStatus,

					d.Id as CallInTimeId,
					COALESCE(DATE_FORMAT( d.CallInTime, '%l:%i %p' ),'') as CallInTime,
					COALESCE(CAST(d.CallInComments as char(512)),'') as CallInComments,
					h.ServiceTypeDesc,
					a.ConfirmationNumber,
					case k.PostingStatusId
	                  when '1' then 'Posted Verified'
	                  when '2' then 'Posted Un-Verified'
	                  else 'Unposted'
				    end as PostingStatusDesc,
				    COALESCE(k.PostingStatusComments,'Service Date/Confirmation Number combination not found on any Daily Posting Report') as PostingStatusComments,   
				    COALESCE(l.DockedHours,'') as DockedHours,
				    COALESCE(l.SetupHours,'') as SetupHours,
				    COALESCE(l.DockedHoursComments,'') as DockedHoursComments 


		 	from tmp_trans a
		 	       JOIN
		 	     Registrants b on a.RegistrantId = b.Id
		 	       JOIN
				 RegistrantTypes f ON b.TypeId = f.Id
		 	       LEFT JOIN
		 	     SchStudents c on a.StudentId = c.Id 
		 	       JOIN
		         SchServiceTypes h ON a.ServiceTypeId = h.Id
				   JOIN 	           
				SchSchools i ON a.SchoolId = i.Id
				   JOIN
				SchDistricts j ON i.DistrictId = j.Id 
				   LEFT JOIN
				SchRNDailyCallInTimes d on a.RegistrantId = d.RegistrantId
				                       and a.ServiceDate = d.ServiceDate   
				   LEFT JOIN 
				SchRNDistrictServiceLiaisons e ON e.DistrictId = i.DistrictId
				 			                   and   e.RNSchoolTypeId = i.RNSchoolTypeId 
				 			                   and   e.ServiceTypeId =  a.ServiceTypeId       
	 			   LEFT JOIN  
	 			SchRNSchoolLiaisons g ON e.RNLiaisonId = g.Id   
                    LEFT JOIN
                SchRNTransactionPostings k on a.Id = k.ScheduleId 
                    LEFT JOIN
                SchRNDockedHours l ON l.ConfirmationNumber = a.ConfirmationNumber
                                      and l.SchoolId = a.SchoolId
                                      and l.ServiceDate = a.ServiceDate 
                                      and l.ServiceTypeId = a.ServiceTypeId     

		 		;
		 



		/* Get long Term Nurse Id - School*/
		/*=================================*/
		update tmp a
		   set a.LongTermRnId = (
		   
		   		Select b.RegistrantId
		   		  FROM SchSchoolAssignmentDetails b
		   		  WHERE a.AssignmentId = b.AssignmentId
		   		  LIMIT 1	
		   		   
		   )
		WHERE a.AssignmentId != 0
		AND a.ServiceTypeId between 39 and 42;  	 

	 

		/* Get long Term Nurse Id - Student*/
		/*=================================*/

		update tmp a
		   set a.LongTermRnId = (
		   
		   		Select b.RegistrantId
		   		  FROM SchStudentAssignmentDetails b
		   		  WHERE a.AssignmentId = b.AssignmentId
		   		  LIMIT 1	
		   		   
		   )
		WHERE a.AssignmentId != 0
		AND a.ServiceTypeId > 42;  	 
	 

		/* Get long Term Nurse Name*/
		/*=================================*/

		update tmp a,
			   Registrants b
		 set a.LongTermRnName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) 	   
		   
		WHERE a.AssignmentId != 0
		AND   a.LongTermRnId = b.Id
		 ;  	 

 
		-- /*========================*/

		create temporary table tmp1 engine=InnoDB
		SELECT  
						ScheduledRNId,
			            ScheduledRNName,
						group_concat( ServiceTypeId SEPARATOR ',' ) as ServiceTypeId,
						ScheduleId,
						ServiceDate,
						DistrictId,
						DistrictName,
						LiaisonName,
						SchoolId,
						SchoolName,
						SchoolDBN,
						SesisSchoolId,
						CallInTime,
						CallInStatus,
						PaidHours,
						BilledHours,

						LongTermRnName,
						StudentName,
						StudentOsisNumber,
						group_concat( ScheduleDesc SEPARATOR ', ' ) as ScheduleDesc,
						group_concat( ScheduleId SEPARATOR ', ' ) as 'ScheduleIdList',
						ConfirmationNumber, 
						group_concat( ServiceTypeDesc SEPARATOR ', ' ) as 'Placement',
						AssignmentType,
						CallInComments,
						PostingStatusDesc,
						PostingStatusComments,
						DockedHours,
						SetupHours,
						DockedHoursComments 
						from tmp
					GROUP BY ScheduledRNId, ServiceDate, ServiceTypeIdgGoup ORDER BY ServiceDate
						 ;


		SET @s = '';

		SET @s = CONCAT("SELECT  
						a.ScheduledRNId,
						a.ScheduledRNName,
			      a.ServiceTypeId,
						a.ScheduleId,
						a.ServiceDate,
						DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDateFrm, 
						a.PaidHours,
						a.BilledHours,

						a.DistrictId,
						a.DistrictName,
						a.LiaisonName,
						a.SchoolId,
						a.SchoolName,
						a.SchoolDBN,
						a.SesisSchoolId,
						a.CallInTime,
						a.CallInStatus,
						a.LongTermRnName,
						a.StudentName,
						a.StudentOsisNumber,
						a.ScheduleDesc,
						a.ScheduleIdList,
						a.ConfirmationNumber, 
						a.Placement,
						a.AssignmentType,
						b.BillingCommentsId,
						b.BillingIssueCategoryId,
						COALESCE(c.BillingIssueCategoryDesc,'') as BillingIssueCategoryDesc,
						COALESCE(a.CallInComments,'') as CallInComments, 
						COALESCE(b.BillingComments,'') as BillingComments,
						COALESCE(b.IssueAmount,'') as IssueAmount,
						COALESCE(b.WriteOffAmount,'') as WriteOffAmount,
						COALESCE(b.AdditionalComments,'') as AdditionalComments,
						PostingStatusDesc,
						PostingStatusComments,
						DockedHours, 
						SetupHours,
						DockedHoursComments 
						 

				from tmp1 a  
					   LEFT JOIN 
                     SchRNBilling b ON a.ScheduleIdList = b.BillingCommentsId
                       LEFT JOIN
                     SchRNBillingIssueCategories c ON b.BillingIssueCategoryId = c.Id 
             WHERE  a.SchoolId = a.SchoolId         
				 ");	


			SET @g = CONCAT(" ORDER BY ServiceDate ");
			

 
			/* Filter By Billing Issue Cathegory*/ 
			/*=====================*/
			IF (p_billing_isse_cat_id != 0)    THEN 

				SET @s = CONCAT(@s, ' and BillingIssueCategoryId = ', p_billing_isse_cat_id);				         

			END IF;

			/* Filter By School*/ 
			/*=====================*/
			IF (p_school_id != 0 )    THEN 

				SET @s = CONCAT(@s, ' and SchoolId = ', p_school_id);				         

			END IF;

			/* Filter By Service Type*/ 
			/*=====================*/
			IF (p_serv_type_id != 0)    THEN 

				SET @s = CONCAT(@s, ' and ServiceTypeId = ', p_serv_type_id);				         

			END IF;

			/* Filter By RN*/ 
			/*=====================*/
			IF (p_rn_id != 0)    THEN 

				SET @s = CONCAT(@s, ' and ScheduledRNId = ', p_rn_id);				         

			END IF;

			/* Filter By Conf #*/ 
			/*=====================*/
			IF (p_conf_number != '')    THEN 

				SET @s = CONCAT(@s, ' and ConfirmationNumber like ','"%', p_conf_number,'%"');				         

			END IF;

			/* Filter By Liaison #*/ 
			/*=====================*/
			IF (p_liaison_id != 0)    THEN 

				SET @s = CONCAT(@s, ' and RNLiaisonId = ', p_liaison_id);				         

			END IF;


			/* Filter By Posting Status*/ 
			/*=====================*/
			-- IF ((p_posting_status_id = '0') || (p_posting_status_id = '1') || (p_posting_status_id = '2'))    THEN 
			IF (p_posting_status_id != '')    THEN 

				SET @s = CONCAT(@s, ' and PostingStatusId = ', p_posting_status_id);				         

			END IF;		    
 		 

			/* Filter By Student Name*/ 
			/*=====================*/
			IF (p_student_name != '')    THEN 

				SET @s = CONCAT(@s, ' and StudentName like ','"%', p_student_name,'%"');				         

			END IF;


				SET @s = CONCAT(@s,@g);				         			         

 		    
 		    PREPARE stmt FROM @s;
		    EXECUTE stmt ;
		    DEALLOCATE PREPARE stmt;

 		 


 		drop temporary table if exists tmp;
 		drop temporary table if exists tmp1;
		drop temporary table if exists tmp_trans;		 



		END $$

		DELIMITER ;		