		DELIMITER $$

		DROP PROCEDURE IF EXISTS proc_getSchStudentWeeeklySchedules$$

		CREATE PROCEDURE proc_getSchStudentWeeeklySchedules (IN 	p_student_id INT, 
																	p_payroll_week DATE
															)  

		BEGIN

			create temporary table tmp
			(
				 
			 		ScheduleId BIGINT, 
			 		ScheduleStatusId INT, 
			 		ScheduleStatusDesc VARCHAR(32),
			 		TextColor VARCHAR(32),
			  		BackgroundColor VARCHAR(32),
			 		ConfirmationNumber VARCHAR(32),
			 		ServiceDate VARCHAR(32), 
			 		ServiceDateSort DATE,
			 		StartTimeNum TIME,
			 		EndTimeNum TIME, 
			  		StartTime VARCHAR(12),
			 		EndTime VARCHAR(12),
					TotalHours DECIMAL(5,2), 
					HoursScheduled DECIMAL(5,2),  						
					WeekDay VARCHAR(5), 

					SchoolId INT, 
					SchoolName VARCHAR(128),   					
					MandateDesc VARCHAR(48),
					SessionGrpSize INT,
					RegistrantId INT, 
					RegistrantName VARCHAR(48),   					
					
					PrimaryVendorFL CHAR(1),   					
					DistrictId INT,
					ServiceTypeId INT,
					ServiceTypeDesc VARCHAR(32),
					RegistrantTypeId INT,
					LastMessage VARCHAR(128), 
					MessagesCount INT,
					UserName VARCHAR(96),  
					TransDate VARCHAR(16)	


			);



			INSERT INTO tmp

			SELECT   		a.Id AS ScheduleId, 
					 		ScheduleStatusId, 
					 		ScheduleStatusDesc,
					 		TextColor,
			 		 		BackgroundColor,
					
					 		a.ConfirmationNumber,
					 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ),
					 		ServiceDate as ServiceDateSort,
 					 		StartTime as StartTimeNum,
			 		  		EndTime as EndTimeNum,
					 		DATE_FORMAT( StartTime, '%l:%i %p' ),
					 		DATE_FORMAT( EndTime, '%l:%i %p' ),
							a.TotalHours, 
							0,
							a.WeekDay, 
							
							a.SchoolId,
							CONCAT(TRIM(SchoolName),' (',DistrictName,') '),
							(SELECT COALESCE(CONCAT( c.SessionFrequency , ' X ', c.SessionLength), '')
								FROM SchStudentMandates c
								WHERE a.MandateId = c.Id LIMIT 1), 
						
							COALESCE(a.SessionGrpSize,''),
							a.RegistrantId, 
							(SELECT COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
								FROM Registrants c, RegistrantTypes f
								WHERE a.Registrantid = c.Id
								AND b.RegistrantTypeId = f.Id LIMIT 1),   
							
							COALESCE((SELECT h.PrimaryVendorFL
								FROM SchDistrictServiceDetails h
								WHERE d.DistrictId = h.DistrictId
								AND a.ServiceTypeId = h.ServiceTypeId LIMIT 1),'0'),   
							
							d.DistrictId,
							a.ServiceTypeId,
							/*b.ServiceTypeDesc,*/
							CONCAT(b.ServiceTypeDesc, ' (', f.RegistrantTypeDesc,')'),
							b.RegistrantTypeId,
							'',
							0,
							CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) )
							,
							a.TransDate


			FROM 	WeeklyServices a, 
					SchServiceTypes b,
					RegistrantTypes f,
					SchDistricts c,
					SchSchools d,
					Users e,
				    ScheduleStatuses g
			WHERE a.StudentId = p_student_id 
				AND  a.PayrollWeek =  p_payroll_week  
				AND a.UserId = e.UserId
				AND ScheduleStatusId = g.Id	
				AND a.ServiceTypeId = b.Id	
				AND d.DistrictId = c.Id
				AND b.RegistrantTypeId = f.Id
				AND a.SchoolId = d.Id	;


			/* Set Total Scheduled Hours for PR Week*/
			/*================================*/
			Update  tmp a 
			   set HoursScheduled = COALESCE((select sum(TotalHours) 
			                             from WeeklyServices b
									Where a.Registrantid > 0  
									AND a.RegistrantId = b.RegistrantId	
		                            AND b.PayrollWeek = p_payroll_week 
									AND b.ScheduleStatusId > 5),'') ;
									 

			/* Set Last Message*/
			/*================================*/
		 
			Update  tmp a
			  Set LastMessage =  COALESCE(( SELECT Msg
					FROM WeeklyServicesMessages b
					WHERE b.Id = ( SELECT max( c.Id )
						FROM WeeklyServicesMessages c
						WHERE c.ScheduleId = a.ScheduleId )),'') ;
		 	
			/* Set Messages Count*/
			/*================================*/
		 
			Update  tmp a
			  Set MessagesCount =  ( SELECT COUNT(*)
					FROM WeeklyServicesMessages b			
						WHERE a.ScheduleId = b.ScheduleId ) ;
		
		 

			SELECT * FROM tmp
			ORDER BY ServiceDateSort, StartTimeNum;	


			drop temporary table if exists tmp;
			 
			
		END $$

		DELIMITER ;		
		 