<?PHP


    $path = "/usr/share/php/libzend-framework-php";
    set_include_path(get_include_path() . PATH_SEPARATOR . $path);

    require_once("Zend/Mail/Transport/Smtp.php"); 
    require_once "Zend/Mail.php";
    
	require_once('DB.php');
	include("db_login.php");
                
    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	
	
	//==================================
	// Get Payroll Week Dates
	//==================================
/*	
	$today = new DateTime();
	$today_str = $today->format('Y-m-d H:i:s');
 	
	$dow = $today_str[wday];
*/
	$dow = 0;

	if ($dow == 0) {

		$payroll_week = date('Y-m-d', strtotime('next Saturday'));
		$sat = date('Y-m-d', strtotime('next Saturday'));
		$fri = date('Y-m-d', strtotime('next Saturday - 1 days'));
		$thu = date('Y-m-d', strtotime('next Saturday - 2 days'));
		$wed = date('Y-m-d', strtotime('next Saturday - 3 days'));
		$tue = date('Y-m-d', strtotime('next Saturday - 4 days'));
		$mon = date('Y-m-d', strtotime('next Saturday - 5 days'));
		$sun = date('Y-m-d', strtotime('next Saturday - 6 days'));
		

	} else {

		$payroll_week = date('Y-m-d', strtotime('next Saturday + 7 days'));
		$sat = date('Y-m-d', strtotime('next Saturday + 7 days'));
		$fri = date('Y-m-d', strtotime('next Saturday + 6 days'));
		$thu = date('Y-m-d', strtotime('next Saturday + 5 days'));
		$wed = date('Y-m-d', strtotime('next Saturday + 4 days'));
		$tue = date('Y-m-d', strtotime('next Saturday + 3 days'));
		$mon = date('Y-m-d', strtotime('next Saturday + 2 days'));
		$sun = date('Y-m-d', strtotime('next Saturday + 1 days'));
		
	
	}
	
	//=========================
	// School Assignments
	//=========================
	$query = "Call proc_SchoolAssigmentsToWklyServicesConversion ('{$payroll_week}' , 
	                                                              '{$mon}', 
																  '{$tue}',
																  '{$wed}',
																  '{$thu}',
																  '{$fri}', 
																  '{$sat}',
																  '{$sun}'
																  )  "; 

	$query = "Call proc_SchoolAssigmentsToWklyServicesConversion () ";
																    


    $result = $connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
	
		
	$connection->disconnect();
	
	echo ('payroll_week: '.$payroll_week.'wed: '.$wed);


?>





 