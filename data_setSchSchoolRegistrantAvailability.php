<?php 


    require "ewDataHandler.php";
    
    $rcr_transaction = new dataHandler(); 

    $RegistrantId =  $_POST['RegistrantId'];
    $PayrollWeek =  $_POST['PayrollWeek'];

    $ServiceDateMon =  $_POST['ServiceDateMon'];	
    $ServiceDateTue =  $_POST['ServiceDateTue'];	
    $ServiceDateWed =  $_POST['ServiceDateWed'];	
    $ServiceDateThu =  $_POST['ServiceDateThu'];	
    $ServiceDateFri =  $_POST['ServiceDateFri'];	

    $MonAvailFL = $_POST['MonAvailFL'];
    $TueAvailFL = $_POST['TueAvailFL'];
    $WedAvailFL = $_POST['WedAvailFL'];
    $ThuAvailFL = $_POST['ThuAvailFL'];
    $FriAvailFL = $_POST['FriAvailFL'];

    $CommentId = $_POST['CommentId'];
    $Comments = $_POST['Comments'];

    $UserId = $_POST['UserId'];


    /* Delete Prev Defined School Registrant Availability
     =========================================================*/
    
    $result = $rcr_transaction->setDeleteSchSchoolAvailability(	$RegistrantId,
                                                                $PayrollWeek); 
                                                    

    /* Set Monday School Registrant Availability
     =========================================================*/
    if ($MonAvailFL == '1') {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailability($RegistrantId,
                                                                            $PayrollWeek,
                                                                            $ServiceDateMon,
                                                                            $UserId
                                                                            ); 
    }

    /* Set Tuesday School Registrant Availability
     =========================================================*/
    if ($TueAvailFL == '1') {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailability($RegistrantId,
                                                                            $PayrollWeek,
                                                                            $ServiceDateTue,
                                                                            $UserId
                                                                            ); 
    }

    /* Set Wednesday School Registrant Availability
     =========================================================*/
    if ($WedAvailFL == '1') {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailability($RegistrantId,
                                                                            $PayrollWeek,
                                                                            $ServiceDateWed,
                                                                            $UserId
                                                                            ); 
    }

    /* Set Thusrday School Registrant Availability
     =========================================================*/
    if ($ThuAvailFL == '1') {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailability($RegistrantId,
                                                                            $PayrollWeek,
                                                                            $ServiceDateThu,
                                                                            $UserId
                                                                            ); 
    }

    /* Set Friday School Registrant Availability
     =========================================================*/
    if ($FriAvailFL == '1') {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailability($RegistrantId,
                                                                            $PayrollWeek,
                                                                            $ServiceDateFri,
                                                                            $UserId
                                                                            ); 
    }


    /* Set School Registrant Availability Comments
     =========================================================*/
    if (($CommentId) || ($Comments)) {

        $result_mon = $rcr_transaction->setSchSchoolRegistrantsAvailabilityComments($RegistrantId,
                                                                                    $PayrollWeek,
                                                                                    $CommentId,
                                                                                    $Comments,
                                                                                    $UserId
                                                                                    ); 
    }



    $rcr_transaction->disconnectDB (); 

    echo $result;

?>
