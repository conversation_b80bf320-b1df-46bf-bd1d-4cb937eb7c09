<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$InclSchedules = $_POST['InclSchedules'];
	$PaidFL = $_POST['PaidFL'];
	$UserId = $_POST['UserId'];

		if ($PaidFL == '1') {
			
	        $query ="UPDATE WeeklyServices 
				            SET PaidFL = '{$PaidFL}', 
	                            UserId = '{$UserId}',
	                            TransDate = now()										
					WHERE 	FIND_IN_SET(Id, '$InclSchedules')  
						";


		} else {

	        $query ="UPDATE WeeklyServices 
				            SET ScheduleStatusId = '7',
				            	PaidFL = '0', 
				            	BilledFL = '0',
				            	PayrollBatchNumber = '',
	                            UserId = '{$UserId}',
	                            TransDate = now()										
					WHERE 	FIND_IN_SET(Id, '$InclSchedules')  
						";

		}




	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	//echo $ret;
	echo $query;


?>



