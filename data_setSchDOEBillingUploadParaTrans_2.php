<?php
       
    
// error_reporting(E_ALL);
// ini_set('display_errors', TRUE);
// ini_set('display_startup_errors', TRUE);
    


    require_once('DB.php');
    include('db_login.php');
    

    $user_id = $_POST['UserId'];
    if (!$user_id) {
        $user_id = '1';
    }   

    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

    $file_date = date('Y-m-d H:i:s').'-'.mt_rand();

    //Portal Input Input File  
    //==========================================================

    // $inputFileName = '../uploads/doe_billing_upload_input -'.$file_date.'.csv';
    // $inputFileName = '../uploads/contractedTNsep24 1.csv';
    $inputFileName = '../rn_reports/Sanchex Vasquez Trans.csv';
 
   
   // if($ufile != none){ 
      
   //      $rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
        
   //  } else {
        
   //      $err_flag = '1';
   //      //print "Error uploading extracted file. Please try again!!! "; 
      
   //      $linecount = 0;
   //      echo  "{ success: true, transactions: '{$linecount}'}";

   //      Return ; 

   //  }
     
  

    $input_file_handle = fopen($inputFileName, "r");
  
    $outputFileName_ts = '../uploads/doe_billing_upload_output_ts -'.$file_date.'.csv';
    $output_file_handle_ts = fopen($outputFileName_ts, "w");
     
    $outputFileName_fs = '../uploads/doe_billing_upload_output_fs -'.$file_date.'.csv';
    $output_file_handle_fs = fopen($outputFileName_fs, "w");

    $out_File_ts = '../uploads/doe_billing_upload_ts'.$file_date.'.txt';
    $fh_ts = fopen($out_File_ts, 'w') or die("can't open file");
    
    $out_File_fs = '../uploads/doe_billing_upload_fs'.$file_date.'.txt';
    $fh_fs = fopen($out_File_fs, 'w') or die("can't open file");





   while (($row_xls = fgetcsv($input_file_handle)) !== FALSE) { // for foreach - start



            /* Check if Wrog file was selected 
             ====================================*/


                $cnt++;
         
                //=========================
                // Write Header
                //========================  
                
                if ($cnt == 1) {


                //$check_file = preg_replace('/[\x00-\x1F\x7F]/', '', $row_xls[0]);
                $row_xls[0] = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $row_xls[0]);
                //echo '$row_xls[0]: '.$row_xls[0].'</br>';     

                /* Check if Wrog file was selected 
                ====================================*/

                        if (($row_xls[0] != 'SRAP FISCAL YR') && ($row_xls[0] != 'SIAP FISCAL YR') && ($row_xls[0] != 'RSAP FISCAL YR')) {

                            $err_flag = '1';


                        } else {

                            fputcsv($output_file_handle_ts, $row_xls);                            
                            fputcsv($output_file_handle_fs, $row_xls);
                            $out_Line_str = implode("\t", $row_xls); 
                            $out_Line_str = $out_Line_str."\n";
                            fwrite($fh_ts, $out_Line_str);
                            fwrite($fh_fs, $out_Line_str);


                            /* Write Heading 
                             ==================*/
                            $write_flag = '1';


                        }




                }


                if ($cnt != 1)   { // cnt !=1 - srart  


                     

                    $auth_start_time = '';
                    $auth_end_time = '';
                    $auth_hours = '';
                    
                    $addon_start_time = ''; 
                    $addon_end_time = ''; 
                    $addon_hours = '';
                    $addon_schedule_id = '';
                    $addon_start_time_unf = ''; 
                    $addon_end_time_unf = ''; 
 
                    //==============================
                    // Get Therapist Id
                    //==============================

                    $therapist_id = $row_xls[10];
                    $therapist_id = str_pad($therapist_id,9, '0', STR_PAD_LEFT); 

                    //==============================
                    // Get Student Id
                    //==============================
                    
                    $student_id = $row_xls[11];

                    //==============================
                    // Get Student Last Name 
                    //==============================

                    $student_last_name = $row_xls[13];


                    //==============================
                    // Get Service Code
                    //==============================

                    $doe_service_code = $row_xls[14];

                    //==============================
                    // Allowed Minutes
                    //==============================

                    $allowed_minutes = $row_xls[19];


                    $service_type_id = '0';
                    $para_service_type_id = '0';

                             
                       $query3 = "SELECT Id as IndServiceTypeId,
                                          SESISParaServiceTypeId    

                                     FROM SchServiceTypes
                                      WHERE DOEServiceTypeIndId like '%{$doe_service_code}%'
                         ";


                        $result3 = $connection->query ($query3);
                        


                        if ($result3->numRows() == 1) {  

 

                            
                            while ($row3 =& $result3->fetchRow (DB_FETCHMODE_ASSOC)) {
                                    $service_type_search = $row3['IndServiceTypeId'];
                                    $para_service_type_id = $row3['SESISParaServiceTypeId'];

                             }  
                        }    




                   echo ' DOE Service Code: '.$doe_service_code.' $service_type_search: '.$service_type_search.' group_fl: '.$group_fl.'</br>';


                    //==============================
                    // Get Service Date
                    //==============================
                    
                    $doe_service_date = $row_xls[24];
                    $service_date = date("Y-m-d", strtotime($row_xls[24]));


                    
                    //echo ' Therapist Id: '.$therapist_id.' Student ID: '.$student_id.' Service Code: '.$doe_service_code.' Service Date: '.$service_date.' Service Type Id: '.$service_type_id.'</br>';                 
                 
                    

             /*###################################################################*/
            if ($para_service_type_id == '2')  { //Para Transportion Service Types - Start
   
                 
                       $query1 = "SELECT   a.Id as ScheduleId,
                                            DATE_FORMAT( StartTime, '%h:%i %p' ) as StartTime,
                                            StartTime as StartTimeUnf,
                                            DATE_FORMAT( EndTime, '%h:%i %p' ) as EndTime,
                                            EndTime as  EndTimeUnf,
                                            a.TotalHours,
                                            a.Id as ServiceId,
                                            a.SchoolId,
                                            f.MandateId, 
                                            a.SchoolId,
                                            a.TotalHours,
                                            a.ServiceTypeId,
                                            e.ParaExtdHoursAuthFL,
                                            e.ParaTransportPerc  
         
                                    FROM    WeeklyServices a,
                                            Registrants b,
                                            SchStudents c,
                                            SchServiceTypes d,
                                            SchStudentMandates e,
                                            SchStudentAssignmentHeader f
                                   WHERE ServiceDate = '{$service_date}'
                                    AND a.ScheduleStatusId = '8'
                                   /* AND a.BilledFL = 0 */
                                    AND a.RegistrantId = b.Id
                                    AND a.StudentId = c.Id  
                                    AND a.ServiceTypeId = d.Id 
                                    AND a.AssignmentId = f.Id
                                    AND f.MandateId = e.Id
                                    AND FIND_IN_SET (a.ServiceTypeId, '8,13') 
                                    AND b.ExtId = '{$therapist_id}'
                                    AND c.ExtId = '{$student_id}'   
                                ORDER BY a.ServiceTypeId        
                                    ";
               

                        
  
                    echo '$query1: '.$query1.'</br>';

                    $result1 = $connection->query ($query1);
                   
                    if (DB::isError($result1)){
                        die("Could not query the database:<br />$query ".DB::errorMessage($result1));
                        die($db->getMessage());
                    }

                  

                    if ($result1->numRows() != 0) { // numRows() > 0 - Start
                 
                        $j = 0;

                        while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) { // Read Transporatation Sessions - Start
                                
                                $j++;



                                $schedule_id = $row['ScheduleId'];
                                $start_time_org = $row['StartTime'];
                                $end_time_org = $row['EndTime'];
                                
                                $start_time_unf = $row['StartTimeUnf'];
                                $end_time_unf = $row['EndTimeUnf'];
                                $total_hours = $row['TotalHours'];

                                $service_id = $row['ServiceId'];
                                $mandate_id = $row['MandateId'];
                                $school_id = $row['SchoolId'];
                                $total_hours = $row['TotalHours'];
                                $service_type_id = $row['ServiceTypeId'];
                                $para_extd_serv_auth_fl = $row['ParaExtdHoursAuthFL'];
                                $para_perc = $row['ParaTransportPerc'];


                                //echo '$j: '.$j.' Start Time: '.$start_time_unf.'</br>';
                                echo "$j: student: $student_id ServieDate: $service_date StartTime: $start_time_unf  EndTime: $end_time_unf</br>";


                                $total_minutes = $total_hours * 60; 
                                //allowed_minutes


                                //if ($para_extd_serv_auth_fl == '1') {   /*======= Process "To School" Hours*/
                                if (1 == 0) {   /*======= Wiin the limit  */



                                    $start_time = $start_time_org; // Start Time
                                    $end_time = $end_time_org; // Start Time
                                    $auth_hours = $total_hours;


                                    echo "end_time (14): $end_time<br>";


                                } else {   /*======= Not Authorozed to Work Extd Hours - Start*/

                                    if ($j == '1') { // "To School" Transportation 

                                        /*===================================================*/
                                        $addon_minutes_1 = $allowed_minutes - $total_minutes;  


                                        echo "addon_minutes_1: $addon_minutes_1 allowed_minutes: $allowed_minutes  total_minutes: $total_minutes<br>"; 
 
                                        if ($addon_minutes_1 < 0) { 

                                            $start_date_time_1 = date("2018-01-01 ".$start_time_unf); 
                                            
                                            $start_time = $start_time_org; // Start Time


                                            $end_time = date('g:i A', strtotime($start_date_time_1 . ' +'.$allowed_minutes.' minutes')); // End Time
                                            
                                            echo "end_time (10): $end_time<br>";


                                            $auth_hours = ($allowed_minutes / 60) ;         
                                        
                                        echo 'Allowed Min: '.$allowed_minutes.' Total Minutes: '.$total_minutes.' Auth Hours: '.$auth_hours.'</br>';


                                            $addon_start_time = date('H:i:s', strtotime($start_date_time_1 . ' +'.$allowed_minutes.' minutes')); // Addon Start Time
                                            $addon_start_time_frm = date('g:i A', strtotime($start_date_time_1 . ' +'.$allowed_minutes.' minutes'));
                                            $addon_end_time_frm = $end_time_org ; // Addon End Time
                                            $addon_end_time = $end_time_unf; // Addon End Time
                                            
                                            $addon_hours = abs($addon_minutes_1) / 60;

                                        echo 'Addon Start: '.$addon_start_time.' Addon End: '.$addon_end_time.' Addon Hours: '.$addon_hours.'</br>';

  
                                            $auth_minutes_2 = 0; 

                                 
                                        }

                                        /*======  1. Auth Minutes (To School )    2. Add On Minutes 2 (From School )  ======*/

                                        if ($addon_minutes_1 >=  0) { 

                                            $start_time = $start_time_org; // Start Time
                                            $end_time = $end_time_org; // Start Time
                                            $auth_hours = $total_hours;
                                            $addon_hours = 0;


                                            $auth_minutes_2 = $addon_minutes_1; 

                                            echo "end_time (11): $end_time auth_minutes_2: $auth_minutes_2<br>";


                                        }

                                    
                                        /*===================================================*/


                                    }  


                                    if ($j == '2') { // "From School" Transportation 

                                        /*======  1. Auth Minutes (To School )  2. Add On Minutes 1 (To School ) 2. Add On Minutes 2 (From School )  ======*/

                                         if ($auth_minutes_2 >= $total_minutes) { // Full Second - No addon

                                            $start_time = $start_time_org; // Start Time
                                            $end_time = $end_time_org; // Start Time
                                            $auth_hours = $total_hours;
                                            $addon_hours = '0';

                                            echo "end_time (12): $end_time<br>";

                                         }   


                                        if ($auth_minutes_2 == 0) { 

                                              

                                             $addon_start_time = $start_time_unf; // Addon Start Time
                                             $addon_end_time = $end_time_unf ; // Addon End Time
                                             $addon_start_time_frm = $start_time_org; // Addon Start Time
                                             $addon_end_time_frm = $end_time_org ; // Addon End Time
                                             
                                             $addon_hours = $total_hours;

                                        echo 'Addon Start 2: '.$addon_start_time.' Addon End 2: '.$addon_end_time.' Addon Hours 2: '.$addon_hours.'</br>';



                                             $start_time = '0';
                                             $end_time = '0';
                                             $auth_hours = '0';

                                     
                                        }

                                        /*======  1. Auth Minutes (To School )    2. Add On Minutes 2 (From School )  ======*/

                                        if ($auth_minutes_2 < $total_minutes) { //Split 

                                            $start_time = $start_time_org; // Start Time

                                            $start_date_time_2 = date("2018-01-01 ".$start_time_unf); 

                                            //======
                                            // $end_time = date('g:i A', strtotime($start_date_time_2 . ' +'.$auth_minutes_2.' minutes'));
                                             
                                           $end_time = get_calcEndTime($start_date_time_2, $auth_minutes_2);

                                            echo "end_time (13) (before): $end_time start_date_time_2: $start_date_time_2 auth_minutes_2: $auth_minutes_2 <br>";

                                            // Split the minutes into whole minutes and fractional minutes (seconds)
                                            // $whole_minutes = floor($auth_minutes_2);
                                            // $seconds = ($auth_minutes_2 - $whole_minutes) * 60; // Convert fraction of a minute to seconds

                                            // Add the minutes and seconds to the start date and time
                                            // $end_time = date('g:i A', strtotime($start_date_time_2 . " +$whole_minutes minutes +$seconds seconds"));

                                            //========
                                            
                                            echo "end_time (13) (after): $end_time start_date_time_2: $start_date_time_2 auth_minutes_2: $auth_minutes_2 <br>";


                                            $auth_hours = ($auth_minutes_2 / 60);

                                            // $addon_start_time = date('H:i:s', strtotime($start_date_time_2 . ' +'.$auth_minutes_2.' minutes'));
                                            // $addon_start_time_frm = date('g:i A', strtotime($start_date_time_2 . ' +'.$auth_minutes_2.' minutes'));
                                            
//=======
// Initial time in HH:MM:SS format
$initial_time = $start_date_time_2;

// Minutes to add
$minutes_to_add = $auth_minutes_2;

// Convert minutes to seconds (1 minute = 60 seconds)
$seconds_to_add = $minutes_to_add * 60;

// Create a DateTime object from the initial time
$time = new DateTime($initial_time);

// Add the seconds to the time
$time->modify("+{$seconds_to_add} seconds");

// $calc_start_time = $time->format('H:i:s');
$addon_start_time = $time->format('H:i:s');
$addon_start_time_frm = $time->format('g:i A');

 //======

                                            $addon_end_time = $end_time_unf ; // Addon End Time
                                            $addon_end_time_frm = $end_time_org ; // Addon End Time

                                            $addon_hours = ($total_minutes - $auth_minutes_2) / 60;


                                        }
                                    }    
                                        


                                } /*======= Not Authorozed to Work Extd Hours - End*/


                            //=======================

                             //echo 'service_type_id: '.$service_type_id.'</br>';

                                if ($auth_hours != '0' ) { // Auth Hours - Start
                                    $bill_rate = getBillRate($connection, $mandate_id, $school_id, $service_type_id);

                                    if ($auth_hours == 0.67) {

                                        $multiplier = 0.66666666667;
                                    
                                    } else {

                                        $multiplier = $auth_hours;


                                    }

                                    $bill_amount = $bill_rate * $multiplier;
                                    $bill_amount = number_format($bill_amount,2,'.',',');

                                    echo "end time (upload file): $end_time<br>";
                                     
                                    $row_xls[25] = 'P';
                                    $row_xls[26] =  '1';
                                    $row_xls[27] =  $start_time;
                                    $row_xls[28] =  $end_time;
                                    $row_xls[29] =  'S'; 
                                    $row_xls[32] =  $bill_amount;
                                        
                                    $linecount++;
                                    $i++;
                                    $write_flag = '1';

                                    //======== Set BilledFL ======== 

                                    $query_bld_fl  = "UPDATE  WeeklyServices 
                                                         SET BilledFl = '1' 
                                                        WHERE Id = '{$schedule_id}'
                                                        ";
                                    // $result_bld_fl = $connection->query ($query_bld_fl);

                                    
                                    $out_Line_str = implode("\t", $row_xls); 
                                    $out_Line_str = $out_Line_str."\n";
                                    
                                    if ($service_type_id == '8') {


                                        fputcsv($output_file_handle_ts, $row_xls);
                                        fwrite($fh_ts, $out_Line_str);

                                    } else {

                                        fputcsv($output_file_handle_fs, $row_xls);
                                        fwrite($fh_fs, $out_Line_str);

                                    }

                                    //echo   $out_Line_str.'</br>'; 



                                } // Auth Hours - End
                                   
                                if ($addon_hours != '0') { // Addo Hours - Start

                                    if ($addon_hours == 0.67) {

                                        $multiplier = 0.66666666667;
                                    
                                    } else {

                                        $multiplier = $addon_hours;


                                    }

                                    $bill_amount = $bill_rate * $multiplier;
                                    $bill_amount = number_format($bill_amount,2,'.',',');



                                    $row_xls[27] =  $addon_start_time_frm;
                                    $row_xls[28] =  $addon_end_time_unf_frm;
                                    $row_xls[32] =  $bill_amount;

                                    $out_Line_addon = implode(",", $row_xls); 
                                    $out_Line_addon = $out_Line_addon."\n";

 
                                     $query4 = "INSERT INTO  SchStudentParaUnBilledTransactions_Recovery_Trans
                                                ( 
                                                    ScheduleId,
                                                    StartTime,
                                                    EndTime,
                                                    TotalHours,
                                                /*    TransCSVData, */
                                                    BillAmount,
                                                    BillRate,
                                                    StudentExtId,
                                                    RegistrantExtId,
                                                    DOEServiceDate,
                                                    DOEServiceCode,
                                                    AllowedMinutes,
                                                    UserId,
                                                    TransDate
                                                )
                                                VALUES
                                                ( 
                                                    '{$schedule_id}',
                                                    '{$addon_start_time}',
                                                    '{$addon_end_time}',
                                                    '{$addon_hours}',
                                                /*    '{$out_Line_addon}', */
                                                    '{$bill_amount}',

                                                    '{$bill_rate}',
                                                    '{$student_id}',
                                                    '{$therapist_id}',
                                                    
                                                    '{$service_date}',
                                                    '{$doe_service_code}',
                                                    '{$allowed_minutes}',
                                                    '1',
                                                    NOW()
                                                ) ";
                               

                                        
                  
                                    //echo '$query4: '.$query4.'</br>';

                                    $result4 = $connection->query ($query4);
                                   
                                    if (DB::isError($result4)){
                                        die("Could not query the database:<br />$query ".DB::errorMessage($result4));
                                        die($db->getMessage());
                                    }



                                } // Addo Hours - End



                         }  // Read Transporatation Sessions - End


                       





               } // numRows() > 0 - end         
             
            } //Para Transportion Service Types - end    
            /*###################################################################*/



            } // Cnt !=1  - end

            /* Wrong file selected error
             =====================================*/
            if ($err_flag == '1') {

                //echo "Wrong file was selected. Please try again!!!</br>"; 
                

                $linecount = 0;
                echo  "{ success: true, transactions: '{$linecount}'}";
                Return ; 

            }


   } // for foreach - end


    fclose($input_file_handle);
    fclose($output_file_handle_ts);
    fclose($output_file_handle_fs);
    fclose($fh_ts); 
    fclose($fh_fs); 

    $connection->disconnect();

 

    $zip = new ZipArchive();
    $filename = "../uploads/eWebToVPortalZipFile.zip";
    unlink($filename);


    $downloaded_filename = "eWebToVPortalZipFile.zip";

    if ($zip->open($filename, ZipArchive::CREATE)!==TRUE) {
        exit("cannot open <$filename>\n");
    }

    $zip->addFile($out_File_ts,'eWebToVPortalUploadFile_ts.txt');
    $zip->addFile($out_File_fs,'eWebToVPortalUploadFile_fs.txt');    
    $zip->addFile($outputFileName_ts, 'eWebToVPortalVerificationFile_ts.csv');
    $zip->addFile($outputFileName_fs, 'eWebToVPortalVerificationFile_fs.csv');

    $zip->close();


    echo  "{ success: true, transactions: '{$linecount}'}";

 
 

 
    
    //=======================

    function getBillRate  ($connection, $mandate_id, $school_id, $service_type_id) {

        $query4 = "SELECT  BillRate
             from SchDistrictServiceDetails a,  
                 SchStudentMandates b,
                 SchSchools c

                    WHERE b.Id = '{$mandate_id}'
                    AND   c.Id = '{$school_id}'
                    AND   a.DistrictId = c.DistrictId
                    AND   a.BillingContractId = b.BillingContractId
                    AND   a.ServiceTypeId = '{$service_type_id}'   ";


 
        $result4 = $connection->query ($query4);

        //echo '$query4 :'.$query4.'</br>';        

        while ($row4 =& $result4->fetchRow (DB_FETCHMODE_ASSOC)) {
                $bill_rate = $row4['BillRate'];
        }  


        return $bill_rate;

 
    }    

  
       function get_1to1_AuthAddOnServiceHours   ($total_hours, $perc, $start_time,  $end_time) {

         
            GLOBAL $auth_start_time;
            GLOBAL $auth_end_time;
            GLOBAL $auth_hours;
                        
            GLOBAL $addon_start_time; 
            GLOBAL $addon_end_time; 
            GLOBAL $addon_hours;
            GLOBAL $addon_schedule_id;
            GLOBAL $addon_start_time_unf; 
            GLOBAL $addon_end_time_unf; 
     
     
          

            $total_minutes = $total_hours * 60; 
            $auth_minutes = 420 * ($perc/100);
            $addon_minutes = $total_minutes - $auth_minutes;


           
            /*========= Return "Original Billing Hours" if <= "Authorizd Hours" ===========*/ 
            if ($addon_minutes   <= 0) {

                $auth_end_time = $end_time;
                $auth_hours =  $total_hours;
                $addon_hours = 0;
                
 

                retrun;


            }
            /*==============================================================================*/



     
            $start_date_time = date("2000-01-01 ".$start_time); 

            //echo 'Start Time: '.$start_date_time;

            /*======= Authorized End Time Calculation =======*/
            $auth_end_date_time = date('H:i:s', strtotime($start_date_time . ' +'.$auth_minutes.' minutes'));
            $auth_end_time = date('g:i A', strtotime($start_date_time . ' +'.$auth_minutes.' minutes'));



            /*======= Addon Start Time (Unformated) =======*/

            $addon_start_time_unf = $auth_end_date_time;


            /*======= Addon End Time Calculation =======*/
            $addon_end_time_unf = date('H:i:s', strtotime($auth_end_date_time . ' +'.$addon_minutes.' minutes'));
            $addon_end_time = date('g:i A', strtotime($auth_end_date_time . ' +'.$addon_minutes.' minutes'));
             
            $auth_hours = $auth_minutes / 60;
            $auth_hours = number_format($auth_hours,2);

            $addon_hours = $addon_minutes / 60;
            $addon_hours = number_format($addon_hours,2);




      }

       function get_Trans_AuthAddOnServiceHours   ($deduct_hours,  $total_hours, $perc, $start_time,  $end_time) {

         
            GLOBAL $auth_start_time;
            GLOBAL $auth_end_time;
            GLOBAL $auth_hours;
                        
            GLOBAL $addon_start_time; 
            GLOBAL $addon_end_time; 
            GLOBAL $addon_hours;
            GLOBAL $addon_schedule_id;
            GLOBAL $addon_start_time_unf; 
            GLOBAL $addon_end_time_unf; 
     
     
          

            $deduct_minutes = $deduct_hours * 60; 
            $total_minutes = $total_hours * 60; 

            $auth_minutes = (420 - $deduct_minutes)  * ($perc/100);
            $addon_minutes = $total_minutes - $auth_minutes;



            
            /*========= Return "Original Billing Hours" if <= "Authorizd Hours" ===========*/ 
            if ($addon_minutes   <= '0') {

                $auth_end_time = $end_time;
                $auth_hours =  $total_hours;
                $addon_hours = 0;
                retrun;


            }
            /*==============================================================================*/



     
            $start_date_time = date("2000-01-01 ".$start_time); 

            //echo 'Start Time: '.$start_date_time;

            /*======= Authorized End Time Calculation =======*/
            $auth_end_date_time = date('H:i:s', strtotime($start_date_time . ' +'.$auth_minutes.' minutes'));
            $auth_end_time = date('g:i A', strtotime($start_date_time . ' +'.$auth_minutes.' minutes'));



            /*======= Addon Start Time (Unformated) =======*/

            $addon_start_time_unf = $auth_end_date_time;


            /*======= Addon End Time Calculation =======*/
            $addon_end_time_unf = date('H:i:s', strtotime($auth_end_date_time . ' +'.$addon_minutes.' minutes'));
            $addon_end_time = date('g:i A', strtotime($auth_end_date_time . ' +'.$addon_minutes.' minutes'));
             
            $auth_hours = $auth_minutes / 60;
            $auth_hours = number_format($auth_hours,1);

            $addon_hours = $addon_minutes / 60;
            $addon_hours = number_format($addon_hours,1);




      }


     function get_calcEndTime($start_date_time_2, $auth_minutes_2) {

        // Split the minutes into whole minutes and fractional minutes (seconds)
        $whole_minutes = floor($auth_minutes_2);
        $seconds = ($auth_minutes_2 - $whole_minutes) * 60; // Convert fraction of a minute to seconds

        // Convert start time to a DateTime object
        $start_date_time = DateTime::createFromFormat('Y-m-d H:i:s', $start_date_time_2);

        // Add whole minutes and seconds to the start date and time
        $start_date_time->add(new DateInterval('PT' . $whole_minutes . 'M' . round($seconds) . 'S'));

        // Format the result in 12-hour format with AM/PM
        $end_time = $start_date_time->format('g:i A');

        // Output result
        return $end_time;
    }

?>