<?php 


	require_once("db_GetSetDataPDO.php");

	$conn = getCon();

	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);
	$InclSchedules =  implode(",",$Data);

	$ScheduleStatusId = $_POST['ScheduleStatusId'];
	$UserId = $_POST['UserId'];

     
    $query = "UPDATE WeeklyServices 
	            SET ScheduleStatusId = '{$ScheduleStatusId}', 
                    UserId = '{$UserId}',
                    TransDate = now()										
				WHERE 	Id in($InclSchedules)  ";

 

	$ret = getData ($conn, $query);

	echo $ret;
     // echo "$query<br>";


	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $Data = $_POST['Data'];
	// $Data=json_decode($Data,true);
	// $InclSchedules =  implode(",",$Data);



	// $ScheduleStatusId = $_POST['ScheduleStatusId'];
	// $UserId = $_POST['UserId'];


	// $result = $rcr_transaction->setRegistrantTimeCardVerification(	$InclSchedules,
	// 																$ScheduleStatusId,
	// 																$UserId ); 

	// $rcr_transaction->disconnectDB (); 

	// //echo  '{ success: true };
	// echo $result;
?>
