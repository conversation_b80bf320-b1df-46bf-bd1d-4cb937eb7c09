<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$InclSchedules = $_POST['InclSchedules'];
	$BilledFL = $_POST['PaidFL'];
	$UserId = $_POST['UserId'];

		if ($PaidFL == '1') {
			
	        $query ="UPDATE WeeklyServices 
				            SET BilledFL = '{$BilledFL}', 
	                            UserId = '{$UserId}',
	                            TransDate = now()										
					WHERE 	FIND_IN_SET(Id, '$InclSchedules')  
						";


		} else {

	        $query ="UPDATE WeeklyServices 
				            SET BilledFL = '0',
	                            UserId = '{$UserId}',
	                            TransDate = now()										
					WHERE 	FIND_IN_SET(Id, '$InclSchedules')  
						";

		}




	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	//echo $ret;
	echo $query;


?>



