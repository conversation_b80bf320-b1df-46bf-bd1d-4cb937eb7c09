<?php
/**
 * <PERSON><PERSON>t to replace Pear DB calls with mysqli equivalents in ewDataHandler.php
 */

$filename = 'ewDataHandler.php';
$content = file_get_contents($filename);

if ($content === false) {
    die("Could not read file: $filename\n");
}

echo "Original file size: " . strlen($content) . " bytes\n";

// Define replacement patterns
$replacements = [
    // Replace getAll calls
    '/\$this->connection->getAll\(\$([^,]+),\s*DB_FETCHMODE_ASSOC\)/' => '$this->getAll($\1, MYSQLI_ASSOC)',
    
    // Replace DB::isError calls
    '/DB::isError\(\$([^)]+)\)/' => '$this->isError($\1)',
    
    // Replace DB::errorMessage calls
    '/DB::errorMessage\(\$([^)]+)\)/' => '$this->errorMessage($\1)',
    
    // Replace connection->query calls
    '/\$this->connection->query\(/' => '$this->query(',
    
    // Replace connection->disconnect calls
    '/\$this->connection->disconnect\(\)/' => '$this->connection->close()',
];

// Apply replacements
$originalContent = $content;
foreach ($replacements as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content);
    if ($newContent !== null) {
        $matches = preg_match_all($pattern, $content);
        echo "Pattern: $pattern\n";
        echo "Matches found: " . (is_array($matches) ? count($matches) : $matches) . "\n";
        $content = $newContent;
    } else {
        echo "Error with pattern: $pattern\n";
    }
}

// Write the modified content back to the file
if (file_put_contents($filename, $content) !== false) {
    echo "File updated successfully!\n";
    echo "New file size: " . strlen($content) . " bytes\n";
    echo "Changes made: " . (strlen($originalContent) !== strlen($content) ? 'Yes' : 'No') . "\n";
} else {
    echo "Error writing to file: $filename\n";
}

echo "Replacement complete!\n";
?>
