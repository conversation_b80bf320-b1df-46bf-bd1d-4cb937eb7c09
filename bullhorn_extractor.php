<!DOCTYPE html>
<html lang='en'>
  <head>
    <meta charset="UTF-8">
    <title>Bullhorn to eWeb</title>
  </head>
  <body>
    <?php
    class Bullhorn {
    const API_USERNAME = 'rcmhealthcare.api';
    const API_PASSWORD = 'Welcome123!';
    const CLIENT_ID = '35dc1fe2-02d4-432f-a5d4-71e876f1c6b2';
    const CLIENT_SECRET = 'qnCZuTw0ZCxGGSgVuOjzgI59';
    const ENDPOINT_AUTH_CODE = 'https://auth.bullhornstaffing.com/oauth/authorize?%s';
    const ENDPOINT_ACCESS_TOKEN = 'https://auth.bullhornstaffing.com/oauth/token?%s';
    const ENDPOINT_REST_TOKEN = 'https://rest.bullhornstaffing.com/rest-services/login?version=*&access_token=%s';

    private $access_token;
    private $auth_code;
    private $comm_response;
    private $comm_response_info;
    private $refresh_token;
    private $rest_token;
    private $rest_endpoint_url;
      private function comm($endpoint_script=NULL, $endpoint_method=NULL, $endpoint_args=array(), $endpoint_url=NULL, $verify_response=TRUE) {
        print_r("function comm()<br/>");
        $endpoint_args = !is_array($endpoint_args) ? array() : $endpoint_args;

        $this->comm_response = NULL;
        $this->comm_response_info = NULL;

        if(NULL === $endpoint_url) $endpoint_url = $this->rest_endpoint_url.ltrim($endpoint_script, '/');

        if(!empty($this->rest_token)) {
          $bh_token_param = '?BhRestToken='.urlencode($this->rest_token).'&';
          if(strstr($endpoint_url, '?')) {
            $endpoint_url = str_replace('?', $bh_token_param, $endpoint_url);
          } else {
            $endpoint_url = $endpoint_url.$bh_token_param;
          }
        } elseif(strstr($endpoint_url, '?')) {
          $endpoint_url = rtrim($endpoint_url, '&');  //.'&';
        } else {
          $endpoint_url = $endpoint_url.'?';
        }

        print_r("$"."endpoint_method=".$endpoint_method."<br/>");
        switch($endpoint_method) {
          case 'GET' :
            $endpoint_url_qs = empty($endpoint_args) ? '' : http_build_query($endpoint_args);
            $ch = curl_init();
            print_r("GET:".$endpoint_url.$endpoint_url_qs."<br/>");
            curl_setopt($ch, CURLOPT_URL, $endpoint_url.$endpoint_url_qs);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 600);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            break;
          case 'POST' :
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $endpoint_url);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 600);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_POST, TRUE);

            if(!empty($endpoint_args)) {
              $endpoint_args_string = json_encode($endpoint_args);

              curl_setopt($ch, CURLOPT_POSTFIELDS, $endpoint_args_string);
              curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Content-Length: '.strlen($endpoint_args_string),
              ));
            }
            break;
          case 'PUT' :
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $endpoint_url);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 600);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');

            if(!empty($endpoint_args)) {
              $endpoint_args_string = json_encode($endpoint_args);
              curl_setopt($ch, CURLOPT_POSTFIELDS, $endpoint_args_string);
              curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Content-Length: '.strlen($endpoint_args_string),
              ));
            }
          break;
          default :
            ob_start();
            var_dump($method);
            return FALSE;
          break;
        }

        $this->comm_response = curl_exec($ch);
        $this->comm_response_info = curl_getinfo($ch);
        curl_close($ch);

        if(FALSE === $this->comm_response) return FALSE;

        if(TRUE === $verify_response) {
          if(NULL === ($this->comm_response = json_decode($this->comm_response, TRUE))) return FALSE;
          if(!is_array($this->comm_response)) return FALSE;
        }

        if(is_array($this->comm_response) && isset($this->comm_response['error']) || isset($this->comm_response['errorCode'])) return FALSE;
        return $this->comm_response;
      }
    private function oAuth() {
        print_r("function oAuth()<br/>");
    if(TRUE !== $this->oAuthSetRestToken()) return FALSE;
    if(TRUE !== $this->oAuthLogin()) return FALSE;
    return TRUE;
    }
      private function oAuthSetAuthCode($force=FALSE) {
        print_r("function oAuthSetAuthCode()<br/>");
    if(FALSE === $force && !empty($this->auth_code)) return TRUE;

    $comm_args = array(
    'client_id' => self::CLIENT_ID,
    'response_type' => 'code',
    'username' => self::API_USERNAME,
    'password' => self::API_PASSWORD,
    'action' => 'Login',
    );

    $comm_url = sprintf(self::ENDPOINT_AUTH_CODE, http_build_query($comm_args));
        print_r("$"."comm_url: ".$comm_url."<br/>");

        if(FALSE === $this->comm(NULL, 'GET', NULL, $comm_url, FALSE)) return FALSE;

    if(!empty($this->comm_response_info) && preg_match('@\?code=(.*)&@i', $this->comm_response_info['url'], $auth_code)) {
    $this->auth_code = urldecode($auth_code[1]);
          print_r("$"."this->auth_code= ".$this->auth_code."<br/>");
    return TRUE;
    }

        print_r("oAuthSetAuthCode:Failed to retreive auth code<br/>");
    return FALSE;
    }
      private function oAuthSetRestToken() {
        print_r("function oAuthSetRestToken()<br/>");
    if(!empty($this->refresh_token)) {
    $comm_args = array(
    'grant_type' => 'refresh_token',
    'refresh_token' => $this->refresh_token,
    'client_id' => self::CLIENT_ID,
    'client_secret' => self::CLIENT_SECRET,
    );

    if(FALSE === $this->comm(NULL, 'POST', NULL, sprintf(self::ENDPOINT_ACCESS_TOKEN, http_build_query($comm_args)))) {
            print_r("oAuthSetRestToken:comm failed<br/>");
    return FALSE;
    }

    $this->access_token = $this->comm_response['access_token'];
          print_r("access_token ".$this->access_token."<br/>");
    $this->refresh_token = $this->comm_response['refresh_token'];
          print_r("refresh_token ".$this->refresh_token."<br/>");

    return TRUE;
    }

    if(FALSE === $this->oAuthSetAuthCode()) return FALSE;

    $comm_args = array(
    'grant_type' => 'authorization_code',
    'code' => $this->auth_code,
    'client_id' => self::CLIENT_ID,
    'client_secret' => self::CLIENT_SECRET,
    );

    if(FALSE === $this->comm(NULL, 'POST', NULL, sprintf(self::ENDPOINT_ACCESS_TOKEN, http_build_query($comm_args)))) return FALSE;

    $this->access_token = $this->comm_response['access_token'];
        print_r("$"."this->access_token=".$this->access_token."<br/>");
    $this->refresh_token = $this->comm_response['refresh_token'];
        print_r("$"."this->refresh_token=".$this->refresh_token."<br/>");

    return TRUE;
    }
    private function oAuthLogin() {
        print_r("function oAuthLogin()<br/>");

    if(FALSE === $this->comm(NULL, 'POST', NULL, sprintf(self::ENDPOINT_REST_TOKEN, urlencode($this->access_token)))) {
    return FALSE;
    }

    $this->rest_endpoint_url = $this->comm_response['restUrl'];
    $this->rest_token = $this->comm_response['BhRestToken'];
        print_r("$"."this->rest_token=".$this->rest_token."<br/");

    return TRUE;
    }
      public function recentPlacements($args=array(), $return_ids=FALSE) {
        print_r("function recentPlacements()<br/>");
        if($this->oAuth() !== TRUE) {
          print_r("oAuth FAILED");
          return FALSE;
        }
        if(FALSE === $this->comm('search/Placement?fields=id,candidate,jobOrder&query=dateAdded:[20230101%20TO%20*]&count=50', 'GET')) {
          print_r("$"."search/Placement FAILED");
          return FALSE;
        }
        return $this->comm_response;
      }
      public function getOneJobOrder($jobOrderId) {
        print_r("function getOneJobOrder()<br/>");
        if($this->comm('query/JobOrder?fields=id,clientCorporation&where=id='.$jobOrderId, 'GET') === false) {
          print_r("$"."query/JobOrder FAILED");
          return false;
        }
        return $this->comm_response['data'][0];
      }
      public function getOneCandidate($candidateId) {
        print_r("function getOneCandidate()<br/>");
        if($this->comm('search/Candidate?fields=ssn,occupation,gender,firstName,lastName,address,phone,mobile,email,customInt10,employeeType,owner,comments&query='.$candidateId, 'GET') === false) {
          print_r("$"."searchCandidate FAILED");
          return false;
        }
        return $this->comm_response['data'][0];
      }
    }
    const TARGET_CLIENT_CORPORATION = 27583; // Ginkgo Cambridge (Wilson)
    const LAST_SWEEP_DATE = "20230101";
    $bullhorn = new Bullhorn();
    $recent_placements = $bullhorn->recentPlacements("[".LAST_SWEEP_DATE."%20TO%20*]", TRUE);
    foreach($recent_placements['data'] as $placement) {
      print_r("$"."placement=".json_encode($placement)."<br/>");
      $placement = json_decode(json_encode($placement));
      $jobOrderId = $placement->jobOrder->id;
      print_r("$"."jobOrderId=".$jobOrderId."<br/>");
      $jobOrder = $bullhorn->getOneJobOrder($jobOrderId);
      print_r(json_encode($jobOrder)."<br/>");
      $jobOrder = json_decode(json_encode($jobOrder));
      $clientCorporationId = $jobOrder->clientCorporation->id;
      print_r("$"."clientCorporationId=".$clientCorporationId."<br/>");
      if ($clientCorporationId == TARGET_CLIENT_CORPORATION) {
        $candidateId = $placement->candidate->id;
        $candidate = $bullhorn->getOneCandidate($candidateId);
        print_r(json_encode($candidate)."<br/>");
        $candidate = json_decode(json_encode($candidate));
        $candidateFirstName = $candidate->firstName;
        print_r("*********************************************************<br/>");
        print_r("$"."candidateFirstName=".$candidateFirstName."<br/>");
        print_r("*********************************************************<br/>");
      }
    }
    ?>
  </body>
</html>