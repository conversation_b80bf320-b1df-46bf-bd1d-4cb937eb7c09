<?php
	/** Error reporting */
/*   	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/ 
  
	require_once('DB.php');
	include('db_login.php');
 
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


 

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection1)){
		$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }
 

 
	$FromDate = $_GET['FromDate'];
    $ToDate = $_GET['ToDate'];
    $UserId = $_GET['UserId'];


	$objPHPExcel = new PHPExcel();

	 
   $objPHPExcel->setActiveSheetIndex(0);
   $objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Student Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Student OSIS #');
   $objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Mandate');
   $objPHPExcel->getActiveSheet()->SetCellValue('D1', 'School Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('E1', 'School Name DBN');

   $objPHPExcel->getActiveSheet()->SetCellValue('F1', 'District');
   $objPHPExcel->getActiveSheet()->SetCellValue('G1', 'Service Type');

   $objPHPExcel->getActiveSheet()->SetCellValue('H1', 'Provider Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Provider SSN');
   $objPHPExcel->getActiveSheet()->SetCellValue('J1', 'Provider HR ID');
   $objPHPExcel->getActiveSheet()->SetCellValue('K1', 'Cov. End Date');
   
   $objPHPExcel->getActiveSheet()->SetCellValue('L1', 'Provider Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('M1', 'Provider SSN');
   $objPHPExcel->getActiveSheet()->SetCellValue('N1', 'Provider HR ID');
   $objPHPExcel->getActiveSheet()->SetCellValue('O1', 'Cov. Start Date');
   $objPHPExcel->getActiveSheet()->SetCellValue('P1', 'Print Status');


  


   $query = "SELECT 
          a.StudentId,
          a.Id as aMandateId,
                b.Id as bMandateId,
                a.RegistrantId as endRegRegistrantId,
                b.RegistrantId as strRegRegistrantId,
                DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) as 'CovEndDate',
                DATE_FORMAT( b.StartDate, '%m-%d-%Y' ) as 'CovStartDate',
                CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as 'StudentName',
                c.ExtId as 'OSIS#',
                CONCAT(d.SchoolName, ' ',d.ExtId) as 'SchoolDBN',
                CONCAT(i.SchoolName, ' ',d.ExtId) as 'School',
                DistrictName as 'District',
                ServiceTypeDesc as 'ServiceTypeDesc',
            CONCAT( a.SessionGrpSize , ' ', a.SessionFrequency , 'xWeekly ', a.ParaTransportPerc,'%' ) as' Mandate',
            CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as 'endProviderName',
              e.HrId as 'endProviderHRID',
              e.ExtId as 'endProviderSSN',
            CONCAT( trim( g.LastName) , ', ', trim(g.FirstName)) as 'strProviderName',
              g.HrId as 'strProviderHRID',
              g.ExtId as 'strProviderSSN',
      COALESCE((SELECT count(*) from SchStudentParaPermCoverageReported i 

                 WHERE a.StudentId = i.StudentId
                 AND   a.Id = i.aMandateId
                 AND   b.Id = i.bMandateId LIMIT 1

            ),0) as PrintFL                

         from       SchStudentMandates a,
                    SchStudentMandates b,
                    SchStudents c,
                    SchSchools d,
                    SchSubSchools i,
                    SchServiceTypes f,
                    Registrants e,
                    Registrants g,
                    SchDistricts h 

        where   a.EndDate BETWEEN '{$FromDate}' and '{$ToDate}'   
         and    DATE_ADD(a.EndDate, INTERVAL 1 DAY) = b.StartDate    
         and    a.StudentId = b.StudentId
         and    a.ServiceTypeId = b.ServiceTypeId
         and    a.SessionFrequency = b.SessionFrequency
         and    a.SessionLength  = b.SessionLength
         and    a.SessionGrpSize  = b.SessionGrpSize
         and    a.StudentId = c.Id
         and    a.ServiceTypeId = f.id 
         and    a.SchoolId = d.Id        
         and    a.SchoolId = i.SchoolId
         AND    c.SubSchoolTypeId = i.SubSchoolTypeId      
         and    a.RegistrantId != b.RegistrantId 
         and    a.RegistrantId = e.Id 
         and    b.RegistrantId = g.Id
         and    d.DistrictId = h.Id
         AND    e.TypeId = '23'
   ORDER BY c.LastName, c.FirstName, a.EndDate  ";                      


	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 1;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	


			$linecount++;


            $row_num++;

 
       $print_status = 'Printed';

       if  ($row['PrintFL'] == 0) {

         $print_status = 'Not Printed';


       } 

          
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['StudentName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['OSIS#']);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['Mandate']);
		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['School']);
        $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['SchoolDBN']);

		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $row['District']);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['ServiceTypeDesc']);

		   
		    $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['endProviderName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['endProviderSSN']);
		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['endProviderHRID']);
		    $objPHPExcel->getActiveSheet()->setCellValue('K'.$row_num, $row['CovEndDate']);
		    
		    $objPHPExcel->getActiveSheet()->setCellValue('L'.$row_num, $row['strProviderName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('M'.$row_num, $row['strProviderSSN']);
		    $objPHPExcel->getActiveSheet()->setCellValue('N'.$row_num, $row['strProviderHRID']);
		    $objPHPExcel->getActiveSheet()->setCellValue('O'.$row_num, $row['CovStartDate']);
        $objPHPExcel->getActiveSheet()->setCellValue('P'.$row_num, $print_status);


		if	($row['PrintFL'] == 0) {

			$query1 = "INSERT into SchStudentParaPermCoverageReported
						( 
							StudentId,
							aMandateId,
							bMandateId,
							UserId,
							TransDate
						)
						VALUES (
							'{$row['StudentId']}',
							'{$row['aMandateId']}',
							'{$row['bMandateId']}',
 							'{$UserId}',
                             NOW()


						) ";
			  
			$result1 = $connection1->query ($query1);

 
		}


		 
 
	}	

 
 
		$connection->disconnect();
		$connection1->disconnect();
 


sleep(5);  

	/*=========================================================================*/

// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";

$SheetName = 'Para Perm Coverage Chnages';
$objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/ParaPermCoverageChanges.xlsx";

$objWriter->save($out_File);

// Echo done
echo  "{ success: true, transactions: '{$linecount}'}";

/*==============================*/
 
   
 
   $DownloadedFileName = 'ParaPermCoverageChanges-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;
   
   

?>