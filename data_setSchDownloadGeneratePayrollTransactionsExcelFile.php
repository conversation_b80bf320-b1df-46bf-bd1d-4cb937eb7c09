<?php

    $out_File = "../payroll_reports/payroll_transactions.xlsx";

    // Ensure the file exists before downloading
    if (!file_exists($out_File) || filesize($out_File) === 0) {
        die("Error: File was not found or is empty.");
    }

    // Clear previous output
    ob_clean();
    ob_end_flush();

    // Send the file for download
    header('Content-Description: File Transfer');
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="payroll_transactions.xlsx"');
    header('Content-Length: ' . filesize($out_File));
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    header('Pragma: public');

    // Read the file and send it to the client
    readfile($out_File);

    // Optionally delete the file after download
    // unlink($out_File);

    exit();
?>
