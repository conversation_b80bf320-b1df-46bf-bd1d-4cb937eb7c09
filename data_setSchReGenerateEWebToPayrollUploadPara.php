<?php

    // error_reporting(E_ALL);
    // ini_set('display_errors', TRUE);
    // ini_set('display_startup_errors', TRUE);

    require_once('db_login.php');
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

    try {
        $conn = new PDO("mysql:host=$db_host;dbname=$db_database;charset=utf8", $db_username, $db_password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch (PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }

    $PayrollBatchNumber = $_GET['PayrollBatchNumber'] ?? '';
    $objPHPExcel = new PHPExcel();
    $sheet = $objPHPExcel->setActiveSheetIndex(0);

    $headers = ['Applicant Name', 'HRID', 'Service Date', 'Start Time', 'End Time', 'Service Type', 'Billing Contract', 'Hours', 'Student', 'School Name', 'School Name DBN', 'District', 'Borough'];
    $col = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($col . '1', $header);
        $col++;
    }

    try {
        $stmt = $conn->prepare("SELECT DISTINCT a.RegistrantId, CONCAT(TRIM(b.LastName), ', ', TRIM(b.FirstName)) AS RegistrantName, HrId,
            DATE_FORMAT(a.ServiceDate, '%m/%d/%Y') AS ServiceDate, 
            DATE_FORMAT(StartTime, '%l:%i %p') AS StartTime,
            DATE_FORMAT(EndTime, '%l:%i %p') AS EndTime,
            a.TotalHours, 
            CONCAT(TRIM(m.SchoolName), ' (', d.DistrictName, ') ', c.ExtId) AS SchoolName,
            CONCAT(TRIM(c.SchoolName), ' (', d.DistrictName, ') ', c.ExtId) AS SchoolNameDBN,
            d.DistrictName,
            CONCAT(TRIM(e.LastName), ', ', TRIM(e.FirstName), ' (', e.ExtId, ')') AS StudentName,
            ServiceTypeDesc,
            n.BoroughCode  
        FROM WeeklyServices a
        JOIN Registrants b ON a.RegistrantId = b.id
        JOIN SchSchools c ON a.SchoolId = c.Id 
        JOIN SchDistricts d ON c.DistrictId = d.Id
        JOIN SchStudents e ON a.StudentId = e.Id
        JOIN SchServiceTypes g ON a.ServiceTypeId = g.Id
        JOIN SchSubSchools m ON e.SchoolId = m.SchoolId 
                             and e.SubSchoolTypeId =  m.SubSchoolTypeId  
        JOIN SchBoroughs n ON d.BoroughId = n.Id
        WHERE PayrollBatchNumber = ?");
        
        $stmt->execute([$PayrollBatchNumber]);
        
        $row_num = 2;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            

            // $district_number = preg_replace('/\D/', '', $row['DistrictName']);
            // Step 1: Remove the part of the string that starts with "CSE..."
            $stringWithoutCSE = preg_replace('/\sCSE.*/', '', $row['DistrictName']);

            // Step 2: Extract digits from the remaining string
            preg_match('/\d+/', $stringWithoutCSE, $matches);
            $district_number = $matches[0];

            // echo "stringWithoutCSE: $stringWithoutCSE "


            $sheet->setCellValue("A$row_num", $row['RegistrantName'])
                  ->setCellValue("B$row_num", $row['HrId'])
                  ->setCellValue("C$row_num", $row['ServiceDate'])
                  ->setCellValue("D$row_num", $row['StartTime'])
                  ->setCellValue("E$row_num", $row['EndTime'])
                  ->setCellValue("F$row_num", $row['ServiceTypeDesc'])
                  ->setCellValue("G$row_num", $row['BillingContractName'])
                  ->setCellValue("H$row_num", $row['TotalHours'])
                  ->setCellValue("I$row_num", $row['StudentName'])
                  ->setCellValue("J$row_num", $row['SchoolName'])
                  ->setCellValue("K$row_num", $row['SchoolNameDBN'])
                  ->setCellValue("L$row_num", $district_number)
                  ->setCellValue("M$row_num", $row['BoroughCode']);
            
            $row_num++;
        }
        
        $conn = null;
        
        $sheet->setTitle('Employee');
        $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
        $out_File = "../uploads/eweb_to_payroll_upload_duplicate.xlsx";
        $objWriter->save($out_File);
        
        $DownloadedFileName = "PayrollUploadFileBatch-$PayrollBatchNumber.xlsx";
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($out_File));
        ob_clean();
        flush();
        readfile($out_File);
        unlink($out_File);
        exit;
    } catch (PDOException $e) {
        die("Error: " . $e->getMessage());
    }

?>