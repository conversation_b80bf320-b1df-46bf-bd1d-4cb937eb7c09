
	    

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_setSchSyncShoolHoursWithMandAssignSched$$

	CREATE PROCEDURE proc_setSchSyncShoolHoursWithMandAssignSched (	p_school_id int,  
														        	p_sub_school_type_id int)  

	BEGIN


	 
	 	DECLARE v_SchoolId,  v_MandateId, v_ParaPerc INT;
		DECLARE v_StartTimeMon, v_EndTimeMon,  v_StartTimeTue, v_EndTimeTue, v_StartTimeWed, v_EndTimeWed TIME;
		DECLARE v_StartTimeThu, v_EndTimeThu,  v_StartTimeFri, v_EndTimeFri TIME;
		DECLARE v_AdjEndTimeMon,  v_AdjEndTimeTue, v_AdjEndTimeWed TIME;
		DECLARE v_AdjEndTimeThu,  v_AdjEndTimeFri TIME;

		DECLARE v_HoursMon, v_HoursTue, v_HoursWed, v_HoursThu, v_HoursFri DEC(5,2);
		DECLARE v_AdjHoursMon, v_AdjHoursTue, v_AdjHoursWed, v_AdjHoursThu, v_AdjHoursFri DEC(5,2);

	 	DECLARE v_AdjMon,  v_AdjTue, v_AdjWed, v_AdjThu, v_AdjFri  INT;


		DECLARE done INT DEFAULT 0;
	 	
		/*============================================*/





		DECLARE cur CURSOR FOR
	 

		SELECT    

			  a.SchoolId,	
			  a.Id as MandateId, 
	          ParaTransportPerc,
	          
	          ParaStartTimeMon1,
			  ParaEndTimeMon1,
	          ParaTotalHoursMon1,
	          
	          round(ParaTotalHoursMon1 * (ParaTransportPerc / 100),2),
	          ADDTIME(ParaStartTimeMon1, SEC_TO_TIME((round(ParaTotalHoursMon1 * (ParaTransportPerc / 100),2))*3600)),


	          ParaStartTimeTue1,
			  ParaEndTimeTue1,
	          ParaTotalHoursTue1,
	          
	          round(ParaTotalHoursTue1 * (ParaTransportPerc / 100),2),
	          ADDTIME(ParaStartTimeTue1, SEC_TO_TIME((round(ParaTotalHoursTue1 * (ParaTransportPerc / 100),2))*3600)),

	          ParaStartTimeWed1,
			  ParaEndTimeWed1,
	          ParaTotalHoursWed1,

	          round(ParaTotalHoursWed1 * (ParaTransportPerc / 100),2),
	          ADDTIME(ParaStartTimeWed1, SEC_TO_TIME((round(ParaTotalHoursWed1 * (ParaTransportPerc / 100),2))*3600)),

	          ParaStartTimeThu1,
			  ParaEndTimeThu1,
	          ParaTotalHoursThu1,

	          round(ParaTotalHoursThu1 * (ParaTransportPerc / 100),2),
	          ADDTIME(ParaStartTimeThu1, SEC_TO_TIME((round(ParaTotalHoursThu1 * (ParaTransportPerc / 100),2))*3600)),

	          ParaStartTimeFri1,
			  ParaEndTimeFri1,
	          ParaTotalHoursFri1,

	          round(ParaTotalHoursFri1 * (ParaTransportPerc / 100),2),
	          ADDTIME(ParaStartTimeFri1, SEC_TO_TIME((round(ParaTotalHoursFri1 * (ParaTransportPerc / 100),2))*3600)) 

	          
	   
	   from SchStudentMandates a,
	                   SchServiceTypes b,
	                   SchStudents c
	     where  a.ServiceTypeId = b.Id
	     and    b.SESISParaServiceTypeId = '1'
	     and    a.StatusId = '1'
	     and    a.SchoolId = p_school_id
	     and    a.StudentId = c.Id
	     and    curdate() between a.StartDate and a.EndDate 
	     and    c.SubSchoolTypeId = p_sub_school_type_id
		;



		declare continue handler for not found set done := true;   
		
		
	 	
	 	
		/* Add Registrant's Service Description to Table Cells       
		======================================================= */	
		
			 
		OPEN cur;
		
		read_loop: LOOP
	 
	       FETCH cur INTO   v_SchoolId,
	       					v_MandateId,
					        v_ParaPerc,
					        v_StartTimeMon, 
					        v_EndTimeMon,  
					        v_HoursMon,
					        v_AdjHoursMon,
					        v_AdjEndTimeMon,

					        v_StartTimeTue, 
					        v_EndTimeTue,
					        v_HoursTue, 
	                        v_AdjHoursTue,
	                        v_AdjEndTimeTue,
					        
					        v_StartTimeWed, 
					        v_EndTimeWed,
					        v_HoursWed,
	                        v_AdjHoursWed,
	                        v_AdjEndTimeWed,
					        
					        v_StartTimeThu,
					        v_EndTimeThu,
					        v_HoursThu,
	                        v_AdjHoursThu,
	                        v_AdjEndTimeThu,
					        
					        v_StartTimeFri,
					        v_EndTimeFri,
					        v_HoursFri,
	                        v_AdjHoursFri,
	                        v_AdjEndTimeFRi

	        ;
	 
			IF done THEN
				LEAVE read_loop;
			END IF;
		



	/*========= Mon =========*/

	SET v_AdjHoursMon = (SELECT round(TotalHours * (v_ParaPerc / 100),2)
							FROM    SchSchoolHours
						    WHERE v_SchoolId = SchoolId
						    AND   SubSchoolTypeId = p_sub_school_type_id 
					 		AND  WeekDayId = 1 ) ;


	 SELECT StartTime into v_StartTimeMon 
	 FROM     SchSchoolHours
     WHERE v_SchoolId = SchoolId
	 AND   SubSchoolTypeId = p_sub_school_type_id 
	 AND  WeekDayId = 1 ;


	SET v_AdjEndTimeMon = ADDTIME(v_StartTimeMon, SEC_TO_TIME(v_AdjHoursMon *3600)); /* Get End Time */ 
	SET v_AdjEndTimeMon = SEC_TO_TIME(FLOOR((TIME_TO_SEC(v_AdjEndTimeMon)+450)/900)*900);
    SET v_AdjHoursMon = TIME_TO_SEC(TIMEDIFF(v_AdjEndTimeMon, v_StartTimeMon)) / 3600;
  
    
	 	 

	IF (v_HoursMon != v_AdjHoursMon) THEN


	/*==== Update Mandates =====*/
	 
		UPDATE SchStudentMandates a 
		   set ParaStartTimeMon1 =  v_StartTimeMon,
		       ParaEndTimeMon1 =    v_AdjEndTimeMon,
		       ParaTotalHoursMon1 = v_AdjHoursMon
		where a.Id = v_MandateId
		    ;
		               
		 


	/*==== Update Assignments =====*/
	 
	    UPDATE SchStudentAssignmentDetails a
	      set  StartTime =  v_StartTimeMon,
		       EndTime = v_AdjEndTimeMon,
		       TotalHours = v_AdjHoursMon 
			where WeekDayId = '1'
		    AND v_AdjHoursMon != TotalHours  
		    and exists (select 1 from SchStudentAssignmentHeader b
		                where b.MandateId = v_MandateId 
		                and   a.AssignmentId = b.Id )
	 
	     ;
	 
	/*==== Update Sesions =====*/

	 
	    UPDATE WeeklyServices a,  SchStudentAssignmentHeader b
	      set  a.StartTime =  v_StartTimeMon,
		       a.EndTime = v_AdjEndTimeMon,
		       a.TotalHours = v_AdjHoursMon 
		 where ScheduleStatusId = 7
		    and  WeekDay = 'Mon'
		    and v_AdjHoursMon != TotalHours 
		    and b.MandateId = v_MandateId 
		    and   a.AssignmentId = b.Id  
		    ;
	 
	 
	END IF;  




    /*========= Tue =========*/

    SET v_AdjHoursTue = (SELECT round(TotalHours * (v_ParaPerc / 100),2)
                            FROM    SchSchoolHours
						    WHERE v_SchoolId = SchoolId
						    AND   SubSchoolTypeId = p_sub_school_type_id 
                             AND  WeekDayId = '2' ) ;


	 SELECT StartTime into v_StartTimeTue 
	 FROM     SchSchoolHours
     WHERE v_SchoolId = SchoolId
	 AND   SubSchoolTypeId = p_sub_school_type_id 
	 AND  WeekDayId = 2 ;


    SET v_AdjEndTimeTue = ADDTIME(v_StartTimeTue, SEC_TO_TIME(v_AdjHoursTue *3600)); /* Get End Time */ 
    SET v_AdjEndTimeTue = SEC_TO_TIME(FLOOR((TIME_TO_SEC(v_AdjEndTimeTue)+450)/900)*900);
    SET v_AdjHoursTue = TIME_TO_SEC(TIMEDIFF(v_AdjEndTimeTue, v_StartTimeTue)) / 3600;

     
           

    IF (v_HoursTue != v_AdjHoursTue) THEN

     

    /*==== Update Mandates =====*/
     
        UPDATE SchStudentMandates a 
           set ParaStartTimeTue1 =  v_StartTimeTue,
               ParaEndTimeTue1 =    v_AdjEndTimeTue,
               ParaTotalHoursTue1 = v_AdjHoursTue
        where a.Id = v_MandateId
            ;
                       
         


    /*==== Update Assignments =====*/
     
        UPDATE SchStudentAssignmentDetails a
          set  StartTime =  v_StartTimeTue,
               EndTime = v_AdjEndTimeTue,
               TotalHours = v_AdjHoursTue 
            where WeekDayId = '2'
            AND v_AdjHoursTue != TotalHours  
            and exists (select 1 from SchStudentAssignmentHeader b
                        where b.MandateId = v_MandateId 
                        and   a.AssignmentId = b.Id )
     
         ;
     
    /*==== Update Sesions =====*/

     
        UPDATE WeeklyServices a,  SchStudentAssignmentHeader b
          set  a.StartTime =  v_StartTimeTue,
               a.EndTime = v_AdjEndTimeTue,
               a.TotalHours = v_AdjHoursTue 
         where ScheduleStatusId = 7
            and  WeekDay = 'Tue'
            and v_AdjHoursTue != TotalHours 
            and b.MandateId = v_MandateId 
            and   a.AssignmentId = b.Id  
            ;
     
     
    END IF;  

	 



    /*========= Wed =========*/

    SET v_AdjHoursWed = (SELECT round(TotalHours * (v_ParaPerc / 100),2)
                            FROM    SchSchoolHours
						    WHERE v_SchoolId = SchoolId
						    AND   SubSchoolTypeId = p_sub_school_type_id 
                             AND  WeekDayId = '3' ) ;


	 SELECT StartTime into v_StartTimeWed 
	 FROM     SchSchoolHours
     WHERE v_SchoolId = SchoolId
	 AND   SubSchoolTypeId = p_sub_school_type_id 
	 AND  WeekDayId = 3 ;

    SET v_AdjEndTimeWed = ADDTIME(v_StartTimeWed, SEC_TO_TIME(v_AdjHoursWed *3600)); /* Get End Time */ 
    SET v_AdjEndTimeWed = SEC_TO_TIME(FLOOR((TIME_TO_SEC(v_AdjEndTimeWed)+450)/900)*900);
    SET v_AdjHoursWed = TIME_TO_SEC(TIMEDIFF(v_AdjEndTimeWed, v_StartTimeWed)) / 3600;

         
       

    IF (v_HoursWed != v_AdjHoursWed) THEN

     

    /*==== Update Mandates =====*/
     
        UPDATE SchStudentMandates a 
           set ParaStartTimeWed1 =  v_StartTimeWed,
               ParaEndTimeWed1 =    v_AdjEndTimeWed,
               ParaTotalHoursWed1 = v_AdjHoursWed
        where a.Id = v_MandateId
            ;
                       
         


    /*==== Update Assignments =====*/
     
        UPDATE SchStudentAssignmentDetails a
          set  StartTime =  v_StartTimeWed,
               EndTime = v_AdjEndTimeWed,
               TotalHours = v_AdjHoursWed 
            where WeekDayId = '3'
            AND v_AdjHoursWed != TotalHours  
            and exists (select 1 from SchStudentAssignmentHeader b
                        where b.MandateId = v_MandateId 
                        and   a.AssignmentId = b.Id )
     
         ;
     
    /*==== Update Sesions =====*/

     
        UPDATE WeeklyServices a,  SchStudentAssignmentHeader b
          set  a.StartTime =  v_StartTimeWed,
               a.EndTime = v_AdjEndTimeWed,
               a.TotalHours = v_AdjHoursWed 
         where ScheduleStatusId = 7
            and  WeekDay = 'Wed'
            and v_AdjHoursWed != TotalHours 
            and b.MandateId = v_MandateId 
            and   a.AssignmentId = b.Id  
            ;
     
     
    END IF;  

	  




    /*========= Thu =========*/

    SET v_AdjHoursThu = (SELECT round(TotalHours * (v_ParaPerc / 100),2)
                            FROM    SchSchoolHours
						    WHERE v_SchoolId = SchoolId
						    AND   SubSchoolTypeId = p_sub_school_type_id 
                             AND  WeekDayId = '4' ) ;

	 SELECT StartTime into v_StartTimeThu 
	 FROM     SchSchoolHours
     WHERE v_SchoolId = SchoolId
	 AND   SubSchoolTypeId = p_sub_school_type_id 
	 AND  WeekDayId = 4 ;


    SET v_AdjEndTimeThu = ADDTIME(v_StartTimeThu, SEC_TO_TIME(v_AdjHoursThu *3600)); /* Get End Time */ 
    SET v_AdjEndTimeThu = SEC_TO_TIME(FLOOR((TIME_TO_SEC(v_AdjEndTimeThu)+450)/900)*900);
    SET v_AdjHoursThu = TIME_TO_SEC(TIMEDIFF(v_AdjEndTimeThu, v_StartTimeThu)) / 3600;

         
           

    IF (v_HoursThu != v_AdjHoursThu) THEN

     

    /*==== Update Mandates =====*/
     
        UPDATE SchStudentMandates a 
           set ParaStartTimeThu1 =  v_StartTimeThu,
               ParaEndTimeThu1 =    v_AdjEndTimeThu,
               ParaTotalHoursThu1 = v_AdjHoursThu
        where a.Id = v_MandateId
            ;
                       
         


    /*==== Update Assignments =====*/
     
        UPDATE SchStudentAssignmentDetails a
          set  StartTime =  v_StartTimeThu,
               EndTime = v_AdjEndTimeThu,
               TotalHours = v_AdjHoursThu 
            where WeekDayId = '4'
            AND v_AdjHoursThu != TotalHours  
            and exists (select 1 from SchStudentAssignmentHeader b
                        where b.MandateId = v_MandateId 
                        and   a.AssignmentId = b.Id )
     
         ;
     
    /*==== Update Sesions =====*/

     
        UPDATE WeeklyServices a,  SchStudentAssignmentHeader b
          set  a.StartTime =  v_StartTimeThu,
               a.EndTime = v_AdjEndTimeThu,
               a.TotalHours = v_AdjHoursThu 
         where ScheduleStatusId = 7
            and  WeekDay = 'Thu'
            and v_AdjHoursThu != TotalHours 
            and b.MandateId = v_MandateId 
            and   a.AssignmentId = b.Id  
            ;
     
     
    END IF;  

	 



    /*========= Fri =========*/

    SET v_AdjHoursFri = (SELECT round(TotalHours * (v_ParaPerc / 100),2)
                            FROM    SchSchoolHours
						    WHERE v_SchoolId = SchoolId
						    AND   SubSchoolTypeId = p_sub_school_type_id 
                             AND  WeekDayId = '5' ) ;


	 SELECT StartTime into v_StartTimeFri 
	 FROM     SchSchoolHours
     WHERE v_SchoolId = SchoolId
	 AND   SubSchoolTypeId = p_sub_school_type_id 
	 AND  WeekDayId = 5 ;


    SET v_AdjEndTimeFri = ADDTIME(v_StartTimeFri, SEC_TO_TIME(v_AdjHoursFri *3600)); /* Get End Time */ 
    SET v_AdjEndTimeFri = SEC_TO_TIME(FLOOR((TIME_TO_SEC(v_AdjEndTimeFri)+450)/900)*900);
    SET v_AdjHoursFri = TIME_TO_SEC(TIMEDIFF(v_AdjEndTimeFri, v_StartTimeFri)) / 3600;

         
           

    IF (v_HoursFri != v_AdjHoursFri) THEN

     

    /*==== Update Mandates =====*/
     
        UPDATE SchStudentMandates a 
           set ParaStartTimeFri1 =  v_StartTimeFri,
               ParaEndTimeFri1 =    v_AdjEndTimeFri,
               ParaTotalHoursFri1 = v_AdjHoursFri
        where a.Id = v_MandateId
            ;
                       
         


    /*==== Update Assignments =====*/
     
        UPDATE SchStudentAssignmentDetails a
          set  StartTime =  v_StartTimeFri,
               EndTime = v_AdjEndTimeFri,
               TotalHours = v_AdjHoursFri 
            where WeekDayId = '5'
            AND v_AdjHoursFri != TotalHours  
            and exists (select 1 from SchStudentAssignmentHeader b
                        where b.MandateId = v_MandateId 
                        and   a.AssignmentId = b.Id )
     
         ;
     
    /*==== Update Sesions =====*/

     
        UPDATE WeeklyServices a,  SchStudentAssignmentHeader b
          set  a.StartTime =  v_StartTimeFri,
               a.EndTime = v_AdjEndTimeFri,
               a.TotalHours = v_AdjHoursFri 
         where ScheduleStatusId = 7
            and  WeekDay = 'Fri'
            and v_AdjHoursFri != TotalHours 
            and b.MandateId = v_MandateId 
            and   a.AssignmentId = b.Id  
            ;
     
     
    END IF;  



		END LOOP;
	    CLOSE cur; 

		 
		 
		 
		
	END $$

	DELIMITER ;	

	 