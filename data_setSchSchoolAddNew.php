<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

	$Id = $_POST['Id'];
	$SubSchoolTypeId = $_POST['SubSchoolTypeId'];  
	$ExtId = $_POST['ExtId'];
	$SearchId = $_POST['SearchId'];
	$DistrictId = $_POST['DistrictId'];
	$StatusId = $_POST['StatusId'];
	$SchoolName = $_POST['SchoolName'];
	$SchoolTypeId = $_POST['SchoolTypeId'];
	$StreetAddress1 = $_POST['StreetAddress1'];
	$City = $_POST['City'];
	$State = $_POST['State'];
	$ZipCode = $_POST['ZipCode'];

	$LiaisonFirstName = $_POST['LiaisonFirstName'];
	$LiaisonLastName = $_POST['LiaisonLastName'];
	$OfficePhone = $_POST['OfficePhone'];
	$MobilePhone = $_POST['MobilePhone'];
	$Fax = $_POST['Fax'];
	$Email = $_POST['Email'];
	
	$LiaisonFirstName2 = $_POST['LiaisonFirstName2'];
	$LiaisonLastName2 = $_POST['LiaisonLastName2'];
	$OfficePhone2 = $_POST['OfficePhone2'];
	$MobilePhone2 = $_POST['MobilePhone2'];
	$Fax2 = $_POST['Fax2'];
	$Email2 = $_POST['Email2'];

	$Comments = $_POST['Comments'];
    $SesisSchoolId = $_POST['SesisSchoolId'];
    $RNSchoolTypeId = $_POST['RNSchoolTypeId'];

	$UserId = $_POST['UserId'];

	$SchoolName = mysqli_real_escape_string($conn, $SchoolName); 
	$LiaisonFirstName = mysqli_real_escape_string($conn,$LiaisonFirstName); 
	$LiaisonLastName = mysqli_real_escape_string($conn,$LiaisonLastName); 
	$LiaisonFirstName2 = mysqli_real_escape_string($conn,$LiaisonFirstName2); 
	$LiaisonLastName2 = mysqli_real_escape_string($conn,$LiaisonLastName2); 

    $StreetAddress1 = mysqli_real_escape_string($conn,$StreetAddress1); 
 
    // Insert into SchSchools
    //========================= 

    $query = "INSERT into SchSchools 
                             (
                                ExtId, 
                                StatusId,
                                SearchId,
                                SchoolName,
                                DistrictId,
                                SchoolTypeId,
                                SesisSchoolId,
                                RNSchoolTypeId,
                                UserId,
                                TransDate
                             )
                values  ('{$ExtId}', 
                        '1',  
                        '{$SearchId}',
                        '{$SchoolName}',
                        '{$DistrictId}',
                        '{$SchoolTypeId}', 
                        '{$SesisSchoolId}',
                        '{$RNSchoolTypeId}',
                        '{$UserId}',
                        NOW()  )  "

					;

	$ret =  setData ($conn, $query);   			
	setDisConn($conn);

    // Insert into SchSchools
    //========================= 

	$conn = getCon();


    $query = "

 INSERT INTO  SchSubSchools
                (SchoolId,
                SubSchoolTypeId,
                SchoolName,
                StreetAddress1,
                City,
                State,
                ZipCode,
                LiaisonFirstName,
                LiaisonLastName,
                OfficePhone,
                MobilePhone,
                Fax,
                Email,
                LiaisonFirstName2,
                LiaisonLastName2,
                OfficePhone2,
                MobilePhone2,
                Fax2,
                Email2,
                Comments,
                UserId,
                TransDate
             )

            SELECT  a.Id,
                    '0',   
                    '{$SchoolName}',
                    '{$StreetAddress1}',  
                    '{$City}', 
                    '{$State}', 
                    '{$ZipCode}', 

                    '{$LiaisonFirstName}',
                    '{$LiaisonLastName}',
                    '{$OfficePhone}', 
                    '{$MobilePhone}', 
                    '{$Fax}', 
                    '{$Email}',

                    '{$LiaisonFirstName2}',
                    '{$LiaisonLastName2}',
                    '{$OfficePhone2}', 
                    '{$MobilePhone2}', 
                    '{$Fax2}', 
                    '{$Email2}',

                    '{$Comments}',
                    '{$UserId}',
                    NOW()   
             FROM SchSchools a 
                   
             WHERE a.SearchId =  '{$SearchId}'   " ;        
    

					 
	$ret =  setData ($conn, $query);   			
	setDisConn($conn);

    //================

    $conn = getCon();


    $query = "

 INSERT INTO  SchSubSchools
                (SchoolId,
                SubSchoolTypeId,
                SchoolName,
                StreetAddress1,
                City,
                State,
                ZipCode,
                LiaisonFirstName,
                LiaisonLastName,
                OfficePhone,
                MobilePhone,
                Fax,
                Email,
                LiaisonFirstName2,
                LiaisonLastName2,
                OfficePhone2,
                MobilePhone2,
                Fax2,
                Email2,
                Comments,
                UserId,
                TransDate
             )

            SELECT  a.Id,
                    '1',   
                    '{$SchoolName}',
                    '{$StreetAddress1}',  
                    '{$City}', 
                    '{$State}', 
                    '{$ZipCode}', 

                    '{$LiaisonFirstName}',
                    '{$LiaisonLastName}',
                    '{$OfficePhone}', 
                    '{$MobilePhone}', 
                    '{$Fax}', 
                    '{$Email}',

                    '{$LiaisonFirstName2}',
                    '{$LiaisonLastName2}',
                    '{$OfficePhone2}', 
                    '{$MobilePhone2}', 
                    '{$Fax2}', 
                    '{$Email2}',

                    '{$Comments}',
                    '{$UserId}',
                    NOW()   
             FROM SchSchools a 
             WHERE a.SearchId =  '{$SearchId}'   " ;        
    

                     
    $ret =  setData ($conn, $query);            
    setDisConn($conn);

    $conn = getCon();


    $query = "

 INSERT INTO  SchSubSchools
                (SchoolId,
                SubSchoolTypeId,
                SchoolName,
                StreetAddress1,
                City,
                State,
                ZipCode,
                LiaisonFirstName,
                LiaisonLastName,
                OfficePhone,
                MobilePhone,
                Fax,
                Email,
                LiaisonFirstName2,
                LiaisonLastName2,
                OfficePhone2,
                MobilePhone2,
                Fax2,
                Email2,
                Comments,
                UserId,
                TransDate
             )

            SELECT  a.Id,
                    '2',   
                    '{$SchoolName}',
                    '{$StreetAddress1}',  
                    '{$City}', 
                    '{$State}', 
                    '{$ZipCode}', 

                    '{$LiaisonFirstName}',
                    '{$LiaisonLastName}',
                    '{$OfficePhone}', 
                    '{$MobilePhone}', 
                    '{$Fax}', 
                    '{$Email}',

                    '{$LiaisonFirstName2}',
                    '{$LiaisonLastName2}',
                    '{$OfficePhone2}', 
                    '{$MobilePhone2}', 
                    '{$Fax2}', 
                    '{$Email2}',

                    '{$Comments}',
                    '{$UserId}',
                    NOW()   
             FROM SchSchools a 
             WHERE a.SearchId =  '{$SearchId}'   " ;        
    

                     
    $ret =  setData ($conn, $query);            
    setDisConn($conn);

	echo $query;


 

?>
