<?php

    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\Exception;

    require 'PHPMailer/src/Exception.php';
    require 'PHPMailer/src/PHPMailer.php';
    require 'PHPMailer/src/SMTP.php';

 
    $RNLiaisonId = $_GET['RNLiaisonId'];
    $InclSchedules = $_GET['InclSchedules'];
    $ReportDate = $_GET['ReportDate'];
    
    echo "Step 01 <br>"; 


    $AgencyName = 'RCM Healthcare';
    $ReplyEmail = '<EMAIL>';
 
    $ReportDate = date('m/d/Y');
  
 

    // $date = strtotime($ServiceDate);;
    // $service_date_frm = date('m/d/Y',$date);;
 
    // $service_date_frm = date('m-d-Y',strtotime($ServiceDate));
    
    $charset = 'utf8mb4';

    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

   $ret_liaison = getLiaisonEmailAddress($pdo, $RNLiaisonId);

   $arr_liaison = explode(":", $ret_liaison);
   $LiaisonEmail =  $arr_liaison[0];  
   $LiaisonName  =  $arr_liaison[1];  


 
    $LiaisonEmail = '<EMAIL>';



    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNDailyReportData(?)");
    $stmt->execute([$InclSchedules]);


    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


    $objPHPExcel->getActiveSheet()->getStyle("A1:N1")->getFont()->setSize(24);
    $objPHPExcel->getActiveSheet()->getStyle("A1:N1")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 1, $AgencyName); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 1, 'Date:'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, 1, $ReportDate); 

   $objPHPExcel->getActiveSheet()->getStyle("A2:N2")->getFont()->setSize(22);
    $objPHPExcel->getActiveSheet()->getStyle("A2:N2")->getFont()->setBold( true );

    // $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 2, 'District Name:'); 
    // $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 2, $DistrictName); 


    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A4:N4")->getFont()->setBold( true );

    // var_dump($results[0]);

    foreach ($results[0] as $header => $value) {
        // $sheet->setCellValueByColumnAndRow($column, 1, $header);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, 4, $header); 
    
        $column++;
    }

    // Set results
    $row = 5;
    foreach ($results as $result) {
        $column = 0;
        foreach ($result as $value) {
            // $sheet->setCellValueByColumnAndRow($column, $row, $value);
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, $row, $value); 

            $column++;
        }
        $row++;
    }  
 
     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_daily_coverage -".$LiaisonName.".xlsx";

     $objWriter->save($out_File);

     $DownloadedFileName = 'rn_daily_coverage_report -'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;



    function getLiaisonEmailAddress($pdo, $RNLiaisonId) {

        $stmt = $pdo->prepare("SELECT 
                                Email AS LiaisonEmail,
                                concat(FirstName,' ',LastName)  as LiaisonName
                            FROM
                                SchRNSchoolLiaisons
                            WHERE
                                Id = ?");
        $stmt->execute([$RNLiaisonId]);
        $row = $stmt->fetch();

        return $row['LiaisonEmail'].':'.$row['LiaisonName']; 

    }


?>
