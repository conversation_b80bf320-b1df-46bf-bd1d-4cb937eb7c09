    DELIMITER $$

    DROP PROCEDURE IF EXISTS proc_getSchRegistrantApproveRsa7aFormsHeaders$$

    CREATE PROCEDURE proc_getSchRegistrantApproveRsa7aFormsHeaders (IN  p_registrant_id INT, 
                                                                        p_from_date DATE,
                                                                        p_first_period_date DATE,
                                                                        p_second_period_date DATE,
                                                                        p_to_date DATE 
                                                                )     

                                        

    BEGIN

      


      /* Tele_Therapy Forms* Period: 1 - 15/  
      /*======================*/
      create temporary table tmp
       
       SELECT   a.Id as MandateId,
                a.ServiceTypeId, 
                c.ServiceTypeDesc,  
                a.SessionLength,
                a.SessionGrpSize,
                a.SessionFrequency,
                a.PlaceOfService,
                d.SchoolName,
                a.StudentId,

                '0' as Rsa7aFormSignatureStatus,               
                COALESCE(GuardianEmail,'') as GuardianEmail,

                CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                (SELECT COUNT(*) FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId >= 7
                    AND  d.SessionDeliveryModeId != 'I' 
                    AND d.ServiceDate between p_from_date and p_first_period_date 

            ) as NumbeOfSessions,
            'Tele-Therapy' as FormType,
            '1 - 15    ' as BiMonthlyPeriodDesc,
            '1' as BiMonthlyPeriodId


        FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
        WHERE a.RegistrantId = p_registrant_id  
        AND b.StatusId = '1'
        AND a.StudentId = b.Id
        AND a.ServiceTypeId = c.Id
        AND a.Schoolid = d.Id
        AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId >= 7
                    AND  d.SessionDeliveryModeId != 'I' 
                    AND d.ServiceDate between p_from_date and p_first_period_date 
                    AND curdate() > p_first_period_date
        )
        AND NOT EXISTS ( SELECT 1 FROM SchRsa7aFormSignatures h 
                    WHERE h.MandateId = a.Id 
                    AND h.FromDate = p_from_date
                    AND h.ToDate = p_to_date 
                    AND h.FormTypeId = '2' 
                    AND h.BiMonthlyPeriodId = '1'

        ) ;     
     
      /* Tele_Therapy Forms* Period: 16 - E.O.M/  
      /*======================*/
      insert into tmp
       
       SELECT   a.Id as MandateId,
                a.ServiceTypeId, 
                c.ServiceTypeDesc,  
                a.SessionLength,
                a.SessionGrpSize,
                a.SessionFrequency,
                a.PlaceOfService,
                d.SchoolName,
                a.StudentId,

                '0' as Rsa7aFormSignatureStatus,               
                COALESCE(GuardianEmail,'') as GuardianEmail,

                CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                (SELECT COUNT(*) FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId >= 7
                    AND  d.SessionDeliveryModeId != 'I' 
                    AND d.ServiceDate between p_second_period_date and p_to_date 

            ) as NumbeOfSessions,
            'Tele-Therapy' as FormType,
            '16 - E.O.M' as BiMonthlyPeriodDesc,
            '2' as BiMonthlyPeriodId


        FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
        WHERE a.RegistrantId = p_registrant_id  
        AND b.StatusId = '1'
        AND a.StudentId = b.Id
        AND a.ServiceTypeId = c.Id
        AND a.Schoolid = d.Id
        AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId >= 7
                    AND  d.SessionDeliveryModeId != 'I' 
                    AND d.ServiceDate between p_second_period_date and p_to_date 
                    AND curdate() > p_to_date
        )
        AND NOT EXISTS ( SELECT 1 FROM SchRsa7aFormSignatures h 
                    WHERE h.MandateId = a.Id 
                    AND h.FromDate = p_from_date
                    AND h.ToDate = p_to_date 
                    AND h.FormTypeId = '2' 
                    AND h.BiMonthlyPeriodId = '2'

        ) ;    
      SELECT * FROM tmp
      order by StudentName  
      ; 


      drop temporary table if exists tmp;
       
      
    END $$

    DELIMITER ;   
     