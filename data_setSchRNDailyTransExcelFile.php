<?php


    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
     
   

   $InclSchedules = $_GET['InclSchedules'];
   
   
    $charset = 'utf8mb4';


    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNDailySelReportsTransactions(?)");
    $stmt->execute([ $InclSchedules 

                  ]);

   $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


 
    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A1:T1")->getFont()->setBold( true );

  
    $objPHPExcel->getActiveSheet()->setCellValue('A1', 'Date');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', 'Call-In Status');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', 'District');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', "School");
    $objPHPExcel->getActiveSheet()->setCellValue('E1', 'School ID');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', "Time of Arrival/ Call in time");
    $objPHPExcel->getActiveSheet()->setCellValue('G1', "Today's Nurse");
    $objPHPExcel->getActiveSheet()->setCellValue('H1', 'Long Term Nurse');
    $objPHPExcel->getActiveSheet()->setCellValue('I1', 'Liaison');
    $objPHPExcel->getActiveSheet()->setCellValue('J1', 'OSIS #');
    $objPHPExcel->getActiveSheet()->setCellValue('K1', 'Trip Location & Time');
    $objPHPExcel->getActiveSheet()->setCellValue('L1', 'Confirmation #');
    $objPHPExcel->getActiveSheet()->setCellValue('M1', 'Placement');
    $objPHPExcel->getActiveSheet()->setCellValue('N1', 'AssignmentType');
    $objPHPExcel->getActiveSheet()->setCellValue('O1', 'Call-In Comments');
    $objPHPExcel->getActiveSheet()->setCellValue('P1', 'Posting Status');
    $objPHPExcel->getActiveSheet()->setCellValue('Q1', 'Posting Comments');
 


    // Set results
    $row = 2;
    foreach ($results as $result) {

       
       $service_date = $result['ServiceDate']; 
  
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $service_date); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['CallInStatus']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['DistrictName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['SchoolName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['SesisSchoolId']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['CallInTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['CallInStatus']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['LongTermRnName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['LiaisonName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['StudentOsisNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['ScheduleDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(11, $row, $result['ConfirmationNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['Placement']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(13, $row, $result['AssignmentType']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(14, $row, $result['CallInComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(15, $row, $result['PostingStatusDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(16, $row, $result['PostingStatusComments']); 
   


        $row++;
    }  
 
     $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_daily_report.xlsx";

     $objWriter->save($out_File);

   $DownloadedFileName = 'rn_daily_report -'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;

?>
