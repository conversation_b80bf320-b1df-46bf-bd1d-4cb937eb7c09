<?php

function getCon() {
    include 'db_login.php';

    try {
        $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password);
        // Set PDO error mode to exception
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        die("Could not connect to the database: " . $e->getMessage());
    }
}

function getData($conn, $query) {
    try {
        $stmt = $conn->query($query);
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return "{ success: true, data: " . json_encode($rows) . "}";
    } catch (PDOException $e) {
        die("Error in selecting: " . $e->getMessage());
    }
}

function setData($conn, $query) {
    try {
        $stmt = $conn->prepare($query);
        $stmt->execute();
        return true;
    } catch (PDOException $e) {
        die("Error in updating: " . $e->getMessage());
    }
}

// PDO automatically closes the connection when the object is destroyed
?>
