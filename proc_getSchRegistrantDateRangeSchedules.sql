			DELIMITER $$

			DROP PROCEDURE IF EXISTS proc_getSchRegistrantDateRangeSchedules$$

			CREATE PROCEDURE proc_getSchRegistrantDateRangeSchedules (IN 	p_registrant_id INT, 
																			p_from_date DATE,
																			p_to_date DATE
																	)  

			BEGIN

				create temporary table tmp
				(
					 
				 		ScheduleId BIGINT, 
				 		ScheduleStatusId INT, 
				 		ScheduleStatusDesc VARCHAR(32),
				 		TextColor VARCHAR(32),
				  		BackgroundColor VARCHAR(32),
				 		ConfirmationNumber VARCHAR(32),
				 		ServiceDateSort DATE, 
				 		ServiceDate VARCHAR(32), 			 		
				 		StartTimeSort TIME,
				 		EndTimeSort TIME, 
				  		StartTime VARCHAR(12),
				 		EndTime VARCHAR(12),
						TotalHours DECIMAL(5,2), 
						HoursScheduled DECIMAL(5,2),  						
						WeekDay VARCHAR(5), 
						PayrollWeek DATE,
						SchoolId INT, 
						SchoolName VARCHAR(128),   					
						MandateDesc VARCHAR(48),
						SessionGrpSize INT,
						StudentId INT, 
						StudentName VARCHAR(48),   					
						RegistrantId INT,
						PrimaryVendorFL CHAR(1),   					
						DistrictId INT,
						ServiceTypeId INT,
						ServiceTypeDesc VARCHAR(32),
						RegistrantTypeId INT,
						LastMessage VARCHAR(128), 
						MessagesCount INT,
						StoredDocName VARCHAR(256),
						SessionDeliveryModeId CHAR(1),
						UserName VARCHAR(96),  
						TransDate VARCHAR(16)	


				);



				INSERT INTO tmp

				SELECT   		a.Id AS ScheduleId, 
						 		ScheduleStatusId, 
						 		ScheduleStatusDesc,
						 		TextColor,
				 		 		BackgroundColor,
						
						 		a.ConfirmationNumber,
						 		ServiceDate,
						 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ), 
						 		StartTime as StartTimeSort,
				 		  		EndTime as EndTimeSort,
						 		DATE_FORMAT( StartTime, '%l:%i %p' ),
						 		DATE_FORMAT( EndTime, '%l:%i %p' ),
								a.TotalHours, 
								0,
								a.WeekDay, 
								a.PayrollWeek,
								a.SchoolId,
								CONCAT(TRIM(SchoolName),' (',DistrictName,') '),
								(SELECT COALESCE(CONCAT( c.SessionFrequency , ' X ', c.SessionLength, ' X ', c.SessionGrpSize), '')
									FROM SchStudentMandates c
									WHERE a.MandateId = c.Id), 
							
								COALESCE(a.SessionGrpSize,''),
								a.StudentId, 
								(SELECT COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ), '')
									FROM SchStudents c
									WHERE a.StudentId = c.Id),
								a.RegistrantId,
								/*
								COALESCE((SELECT h.PrimaryVendorFL
									FROM SchDistrictServiceDetails h
									WHERE d.DistrictId = h.DistrictId
									AND a.ServiceTypeId = h.ServiceTypeId),'0'),   
								*/
								'0',
								d.DistrictId,
								a.ServiceTypeId,
								/*b.ServiceTypeDesc,*/
								CONCAT(b.ServiceTypeDesc, ' (', f.RegistrantTypeDesc,')'),
								b.RegistrantTypeId,
								'',
								0,
								COALESCE((SELECT StoredName FROM SchStudentsSessionNotes h
								WHERE a.Id = h.ScheduleId),''),

								SessionDeliveryModeId,
								CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) )
								,
								a.TransDate


				FROM 	WeeklyServices a, 
						SchServiceTypes b,
						RegistrantTypes f,
						SchDistricts c,
						SchSchools d,
						Users e,
					    ScheduleStatuses g
				WHERE a.RegistrantId = p_registrant_id 
					AND a.ServiceDate between  p_from_date and p_to_date  
					AND a.UserId = e.UserId
					AND ScheduleStatusId = g.Id	
					AND a.ServiceTypeId = b.Id	
					AND d.DistrictId = c.Id
					AND b.RegistrantTypeId = f.Id
					AND a.SchoolId = d.Id	;



				/* Set Last Message*/
				/*================================*/
			 
				Update  tmp a
				  Set LastMessage =  COALESCE(( SELECT Msg
						FROM WeeklyServicesMessages b
						WHERE b.Id = ( SELECT max( c.Id )
							FROM WeeklyServicesMessages c
							WHERE c.ScheduleId = a.ScheduleId )),'') ;
			 	
				/* Set Messages Count*/
				/*================================*/
			 
				Update  tmp a
				  Set MessagesCount =  ( SELECT COUNT(*)
						FROM WeeklyServicesMessages b			
							WHERE a.ScheduleId = b.ScheduleId ) ;
			
			 

				SELECT * FROM tmp
				ORDER BY ServiceDateSort, StartTimeSort 
				;	


				drop temporary table if exists tmp;
				 
				
			END $$

			DELIMITER ;		
			 