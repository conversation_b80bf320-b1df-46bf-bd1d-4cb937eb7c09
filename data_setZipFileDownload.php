  
<?php
    
 
    $filename = $_GET['filename'];
    $downloaded_filename = $_GET['downloaded_filename'];


/*
    $filename = "../uploads/eWebToVPortalZipFile.zip";
    $downloaded_filename = "eWebToVPortalZipFile.zip";
*/

    header('Content-Type: application/zip');
    header('Content-disposition: attachment; filename='.$downloaded_filename);
    header('Content-Length: ' . filesize($filename));
    ob_clean();
    flush();
    readfile($filename);

    exit;


?>