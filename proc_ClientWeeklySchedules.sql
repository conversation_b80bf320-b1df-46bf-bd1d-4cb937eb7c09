
/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_ClientWklySchedules$$

CREATE PROCEDURE proc_ClientWklySchedules (IN   p_client_id INT, 
                                                p_payroll_week DATE, 
                                                p_service_date DATE,
                                                p_client_unit_id VARCHAR(128),
                                                p_service_type_id VARCHAR(10)
                                                )  

BEGIN

    
    /*============================================*/
    


    create temporary table tmp
    (
         
            ScheduleId INT, 
            ScheduleStatusId INT, 
            ScheduleStatusDesc VARCHAR(32),
            TextColor VARCHAR(32),
            BackgroundColor VARCHAR(32),

            WorkOrderId INT,
            ClientId INT, 
            ServiceDate VARCHAR(12), 
            ServiceDateOrg DATE,
            StartTimeNum TIME,
            EndTimeNum TIME,
            StartTime VARCHAR(12),
            EndTime VARCHAR(12),
            PayrollWeek DATE,  
            ShiftId INT,
            LunchHour VARCHAR(8),
            TotalHours VARCHAR(12), 
            WeekDay INT, 
            RegistrantId INT, 
            ServiceTypeId INT,
            RegistrantName VARCHAR(48),
            RegistrantTypeDesc VARCHAR(48), 
            ServiceTypeDesc VARCHAR(48), 
            SpecialtiesList VARCHAR(48), 
            LastMessage VARCHAR(128), 
            MessagesCount INT,
            RegistrantTypeId INT,
            ClientUnitId INT, 
            UnitName VARCHAR(32),
            ClientConfFL INT,
            RegistrantConfFL INT,
            ScheduleOrigBy INT,
            HoursScheduled VARCHAR(8),                          
            UserName VARCHAR(96),  
            TransDate VARCHAR(16)   


    );





    IF (!p_client_unit_id) THEN
    
    INSERT INTO tmp


    SELECT  a.Id AS ScheduleId, 
                ScheduleStatusId, 
                ScheduleStatusDesc,
                TextColor,
                BackgroundColor,
                a.WorkOrderId,
                a.ClientId, 
                DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                ServiceDate as ServiceDateOrg,
                StartTime as StartTimeNum,
                EndTime as EndTimeNum,
                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                PayrollWeek,  
                ShiftId,
                LunchHour,
                TotalHours, 
                WeekDay, 
                RegistrantId, 
                ServiceTypeId,
                
                '                                                               ' as RegistrantName,
                '                               ' as RegistrantTypeDesc, 
                '                               ' as ServiceTypeDesc, 
                '                               ' as SpecialtiesList, 
                '                                                                                                     ' as LastMessage, 
                0 as MessagesCount,
                RegistrantTypeId,
                ClientUnitId, 
                COALESCE((SELECT UnitName FROM ClientUnits c
                    WHERE a.ClientUnitId = c.Id), '') as UnitName,
                ClientConfFL,
                RegistrantConfFL,
                ScheduleOrigBy,
                000.0 as HoursScheduled,            
                CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
                DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) AS TransDate
            FROM    WeeklyServices a, 
                    Users e,
                    ScheduleStatuses g                              
                Where   a.ClientId= p_client_id 
                AND  a.PayrollWeek =  p_payroll_week
                AND  a.ServiceDate =  p_service_date  
                AND a.UserId = e.UserId
                AND ScheduleStatusId = g.Id
                
              AND ServiceTypeId like p_service_type_id  
        ORDER BY PayrollWeek desc, ServiceDate, StartTime;  
  

    ELSE

        INSERT INTO tmp

        SELECT  a.Id AS ScheduleId, 
                ScheduleStatusId, 
                ScheduleStatusDesc,
                TextColor,
                BackgroundColor,
                a.WorkOrderId,
                a.ClientId, 
                DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                ServiceDate as ServiceDateOrg,
                StartTime as StartTimeNum,
                EndTime as EndTimeNum,
                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
                DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                PayrollWeek,  
                ShiftId,
                LunchHour,
                TotalHours, 
                WeekDay, 
                RegistrantId, 
                ServiceTypeId,
                
                '                                                               ' as RegistrantName,
                '                               ' as RegistrantTypeDesc, 
                '                               ' as ServiceTypeDesc, 
                '                               ' as SpecialtiesList, 
                '                                                                                                     ' as LastMessage, 
                0 as MessagesCount,
                RegistrantTypeId,
                ClientUnitId, 
                COALESCE((SELECT UnitName FROM ClientUnits c
                    WHERE a.ClientUnitId = c.Id), '') as UnitName,
                ClientConfFL,
                RegistrantConfFL,
                ScheduleOrigBy,
                000.0 as HoursScheduled,            
                CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
                DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) AS TransDate
            FROM    WeeklyServices a, 
                    Users e,
                    ScheduleStatuses g                              
                Where   a.ClientId= p_client_id 
                AND  a.PayrollWeek =  p_payroll_week
                AND  a.ServiceDate =  p_service_date  
                AND (CASE WHEN p_client_unit_id != '' THEN FIND_IN_SET(ClientUnitId, p_client_unit_id) ELSE TRUE END ) 
                AND ServiceTypeId like p_service_type_id  

                AND  a.UserId = e.UserId
                AND  ScheduleStatusId = g.Id
                
        ORDER BY PayrollWeek desc, ServiceDate, StartTime;  


    END IF; 
  
    /* Set Employee Name */
    /*================================*/
    Update  tmp a,  Registrants b, RegistrantTypes f
        Set a.RegistrantName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,') ' , trim(MobilePhone) , ' (S) ',  trim(HomePhone), ' (H) ')  
         Where a.Registrantid IS NOT NULL 
          AND a.RegistrantId = b.Id
    AND b.TypeId = f.Id ;

    /* Set Requested Employee Type*/
    /*================================*/
    Update  tmp a,  RegistrantTypes f
        Set  a.RegistrantTypeDesc = f.RegistrantTypeDesc
         Where  a.RegistrantTypeId = f.Id
      ;

    /* Set Service Type Description*/
    /*================================*/
    Update  tmp a,  ServiceTypes f
        Set  a.ServiceTypeDesc = f.ServiceTypeDesc
         Where  a.ServiceTypeId = f.Id
      ;

    
    
    /* Set Total Scheduled Hours for PR Week*/
    /*================================*/
    Update  tmp a 
       set HoursScheduled = (select sum(TotalHours) 
                                 from WeeklyServices b
                            Where a.Registrantid > 0  
                            AND a.RegistrantId = b.RegistrantId 
                            AND b.PayrollWeek = p_payroll_week 
                            AND b.ScheduleStatusId > 5) ;
                             

    /* Set Last Message*/
    /*================================*/
    Update  tmp a
      Set LastMessage =  ( SELECT Msg
            FROM WeeklyServicesMessages b
            WHERE b.Id = ( SELECT max( c.Id )
                FROM WeeklyServicesMessages c
                WHERE c.ScheduleId = a.ScheduleId )) ;
    
    /* Set Messages Count*/
    /*================================*/
    Update  tmp a
      Set MessagesCount =  ( SELECT COUNT(*)
            FROM WeeklyServicesMessages b           
                WHERE a.ScheduleId = b.ScheduleId ) ;
    
    
    Select  *
    from tmp
    Order By ServiceDateOrg, StartTimeNum;
    
    drop temporary table if exists tmp;
     
    
END $$

DELIMITER ; 