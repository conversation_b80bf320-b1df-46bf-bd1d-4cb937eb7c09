<?php
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

include 'db_login.php';

try {
    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $filePath = '../school_hours/school_hours_report.csv';

    if (!file_exists($filePath)) {
        echo "CSV file not found at: " . realpath($filePath);
        exit;
    }

    if (($handle = fopen($filePath, "r")) !== FALSE) {
        fgetcsv($handle, 1000, ",");

        // Adjusted SQL statement
        $sql = "UPDATE SchSchoolHours 
                SET    StartTime = :StartTimeFrm,
                       EndTime = :EndTimeFrm,
                       TotalHours = :TotalHours 
                WHERE Id = :SchoolHoursId";

        $stmt = $conn->prepare($sql);

        $j = 0;
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $j++;
            echo "<pre>Row $j: ";
            print_r($data);
            echo "</pre>";

            $startTime12Hour = $data[4];
            $endTime12Hour = $data[5];

            // Convert to 24-hour format
            $startTime24Hour = DateTime::createFromFormat('g:i A', $startTime12Hour)->format('H:i:s');
            $endTime24Hour = DateTime::createFromFormat('g:i A', $endTime12Hour)->format('H:i:s');

            $stmt->bindParam(':SchoolHoursId', $data[0], PDO::PARAM_INT);
            $stmt->bindParam(':StartTimeFrm', $startTime24Hour, PDO::PARAM_STR);
            $stmt->bindParam(':EndTimeFrm', $endTime24Hour, PDO::PARAM_STR);
            $stmt->bindParam(':TotalHours', $data[6], PDO::PARAM_STR);

            try {
                $stmt->execute();
            } catch (PDOException $e) {
                echo "Error on row $j: " . $e->getMessage();
            }
        }
        fclose($handle);
        echo "School Hours were updated successfully.";
    } else {
        echo "Error opening the CSV file.";
    }
} catch (PDOException $e) {
    echo "Connection error: " . $e->getMessage();
}

$conn = null;
?>
