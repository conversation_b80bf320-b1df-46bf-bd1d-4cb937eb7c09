  
<?php

/*   
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);
*/ 

    
    ob_start();

    require_once('fpdf/fpdf.php');
    require_once('fpdi/fpdi.php');
    require_once("db_login.php");
    require_once('DB.php');
 
 

  //$MandateId = $_GET['MandateId'];
  
  $Data = $_GET['Data'];
  $Data=json_decode($Data,true);

 

     
 

 

  $Year = $_GET['Year'];
  $Month = $_GET['Month'];


    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

    
    if (DB::isError($connection)){
        die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 



  // initiate FPDI
     $pdf = new FPDI('P', 'pt', 'Letter');
    
    $pageNo = 1;

    $pageCount = $pdf->setSourceFile('CPSE_FORM.pdf');

    // import the "empty" form
    $templateId = $pdf->importPage($pageNo);

    $size = $pdf->getTemplateSize($templateId);

    // $size = $pdf->getTemplateSize($templateId);
    $pdf->SetAutoPageBreak(false);





  foreach ($Data as $StudentData) { // For Each Mandate - Start

    $MandateId = $StudentData['MandateId'];


    $pageNo++;

  
    $pdf->AddPage();

  
    
    $pdf->useTemplate($templateId);
      




  $x = 0;
 
    ++$x;



    //==================================
    // Get Company/Client Name
    //==================================
    
     //==================================
    // Get Company/Client Name
    //==================================
    
    $query = "SELECT  a.CompanyName as Agency_Name,
                      a.StreetAddress1  as Agency_Address1,
                      CONCAT(a.City, ' ', a.State, ' ', a.ZipCode) as Agency_Address2,
                      a.TIN as Agency_TaxId,
                      a.PhoneNumber as Agency_Phone,
                      a.Email as Agency_Email,
                      CONCAT( b.LastName, ', ', b.FirstName) as Student_Name,
                      b.ExtId as Student_NYC_Id,
                      DATE_FORMAT( b.DateOfBirth, '%m/%d/%Y' ) AS Student_DOB,
                      d.DistrictName as Student_District,
                      g.ServiceTypeDesc as Student_Service_Type,
                      DATE_FORMAT( f.StartDate, '%m-%d-%Y' ) as MandateStartDate, 
                      f.SessionFrequency,
                      f.SessionLength,
                      f.SessionGrpSize,
                      f.Language,
                      CONCAT( e.LastName, ', ', e.FirstName) as Provider_Name,
                      e.ExtId as Provider_ID,
                      '' as Provider_Title,
                      e.StreetAddress1  as Provider_Address1,
                      CONCAT(e.City, ' ', e.State, ' ', e.ZipCode) as Provider_Address2,

                      e.MobilePhone as Provider_Phone,
                      e.Email as Provider_Email,
                      c.ExtId as SchoolCode,
                      c.SchoolName,
                      /*
                      (SELECT i.Title  FROM  SchRegistrantSupervision h, Registrants i
                            WHERE e.Id = h.RegistrantId
                            AND   h.SupervisorId = i.Id  
                      ) as Supervisor_Title 
                      */
                      '' as  Supervisor_Title

                      FROM Company a,
                           SchStudents b,
                           SchSchools c,
                           SchDistricts d,
                           Registrants e,
                           SchStudentMandates f,
                           SchServiceTypes g
                      WHERE   f.Id = '{$MandateId}'  

                      AND b.Id = f.StudentId
                      AND   e.Id = f.RegistrantId 
                                         
                      AND   f.SchoolId = c.Id
                      AND   c.DistrictId = d.Id
                      AND   f.ServiceTypeId = g.Id ";
                
    
    $result = $connection->query ($query);
    while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
        $GLOBALS['Agency_Name'] = $row['Agency_Name'];
        $GLOBALS['Agency_Address1'] = $row['Agency_Address1']; 
        $GLOBALS['Agency_Address2'] = $row['Agency_Address2']; 

        $GLOBALS['Agency_TaxId'] = $row['Agency_TaxId'];
        $GLOBALS['Agency_Phone'] = $row['Agency_Phone'];
        $GLOBALS['Agency_Email'] = $row['Agency_Email'];
        $GLOBALS['Student_Name'] = $row['Student_Name'];        
        $GLOBALS['Student_NYC_Id'] = $row['Student_NYC_Id'];
        $GLOBALS['Student_DOB'] = $row['Student_DOB'];
        $GLOBALS['Student_District'] = $row['Student_District'];
        $GLOBALS['Student_Service_Type'] = $row['Student_Service_Type'];
        $GLOBALS['SessionFrequency'] = $row['SessionFrequency'];
        $GLOBALS['SessionLength'] = $row['SessionLength'];
        $GLOBALS['SessionGrpSize'] = $row['SessionGrpSize'];
        $GLOBALS['Language'] = $row['Language'];
        $GLOBALS['Provider_Name'] = $row['Provider_Name'];
        $GLOBALS['Provider_ID'] = $row['Provider_ID'];
        $GLOBALS['Provider_Title'] = $row['Provider_Title'];        
        $GLOBALS['Supervisor_Title'] = $row['Supervisor_Title'];        
        
        $GLOBALS['Provider_Address1'] = $row['Provider_Address1'];
        $GLOBALS['Provider_Address2'] = $row['Provider_Address2']; 
        $GLOBALS['Provider_Phone'] = $row['Provider_Phone'];
        $GLOBALS['Provider_Email'] = $row['Provider_Email'];
        $GLOBALS['SchoolCode'] = $row['SchoolCode'];
        $GLOBALS['SchoolName'] = $row['SchoolName'];
        $GLOBALS['MandateStartDate'] = $row['MandateStartDate'];
        
    }   

      /*=========  Month  =======*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(488, 35);
      $pdf->Cell(20, 10, $Month);

      /*=========  Year  =======*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(545, 35);
      $pdf->Cell(20, 10, $Year);
  
      /*=========  Student Name  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(135, 114);
      $pdf->Cell(20, 10,  $GLOBALS['Student_Name']);
 
      /*=========  Provider Name  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(415, 114);
      $pdf->Cell(20, 10,  $GLOBALS['Provider_Name']);


      /*=========  NYC ID  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(75, 127);
      $pdf->Cell(20, 10,  $GLOBALS['Student_NYC_Id']);

      /*=========  Provider Address1  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(350, 127);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Address1']);

      /*=========  Student DOB  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(87, 142);
      $pdf->Cell(20, 10,  $GLOBALS['Student_DOB']);


      /*=========  Student District  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(230, 142);
      $pdf->Cell(20, 10,  $GLOBALS['Student_District']);

      /*=========  Student Address2  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(350, 142);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Address2']);


      /*=========  Service Type ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(99, 156);
      $pdf->Cell(20, 10,  $GLOBALS['Student_Service_Type']);

      /*=========  Provider Phone # ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(370, 156);
      $pdf->Cell(20, 10,  '************');


      /*=========  Provider ID ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(500, 156);
      // $pdf->Cell(20, 10,  $GLOBALS['Provider_ID']);

      /*=========  Mandate Freq.  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(100, 185);
      $pdf->Cell(20, 10,  $GLOBALS['SessionFrequency']);

      /*=========  Mandate Dur.  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(240, 185);
      $pdf->Cell(20, 10,  $GLOBALS['SessionLength']);

      /*=========  Mandate Grp. Size  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(100, 200);
      $pdf->Cell(20, 10,  $GLOBALS['SessionGrpSize']);

      /*=========  Mandate Language  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(220, 200);
      $pdf->Cell(20, 10,  $GLOBALS['Language']);




      /*=========  Service Location  ======+*/    

      $service_location = trim($GLOBALS['SchoolName']).' ('.$GLOBALS['SchoolCode'].')';

      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(109, 215);
      $pdf->Cell(20, 10,  $service_location);

      /*=========  Agency Name  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(372, 229);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Name']);

      /*=========  Agency Address1  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(372, 247);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Address1']);

      /*=========  Student Assignment - Check 1  ======+*/    
/* 
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(35, 245);
      $pdf->Cell(20, 10,  'X');
*/
      /*=========  Student Assignment - Check 2  ======+*/    

      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(35, 280);
      $pdf->Cell(20, 10,  'X');

      /*=========  Agency Address2  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(372, 262);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Address2']);


      /*=========  Agency Contact Name  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(415, 280);
      $pdf->Cell(20, 10,  'Laryea, Andrea');

      /*=========  Agency Phone ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(370, 308);
      $pdf->Cell(20, 10,  '************');


      /*=========  Agency Email ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(474, 308);
      $pdf->Cell(20, 10,  '<EMAIL>');

      /*=========  Agency Tax ID  ======+*/    
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(374, 294);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_TaxId']);

/*
      $pdf->SetFont('Arial','B' ,6);
      $pdf->SetXY(372, 307);
      $pdf->Cell(20, 10,  $GLOBALS['Agency_Phone']);
*/



      //=======================
      // Service Details


   $query1 = "SELECT a.ServiceDate,  
                    DAYOFMONTH(a.ServiceDate) as DayOfMonth,
                    DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 
                    DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime, 
                    a.SessionGrpSize,
                  /*  
                    COALESCE((SELECT CONCAT(ParentSignatureName, ' (E.S.)') FROM SchCPSEFormSignaturesDetails b 
                              WHERE a.Id = b.ScheduleId LIMIT 1), '') as ParentSignatureName 
                  */
                  '' as ParentSignatureName            
            FROM  WeeklyServices a
              where  a.MandateId = '{$MandateId}'  
              and a.ScheduleStatusId > 6
              and YEAR(a.ServiceDate) = '{$Year}'  
              
             and   MONTH(a.ServiceDate) = '{$Month}'  
     ";
                
    
    $result1 = $connection->query ($query1);
    while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) { // Get Sessions - Start   

      $DayOfMonth =  $row1['DayOfMonth']; 
      
      if ($DayOfMonth == 1) { //Day: 01

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 407);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','' ,6);
        $pdf->SetXY(95, 407);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','' ,6);
        $pdf->SetXY(128, 407);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 407);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );


       } 


      if ($DayOfMonth == 2)  { //Day: 02

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 422);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']); 

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 422);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 422);
        $pdf->Cell(20, 10, $row1['EndTime']);


        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 422);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );


        } 

     if ($DayOfMonth == 3) { //Day: 03

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 437);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 437);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 437);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 437);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 4) { //Day: 04

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 452);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 452);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 452);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 452);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 5) { //Day: 05

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 467);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 467);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 467);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 467);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 6) { //Day: 06

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 482);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 482);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 482);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 482);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 7) { //Day: 07

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 497);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 497);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 497);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 497);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 8) { //Day: 08

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 512);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 512);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 512);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 512);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 9) { //Day: 09

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 527);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 527);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 527);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 527);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 10) { //Day: 10

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 542);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 542);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 542);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 542);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 11) { //Day: 11

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 557);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 557);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 557);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 557);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 12) { //Day: 12

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 572);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 572);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 572);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 572);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 13) { //Day: 13

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 587);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 587);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 587);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 587);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 14) { //Day: 14

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 602);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 602);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 602);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 602);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 15) { //Day: 15

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 617);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 617);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 617);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 617);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 16) { //Day: 16

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(73, 632);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(95, 632);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(128, 632);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(160, 632);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 


      if ($DayOfMonth == 17) { //Day: 17

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 407);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 407);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 407);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 407);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 18) { //Day: 18

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 422);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 422);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 422);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 422);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 19) { //Day: 19

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 437);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 437);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 437);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 437);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 20) { //Day: 20

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 452);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 452);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 452);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 452);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 21) { //Day: 21

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 467);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 467);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 467);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 467);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 22) { //Day: 22

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 482);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 482);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 482);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 482);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 23) { //Day: 23

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 497);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 497);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 497);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 497);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 24) { //Day: 24

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 512);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 512);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 512);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 512);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 25) { //Day: 25

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 527);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 527);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 527);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 527);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 26) { //Day: 26

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 542);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 542);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 542);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 542);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 27) { //Day: 27

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 557);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 557);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 557);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 557);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 28) { //Day: 28

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 572);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 572);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 572);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 572);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 29) { //Day: 29

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 587);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 587);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 587);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 587);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 30) { //Day: 30

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 602);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 602);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 602);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 602);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 

     if ($DayOfMonth == 31) { //Day: 331

        /*=========  Grp Size ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(350, 617);
        $pdf->Cell(20, 10, $row1['SessionGrpSize']);

        /*=========  Start Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(373, 617);
        $pdf->Cell(20, 10, $row1['StartTime']);

        /*=========  End Type ======+*/    
        $pdf->SetFont('Arial','B' ,6);
        $pdf->SetXY(405, 617);
        $pdf->Cell(20, 10, $row1['EndTime']);

        /*=========  Signature ======+*/    

        $pdf->SetXY(438, 617);
        $pdf->Cell(50, 10, $row1['ParentSignatureName'] );

      } 
    }  // Get Sessions - End   
  
  
  }

      /**********************/

      $pdf->Output();

      $connection->disconnect();
  
  
 

?>
 
