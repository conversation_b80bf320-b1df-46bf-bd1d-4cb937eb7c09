<?php 
	
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


  require_once("db_GetSetData.php");

  $conn = getCon();

  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  $ServiceTypeIdList = $_GET['ServiceTypeIdList'];
  
  $DistrictId = $_GET['DistrictId'];
  $SchoolId = $_GET['SchoolId'];
  $ServiceTypeId = $_GET['ServiceTypeId'];
  $RnId = $_GET['RnId'];
  $RNLiaisonId = $_GET['RNLiaisonId'];
  $PostingStatusId = $_GET['PostingStatusId'];
  $ConfirmationNumber = $_GET['ConfirmationNumber'];
 


 

  $query = "call proc_getSchRNSchoolReportsTransactions ( '{$FromDate}', 
                                                          '{$ToDate}', 
                                                          '{$ServiceTypeIdList}',
                                                          
                                                          '{$DistrictId}',
                                                          '{$SchoolId}',
                                                          '{$ServiceTypeId}',
                                                          '{$RnId}',
                                                          '{$RNLiaisonId}',
                                                          '{$PostingStatusId}',
                                                          '{$ConfirmationNumber}'  

                                                 ) "; 


  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  // echo $query;



  
  
?>

