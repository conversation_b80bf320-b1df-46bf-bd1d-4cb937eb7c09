      DELIMITER $$

      DROP PROCEDURE IF EXISTS proc_getSchRegistrantsUnBilledSessions$$

      CREATE PROCEDURE proc_getSchRegistrantsUnBilledSessions (IN  p_month INT, p_year INT, p_school_id VARCHAR(16), p_registrant_id VARCHAR(16)  ) 
                                   

      BEGIN


            create temporary table tmp

            Select a.Id as ScheduleId FROM  WeeklyServices  a, Registrants b 
           WHERE            month(ServiceDate) = p_month
                                      AND   year(ServiceDate) = p_year
                                      AND   a.BilledFL = '0'
                                    /*  and   a.AssignmentId = '0' */
                                      AND   a.SchoolId like p_school_id
                                      AND   a.RegistrantId like p_registrant_id
                                      AND   a.ScheduleStatusId >= 7 
                                      AND   a.RegistrantId = b.Id
                                      AND   TypeId != 23
                                      ;

                  
                  /*========== =========*/

            create temporary table tmp1
                 SELECT  DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                            a.ServiceDate AS ServiceDateSort,
                            DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                            a.StartTime as StartTimeSort,
                            DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                            a.EndTime as EndTimeSort,
                            FORMAT((a.TotalHours * 60), 0) as TotalHours,
                            a.TotalHours as TotalHoursUnf,
                            CONCAT(b.FirstName, ' ', b.LastName, ' (', b.Extid, ')' ) as StudentName,
                            a.Id  as ScheduleId,
                            COALESCE((SELECT CONCAT( c.SessionFrequency , ' X ', c.SessionLength, ' X ',c.SessionGrpSize) 
                              FROM SchStudentMandates c
                              WHERE a.MandateId = c.Id),'') as MandateDesc, 
                            
                            SessionGrpSize,
                            a.RegistrantId,
                            c.SearchId as RegistrantSearchId,
                            a.ScheduleStatusId,
                            ScheduleStatusDesc,
                            TextColor,
                            BackgroundColor,
                            a.SchoolId,
                            d.SchoolName as SchoolNameDBN,
                            f.SchoolName as SchoolName,
                            d.DistrictId,
                            (SELECT f.Id FROM   SchStudentMandates f
                                        WHERE  a.StudentId = f.StudentId
                                        AND    a.ServiceTypeId = f.ServiceTypeId LIMIT 1
                            )  as MandateId,                                
                            000.00 as BillRate,
                            000.00 as BillAmount,
                            a.ServiceTypeId,                        
                            ServiceTypeDesc,
                            COALESCE(BillingComments, '') as BillingComments, 
                            CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
                            
                            CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
                            DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

                        FROM  tmp j,
                              WeeklyServices a, 
                              SchStudents b,
                              Registrants c, 
                              ScheduleStatuses g,
                              RegistrantTypes h, 
                              SchSchools d,
                              SchSubSchools f,
                              Users e,
                              SchServiceTypes i
                              WHERE  j.ScheduleId = a.Id
                               AND   b.Id = a.StudentId
                              AND   a.ScheduleStatusId = g.Id
                              AND   a.RegistrantId = c.Id
                              AND   c.TypeId = h.Id
                              AND   a.SchoolId = d.Id   
                              AND   a.SchoolId = f.SchoolId
                              AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
                              AND   a.ServiceTypeId = i.Id                     
                              AND   a.UserId = e.UserId ;              

        UPDATE  tmp1 c, 
            SchDistrictServiceDetails a,
                    SchStudentMandates b 
                Set c.BillRate = a.BillRate,
                  BillAmount = ROUND((a.BillRate * TotalHoursUnf / c.SessionGrpSize),2)
        WHERE   c.MandateId = b.Id     
        AND     c.ServiceTypeId = a.ServiceTypeId    
        AND     a.DistrictId = c.DistrictId
        AND     a.BillingContractId = b.BillingContractId
        ;

        SELECT * FROM tmp1
        ORDER BY RegistrantName,  ServiceDateSort, EndTimeSort  ;
     
         

        drop temporary table if exists tmp;
        drop temporary table if exists tmp1;
         
        
      END $$

      DELIMITER ;   
       