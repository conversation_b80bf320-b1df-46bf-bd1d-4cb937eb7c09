
/*=========================================*/

DELIMITER $$

DROP TRIGGER IF EXISTS trig_SchStudentMandateUpdate  $$

CREATE TRIGGER trig_SchStudentMandateUpdate BEFORE UPDATE ON SchStudentMandates

FOR EACH ROW BEGIN
	 
        IF NEW.StatusId = 2 THEN
		
 			
			UPDATE SchStudentAssignmentHeader a
			SET a.StatusId = '2'  
			WHERE a.MandateId =  NEW.Id
            and a.Statusid = '1' ;
			
			
		END IF;
     
	
		 
	
END; $$

DELIMITER ;