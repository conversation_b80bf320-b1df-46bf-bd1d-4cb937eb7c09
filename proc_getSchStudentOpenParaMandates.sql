		DELIMITER $$

		DROP PROCEDURE IF EXISTS proc_getSchStudentOpenParaMandates$$

		CREATE PROCEDURE proc_getSchStudentOpenParaMandates ()
 
		BEGIN

			SELECT 	a.Id as id,
						a.Id as MandateId,
						StudentId, 
						b.SearchId as StudentSearchId,
						StudentExtId,
						CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
						COALESCE(CONCAT(trim(b.StreetAddress1), ' ',trim(b.StreetAddress2),' ', b.City, ', NY ',b.ZipCode, ' ',b.MobilePhone, ' ',b.HomePhone  ),'') as StudentInfo,
                        DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) as DateOfBirth,
                        b.<PERSON>,
                        b.<PERSON>,
 						CASE a.SchoolId 
							WHEN '0' THEN ''
						ELSE a.SchoolId
						END AS SchoolId,

						COALESCE((SELECT CONCAT(trim(SchoolName), ' ', trim(c.StreetAddress1),' ', c.City,' ', c.State,' ', c.<PERSON>ip<PERSON>,' ', c.OfficePhone) 
						      from SchSchools c
						   WHERE a.SchoolId = c.Id ),'') as SchoolName,  
						
						ServiceTypeDesc,  
				/*		
						CASE a.ParaTransportPerc
						WHEN '0' THEN ServiceTypeDesc
						ELSE CONCAT( ServiceTypeDesc , ' - ', ParaTransportPerc , '%')
						END AS ServiceTypeDesc,
				*/		
						ServiceTypeId,
						a.StatusId , 
						SECMandateStatus,
						DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
						DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,

 						SECMandateStatus,
						DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
						
						CASE a.ParaTransportPerc
						WHEN '0' THEN CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize )
						ELSE CONCAT( SessionFrequency , ' X ', ParaTransportPerc , ' %')
						END AS MandateDesc,
						
						
						Language,
						
						/*CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName, */
						
						COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
						      from Registrants c, RegistrantTypes f 
						   WHERE a.RegistrantId = c.Id 
						   AND c.TypeId = f.Id),'') as RegistrantName,  


              			a.RegistrantId,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						DOESchoolName,

						SESISParaServiceTypeId,

						
						DATE_FORMAT( ParaStartTimeMon1, '%l:%i %p' ) as ParaStartTimeMon1,
						DATE_FORMAT( ParaEndTimeMon1, '%l:%i %p' ) as ParaEndTimeMon1,
						ParaTotalHoursMon1,
						DATE_FORMAT( ParaStartTimeMon2, '%l:%i %p' ) as ParaStartTimeMon2,
						DATE_FORMAT( ParaEndTimeMon2, '%l:%i %p' ) as ParaEndTimeMon2,
						ParaTotalHoursMon2,

			            DATE_FORMAT( ParaStartTimeTue1, '%l:%i %p' ) as ParaStartTimeTue1,
			            DATE_FORMAT( ParaEndTimeTue1, '%l:%i %p' ) as ParaEndTimeTue1,
			            ParaTotalHoursTue1,
			            DATE_FORMAT( ParaStartTimeTue2, '%l:%i %p' ) as ParaStartTimeTue2,
			            DATE_FORMAT( ParaEndTimeTue2, '%l:%i %p' ) as ParaEndTimeTue2,
			            ParaTotalHoursTue2,

			            DATE_FORMAT( ParaStartTimeWed1, '%l:%i %p' ) as ParaStartTimeWed1,
			            DATE_FORMAT( ParaEndTimeWed1, '%l:%i %p' ) as ParaEndTimeWed1,
			            ParaTotalHoursWed1,
			            DATE_FORMAT( ParaStartTimeWed2, '%l:%i %p' ) as ParaStartTimeWed2,
			            DATE_FORMAT( ParaEndTimeWed2, '%l:%i %p' ) as ParaEndTimeWed2,
			            ParaTotalHoursWed2,

		                DATE_FORMAT( ParaStartTimeThu1, '%l:%i %p' ) as ParaStartTimeThu1,
		                DATE_FORMAT( ParaEndTimeThu1, '%l:%i %p' ) as ParaEndTimeThu1,
   		                ParaTotalHoursThu1,
	                    DATE_FORMAT( ParaStartTimeThu2, '%l:%i %p' ) as ParaStartTimeThu2,
	                    DATE_FORMAT( ParaEndTimeThu2, '%l:%i %p' ) as ParaEndTimeThu2,
	                    ParaTotalHoursThu2,

	                    DATE_FORMAT( ParaStartTimeFri1, '%l:%i %p' ) as ParaStartTimeFri1,
	                    DATE_FORMAT( ParaEndTimeFri1, '%l:%i %p' ) as ParaEndTimeFri1,
	                    ParaTotalHoursFri1,
	                    DATE_FORMAT( ParaStartTimeFri2, '%l:%i %p' ) as ParaStartTimeFri2,
	                    DATE_FORMAT( ParaEndTimeFri2, '%l:%i %p' ) as ParaEndTimeFri2,
	                    ParaTotalHoursFri2,
						
						a.UserId , 
						DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as TransDate 

				FROM 	SchStudentMandates a, 
						SchStudents b,
						SchBillingContracts c, 
						SchServiceTypes f,
					    Users e
					WHERE f.RegistrantTypeId = 23   
					AND a.StudentId = b.Id	
					AND a.AssignmentGeneratedFL = 0  
					AND a.ServiceTypeId = f.Id
					AND a.BillingContractId = c.Id
					AND a.UserId = e.UserId
				ORDER BY ServiceTypeId, b.LastName, b.FirstName	LIMIT 1000 ;
			 
			
		END $$

		DELIMITER ;		
		 