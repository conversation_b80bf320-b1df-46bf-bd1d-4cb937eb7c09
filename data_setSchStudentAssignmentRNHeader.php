<?php 


 	require_once("db_GetSetData.php");
    $conn = getCon();

	$StudentId = $_POST['StudentId'];
	$StatusId = $_POST['StatusId'];	
	$AssignmentId = $_POST['AssignmentId'];
	$AssignmentTypeId = $_POST['AssignmentTypeId'];
	$ConfirmationNumber = $_POST['ConfirmationNumber'];
	$StartDate = $_POST['StartDate'];
	$EndDate = $_POST['EndDate'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$IncludeSundayFL = $_POST['IncludeSundayFL'];
	$UserId = $_POST['UserId'];



	$query = "Call  proc_setSchStudentAssignmentRNHeader (	'{$StudentId}',
															'{$StatusId}',
															'{$AssignmentId}',
															'{$AssignmentTypeId}',
															'{$ConfirmationNumber}',
															'{$StartDate}',
															'{$EndDate}',
															'{$ServiceTypeId}',
															'{$IncludeSundayFL}',
															'{$UserId}')  "; 
       
     
   

    $ret =  setData ($conn, $query);        
 

    
  	setDisConn($conn);
  	echo $ret;
  	// echo $query;


	// require "ewDataHandler.php"; 
	  
	// $rcr_transaction = new dataHandler(); 

	// $StudentId = $_POST['StudentId'];
	// $StatusId = $_POST['StatusId'];	
	// $AssignmentId = $_POST['AssignmentId'];
	// $AssignmentTypeId = $_POST['AssignmentTypeId'];
	// $ConfirmationNumber = $_POST['ConfirmationNumber'];
	// $StartDate = $_POST['StartDate'];
	// $EndDate = $_POST['EndDate'];
	// $ServiceTypeId = $_POST['ServiceTypeId'];
	// $IncludeSundayFL = $_POST['IncludeSundayFL'];

	// $UserId = $_POST['UserId'];


	// $result = $rcr_transaction->setSchStudentAssignmentHeader(	$StudentId,
	// 															$StatusId,
	// 															$AssignmentId,
	// 															$AssignmentTypeId,
	// 															$ConfirmationNumber,
	// 															$StartDate,
	// 															$EndDate,
	// 															$ServiceTypeId,
	// 															$IncludeSundayFL,
	// 															$UserId ); 

	// $rcr_transaction->disconnectDB (); 

	// //echo  '{ success: true };
	// echo $result;

?>
