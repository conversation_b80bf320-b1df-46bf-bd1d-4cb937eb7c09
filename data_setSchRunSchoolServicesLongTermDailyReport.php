<?php
	
	require_once("db_login.php");
	require_once('DB.php');



	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	

	$ReportDate =  $_POST['ReportDate'];
	$Email =  $_POST['Email'];
	


 	$query1 = "SELECT DISTINCT	c.DistrictId
				FROM 	WeeklyServices a, 
						SchDistricts b,
						SchSchools c,
						SchServiceTypes d  
					WHERE AssignmentTypeId = '1'
					AND ServiceDate = '{$ReportDate}'
					AND a.ScheduleStatusId  not in (2,3,4)
					AND a.ServiceTypeId = d.Id
					AND ServiceCategoryId = '0'
					AND a.SchoolId = c.Id 
					AND c.DistrictId = b.Id	";
	
	$result1 = $connection->query ($query1);

	$reports_count =  $result1->numRows();


	if ($reports_count == 0) {

		echo  '0';
		return;

	}


	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {


		$DistrictId = $row1['DistrictId'];



		$exec_string = 'php -f data_setSchGenerateSchoolServicesLongTermDailyReportPDF.php '.$ReportDate.' '.$DistrictId.' '.$Email; 

	 	


		$output = exec($exec_string);	

	 
	}


	$connection->disconnect();



	echo  $reports_count;


?>