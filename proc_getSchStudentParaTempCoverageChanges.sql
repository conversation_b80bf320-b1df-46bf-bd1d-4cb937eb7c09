DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchStudentParaTempCoverageChanges$$

CREATE PROCEDURE proc_getSchStudentParaTempCoverageChanges (IN 	p_start_date date, 
 																	p_end_date date)  

BEGIN



 
 
	
 	DECLARE v_StudentId INT;  
 	DECLARE v_RegistrantId INT;  
 	DECLARE v_ServiceTypeId INT;  
 	DECLARE v_StartDate  DATE;  
 	DECLARE v_StartDatePlus1Day  DATE;  
 
 	DECLARE sv_StudentId INT;  
 	DECLARE sv_RegistrantId INT;  
 	DECLARE sv_MandateId INT; 
 	DECLARE sv_ServiceTypeId INT;   	 	 	  	
 	DECLARE sv_StartDate  DATE;  
 	DECLARE sv_EndDate  DATE;  
 	DECLARE sv_StartDatePlus1Day  DATE;  

	DECLARE done INT DEFAULT 0;
	

    
  	 
	
	/*============================================*/
	
	DECLARE cur CURSOR FOR
 

		SELECT  DISTINCT  a.StudentId,
                a.RegistrantId,
                CASE a.ServiceTypeId
                	WHEN '8' THEn '22'
                	WHEN '13' THEn '22'
                	ELSE  a.ServiceTypeId
                END as 	ServiceTypeId, 

                a.ServiceDate, 
                DATE_ADD(a.ServiceDate, INTERVAL 1 DAY) 

                  
			 from 	WeeklyServices a, 
					Registrants b, 
 			        SchStudents e 
 			 where ScheduleStatusId = 8
			and a.RegistrantId = b.Id
			 and   b.TypeId = 23
 			 and  a.StudentId = e.Id
			 and a.ServiceDate between p_start_date and p_end_date
            and a.RegistrantId !=      (select c.RegistrantId  FROM 
			        SchStudentMandates c ,
			        SchStudentAssignmentHeader d 
					where  a.AssignmentId = d.Id 
					and  d.MandateId =  c.Id )
 
 
			 Order BY a.StudentId, 	a.ServiceDate, a.RegistrantId 		;
       


	declare continue handler for not found set done := true;   
		 
	OPEN cur;


create temporary table tmp
    (
         
            StudentId INT, 
            RegistrantId INT,  
            ServiceTypeId INT,
            StartDate DATE,
            EndDate DATE 

    ); 
	
	read_loop: LOOP
 
       FETCH cur INTO 	v_StudentId,   
 	                    v_RegistrantId,  
 	                    v_ServiceTypeId,
					 	v_StartDate,  
					 	v_StartDatePlus1Day   ;
   
		IF done THEN
			LEAVE read_loop;
		END IF;
		
		IF (sv_StudentId IS NULL) THEN		

			SET sv_StartDate = v_StartDate;
			SET sv_EndDate = v_StartDate;
		 	SET sv_StartDatePlus1Day = v_StartDate;  
			SET sv_StudentId = v_StudentId;
			SET sv_RegistrantId = v_RegistrantId;
			SET sv_ServiceTypeId = v_ServiceTypeId ;
 



		END IF;
     
		/* Break in Student or Provider   */
		/*==========================================*/

		IF ((sv_StudentId != v_StudentId) || (sv_RegistrantId != v_RegistrantId) || (sv_StartDatePlus1Day != v_StartDate)) THEN

		INSERT INTO tmp
		VALUES (

            sv_StudentId, 
            sv_RegistrantId, 
            sv_ServiceTypeId, 
            sv_StartDate,
            sv_EndDate 

			   );

			SET sv_StartDate = v_StartDate;
			SET sv_EndDate = v_StartDate;
			SET sv_StartDatePlus1Day = v_StartDatePlus1Day;
			SET sv_StudentId = v_StudentId;
			SET sv_RegistrantId = v_RegistrantId;
			SET sv_ServiceTypeId = v_ServiceTypeId;
 

		END IF;	

		IF (sv_StartDatePlus1Day = v_StartDate) THEN

			SET sv_EndDate = v_StartDate;
			SET sv_StartDatePlus1Day =  DATE_ADD(sv_EndDate, INTERVAL 1 DAY) ;
 
		
		END IF;	
 
	
    END LOOP;
    CLOSE cur; 


		IF (sv_StartDatePlus1Day = v_StartDatePlus1Day) THEN

			SET sv_EndDate = v_StartDatePlus1Day;

		END IF;


		INSERT INTO tmp
		VALUES (

            sv_StudentId, 
            sv_RegistrantId, 
            sv_ServiceTypeId,
            sv_StartDate,
            sv_EndDate 

			   );


	 

	select  CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as 'Student Name',
	        b.Id as StudentId,
	        c.Id as RegistrantId,
	        d.Id as SchoolId,
            b.ExtId as 'Student OSIS #',
            CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as 'Provider Name',
 		    c.HrId as 'Provider HR ID',
 		    c.ExtId as 'Provider SSN',
            d.SchoolName as 'SchoolDBN', 
            i.SchoolName as 'School',
            DistrictName as 'District',
            ServiceTypeDesc as 'Service Type',
            (SELECT CONCAT( e.SessionGrpSize , ' ', e.SessionFrequency , 'xWeekly ', e.ParaTransportPerc,'%' ) 
            	FROM SchStudentMandates e 
               WHERE a.StudentId = e.StudentId
               AND   a.ServiceTypeId = e.ServiceTypeId
               AND   a.StartDate between  e.StartDate and e.EndDate LIMIT 1 	
            ) AS 'Mandate',

            COALESCE((SELECT count(*) from SchStudentParaTempCoverageReported i 

                 WHERE b.Id = i.StudentId
                 AND   c.Id = i.RegistrantId
                 AND   d.Id = i.SchoolId
                 AND   a.StartDate = i.StartDate
                 AND   a.EndDate = i.EndDate LIMIT 1

            ),0) as PrintFL,

            DATE_FORMAT( a.StartDate, '%m-%d-%Y' ) as 'Start Date', 
            DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) as 'End Date',
            a.StartDate  as 'StartDateUnf', 
            a.EndDate  as 'EndDateUnf'


    from  tmp a, 
          SchStudents b, 
          Registrants c,
          SchSchools d,
          SchSubSchools i,
          SchServiceTypes f,
          SchDistricts g
 		WHERE a.StudentId = b.Id
        and a.RegistrantId = c.Id 
        and b.SchoolId = d.Id
        and b.SchoolId = i.SchoolId
        AND b.SubSchoolTypeId = i.SubSchoolTypeId   
        and d.DistrictId = g.Id
        and a.ServiceTypeId = f.Id
    ORDER BY  b.LastName, b.FirstName,  a.StartDate  
        ;
 
	 

	 


 	drop temporary table if exists tmp;
 	 
	
END $$

DELIMITER ;	 
 
 