<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

ini_set('max_execution_time', '300'); // Increase execution time to 5 minutes
ini_set('memory_limit', '-1'); // Remove memory limit




require("db_login.php");
include('../../phpexcel-1-8/Classes/PHPExcel.php');
include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

$FromDate = $_GET['FromDate'];
$ToDate = $_GET['ToDate'];

// $FromDate = '2024-09-01';
// $ToDate = '2024-12-09';

 
$chunkSize = 1000; // Number of rows per chunkSize

$charset = 'utf8mb4';
$pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

// Initialize PHPExcel object
$objPHPExcel = new PHPExcel();
$objPHPExcel->setActiveSheetIndex(0);
$objPHPExcel->getActiveSheet()->getStyle("A1:N1")->getFont()->setSize(24);
$objPHPExcel->getActiveSheet()->getStyle("A1:N1")->getFont()->setBold(true);

$rowIndex = 4; // Start writing data from row 4
$headerWritten = false; // Track if the header row is written
$offset = 0; // Start at the beginning of the dataset

do {
    $sql = "
    SELECT  
        CASE a.PaidFl
            WHEN '1' THEN 'Paid'
            ELSE 'Not Paid'
        END AS 'Paid Status',
        CASE a.BilledFl
            WHEN '1' THEN 'Billed'
            ELSE 'Not Billed'
        END AS 'Billed Status',
        CONCAT(TRIM(e.LastName), ', ', TRIM(e.FirstName)) AS 'Registrant Name',
        DATE_FORMAT(a.ServiceDate, '%m-%d-%Y') AS 'Session Date',
        DATE_FORMAT(a.StartTime, '%l:%i %p') AS 'StartTime',
        DATE_FORMAT(a.EndTime, '%l:%i %p') AS 'End Time',
        COALESCE((SELECT SessionFrequency FROM SchStudentMandates i WHERE a.MandateId = i.Id), '5') AS 'Frequency',
        FORMAT((a.TotalHours * 60), 0) AS 'Duration',
        CASE SessionGrpSize
            WHEN '0' THEN '1'
            ELSE SessionGrpSize
        END AS 'Group Size',
        COALESCE(GROUP_CONCAT(CONCAT(b.FirstName, ' ', b.LastName) SEPARATOR '; '), '') AS StudentName,
        CASE e.TypeId
            WHEN '12' THEN d.SchoolName
            ELSE m.SchoolName
        END AS 'School Name',
        d.SchoolName AS 'School Name DBN',
        ServiceTypeDesc AS 'Service Type',
        n.BoroughName AS 'Borough',
        CASE e.TypeId
            WHEN '12' THEN 'RN'
            WHEN '23' THEN 'Para'
            ELSE 'Therapy'
        END AS 'Payroll Type' 
    FROM WeeklyServices a
        LEFT JOIN SchStudents b ON b.Id = a.StudentId
        JOIN Registrants e ON a.RegistrantId = e.Id
        JOIN ScheduleStatuses g ON a.ScheduleStatusId = g.Id
        JOIN SchSchools d ON a.SchoolId = d.Id
        JOIN SchServiceTypes h ON a.ServiceTypeId = h.Id
        LEFT JOIN SchSubSchools m ON a.SchoolId = m.SchoolId AND b.SubSchoolTypeId = m.SubSchoolTypeId
        JOIN SchDistricts j ON d.DistrictId = j.Id
        JOIN SchBoroughs n ON j.BoroughId = n.Id
    WHERE a.ServiceDate BETWEEN '{$FromDate}' AND '{$ToDate}' 
        AND a.ScheduleStatusId >= 8
        AND a.StudentId != 0
    GROUP BY a.RegistrantId, a.ServiceDate, a.StartTime, a.EndTime, a.TotalHours 
    LIMIT $chunkSize OFFSET $offset";

    echo "sql: $sql<br>";


    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!$headerWritten && !empty($results)) {
        // Write header row
        $column = 0;
        foreach (array_keys($results[0]) as $header) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, $rowIndex, $header);
            $column++;
        }
        $headerWritten = true;
        $rowIndex++;
    }

    // Write data rows
    foreach ($results as $result) {
        $column = 0;
        foreach ($result as $value) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, $rowIndex, $value);
            $column++;
        }
        $rowIndex++;
    }

    $offset += $chunkSize;

    unset($results);
    ob_flush();
    flush();

} while (!empty($stmt->rowCount()));

// Save the Excel file
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
$out_File = "../pr/payroll_transactions.xlsx";
$objWriter->save($out_File);

// Discard buffered output before sending headers
ob_clean();

echo 'OK'; 

// // Send the file to the client
// header('Content-Description: File Transfer');
// header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
// header('Content-Disposition: attachment; filename="payroll_transactions.xlsx"');
// header('Content-Length: ' . filesize($out_File));
// header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
// header('Expires: 0');
// header('Pragma: public');

// flush();
// readfile($out_File);
// unlink($out_File);
// exit();
