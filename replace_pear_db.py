#!/usr/bin/env python3
"""
Script to replace Pear DB calls with mysqli equivalents in ewDataHandler.php
"""

import re
import sys

def main():
    filename = 'ewDataHandler.php'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: Could not find file {filename}")
        return 1
    except Exception as e:
        print(f"Error reading file: {e}")
        return 1
    
    print(f"Original file size: {len(content)} bytes")
    original_content = content
    
    # Define replacement patterns
    replacements = [
        # Replace getAll calls with DB_FETCHMODE_ASSOC (more flexible pattern)
        (r'\$this->connection->getAll\(([^,]+),\s*DB_FETCHMODE_ASSOC\)', r'$this->getAll(\1, MYSQLI_ASSOC)'),

        # Replace DB::isError calls
        (r'DB::isError\(([^)]+)\)', r'$this->isError(\1)'),

        # Replace DB::errorMessage calls
        (r'DB::errorMessage\(([^)]+)\)', r'$this->errorMessage(\1)'),

        # Replace connection->query calls (but not the ones we already added)
        (r'\$this->connection->query\(', r'$this->query('),

        # Replace connection->disconnect calls
        (r'\$this->connection->disconnect\(\)', r'$this->connection->close()'),

        # Replace any remaining DB_FETCHMODE_ASSOC constants
        (r'DB_FETCHMODE_ASSOC', r'MYSQLI_ASSOC'),
    ]
    
    # Apply replacements
    total_replacements = 0
    for pattern, replacement in replacements:
        matches = re.findall(pattern, content)
        if matches:
            print(f"Pattern: {pattern}")
            print(f"Matches found: {len(matches)}")
            content = re.sub(pattern, replacement, content)
            total_replacements += len(matches)
        else:
            print(f"No matches for pattern: {pattern}")
    
    print(f"Total replacements made: {total_replacements}")
    
    # Remove the Pear DB require statement
    pear_require_pattern = r"require_once\s*\(\s*['\"]DB\.php['\"]\s*\)\s*;"
    if re.search(pear_require_pattern, content):
        content = re.sub(pear_require_pattern, '', content)
        print("Removed Pear DB require statement")
    
    # Write the modified content back to the file
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"File updated successfully!")
        print(f"New file size: {len(content)} bytes")
        print(f"Size change: {len(content) - len(original_content)} bytes")
    except Exception as e:
        print(f"Error writing to file: {e}")
        return 1
    
    print("Replacement complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
