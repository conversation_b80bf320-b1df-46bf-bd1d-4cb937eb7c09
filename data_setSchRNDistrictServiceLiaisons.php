<?php 
	

require_once("db_GetSetData.php");

	$conn = getCon();

	$DistrictId = $_POST['DistrictId'];  
  $RNLiaisonId = $_POST['RNLiaisonId'];
  $UserId = $_POST['UserId'];
    

  $Data = $_POST['Data'];
  $Data=json_decode($Data,true);
  
  foreach ($Data as $LiaisonData) {

	    $RNSchoolTypeId = $LiaisonData['RNSchoolTypeId'];
  	  $ServiceTypeId = $LiaisonData['ServiceTypeId'];

      $query = "INSERT INTO SchRNDistrictServiceLiaisons
				(
					DistrictId,
					RNSchoolTypeId,
					ServiceTypeId,
					RNLiaisonId,
					UserId 

				)
				
				VALUES
				(
				'{$DistrictId}',
				'{$RNSchoolTypeId}',
				'{$ServiceTypeId}',
				'{$RNLiaisonId}',
				'{$UserId}' 
                
        ) 	
				ON DUPLICATE KEY UPDATE
				   RNLiaisonId = '{$RNLiaisonId}'

          ";

	 
						$ret = setData ($conn, $query);
	
   }


	setDisConn($conn);

	echo $ret;


 
  
?>
