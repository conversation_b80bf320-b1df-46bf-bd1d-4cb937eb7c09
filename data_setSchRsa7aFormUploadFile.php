<?php

	
	ini_set('upload-max-filesize', '10M');
	ini_set('post_max_size', '10M');
	ini_set('memory_limit', '-1');
	
    require_once("db_GetSetData.php");
    $conn = getCon();

	
	
    $Rsa7aFormId = $_POST["RSA7aFormId"];
    $FileExt = $_POST["FileExt"];
 	$UserId = $_POST['UserId'];



	/* Generate Name for newaly uploaded file 
	 =============================================*/
	$new_file_name =  generateRandomString();
	$new_file_path =  '../em/'.$new_file_name.'.'.$FileExt;

	$db_new_file_name =  $new_file_name.'.'.$FileExt;

	/* Upload New File 
	 =============================================*/
		
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $new_file_path);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}


	
 
        $query ="UPDATE SchRsa7aFormSignatures  
                         
               SET   
                StatusId = '4',
                ParentApprovalEmailFileName =   '{$db_new_file_name}',      
                ParentSignatureTimeStamp = NOW(),
                UserId = '{$UserId}',
                TransDate = NOW()

            WHERE Id =   '{$Rsa7aFormId}'
             ";

 	        $ret =  setData ($conn, $query);        

    setDisConn($conn);

	//echo  "{ success: true, transactions: '{$linecount}'}";
	echo  "{ success: true, transactions: '1'}";

	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	
	
?>