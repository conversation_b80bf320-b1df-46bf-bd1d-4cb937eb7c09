<?php
	/** Error reporting */
/*  	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/  
	require_once('DB.php');
	include('db_login.php');
 
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


 

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


 
	$SchoolSeasonId = $_GET['SchoolSeasonId'];


	$objPHPExcel = new PHPExcel();

	 
	 $objPHPExcel->setActiveSheetIndex(0);
   $objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Date');
   $objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Start Date');
   $objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Provider');
   $objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Student');
   $objPHPExcel->getActiveSheet()->SetCellValue('E1', 'OSIS #');
   $objPHPExcel->getActiveSheet()->SetCellValue('F1', 'DOB');
   $objPHPExcel->getActiveSheet()->SetCellValue('G1', 'Mandate');
   $objPHPExcel->getActiveSheet()->SetCellValue('H1', 'Service Type');

   $objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Time In / AM Pick Up - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('I1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('J1', 'Lunch In / AM Drop Off - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('J1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('K1', 'Approved Hours - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('K1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('L1', 'Lunch Out / PM Pick Up - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('L1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('M1', 'Time Out / PM Drop Off - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('M1')->getAlignment()->setWrapText(true);   
   $objPHPExcel->getActiveSheet()->SetCellValue('N1', 'Approved Hours - Mon');
   $objPHPExcel->getActiveSheet()->getStyle('N1')->getAlignment()->setWrapText(true);


   $objPHPExcel->getActiveSheet()->SetCellValue('O1', 'Time In / AM Pick Up - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('O1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('P1', 'Lunch In / AM Drop Off - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('P1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('Q1', 'Approved Hours - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('Q1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('R1', 'Lunch Out / PM Pick Up - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('R1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('S1', 'Time Out / PM Drop Off - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('S1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('T1', 'Approved Hours - Tue');
   $objPHPExcel->getActiveSheet()->getStyle('T1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('U1', 'Time In / AM Pick Up - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('U1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('V1', 'Lunch In / AM Drop Off - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('V1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('W1', 'Approved Hours - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('W1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('X1', 'Lunch Out / PM Pick Up - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('X1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('Y1', 'Time Out / PM Drop Off - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('Y1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('Z1', 'Approved Hours - Wed');
   $objPHPExcel->getActiveSheet()->getStyle('Z1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('AA1', 'Time In / AM Pick Up - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AA1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AB1', 'Lunch In / AM Drop Off - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AB1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AC1', 'Approved Hours - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AC1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AD1', 'Lunch Out / PM Pick Up - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AD1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AE1', 'Time Out / PM Drop Off - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AE1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AF1', 'Approved Hours - Thu');
   $objPHPExcel->getActiveSheet()->getStyle('AF1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('AG1', 'Time In / AM Pick Up - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AG1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AH1', 'Lunch In / AM Drop Off - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AH1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AI1', 'Approved Hours - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AI1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AJ1', 'Lunch Out / PM Pick Up - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AJ1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AK1', 'Time Out / PM Drop Off - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AK1')->getAlignment()->setWrapText(true);
   $objPHPExcel->getActiveSheet()->SetCellValue('AL1', 'Approved Hours - Fri');
   $objPHPExcel->getActiveSheet()->getStyle('AL1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('AM1', 'Student Information');
   $objPHPExcel->getActiveSheet()->getStyle('AM1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('AN1', 'Medical Needs');
   $objPHPExcel->getActiveSheet()->getStyle('AN1')->getAlignment()->setWrapText(true);

   $objPHPExcel->getActiveSheet()->SetCellValue('AO1', 'School Info');
   $objPHPExcel->getActiveSheet()->getStyle('AO1')->getAlignment()->setWrapText(true);

/*
   $objPHPExcel->getActiveSheet()->SetCellValue('AP1', 'Override School Info');
   $objPHPExcel->getActiveSheet()->getStyle('AP1')->getAlignment()->setWrapText(true);
*/
   $objPHPExcel->getActiveSheet()->SetCellValue('AP1', 'Comments');
   $objPHPExcel->getActiveSheet()->getStyle('AP1')->getAlignment()->setWrapText(true);


	$query = "SELECT       DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as 'TransDate', 
             DATE_FORMAT( StartDate, '%m-%d-%Y' ) as 'StartDate',
             COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
                  from Registrants c, RegistrantTypes f 
               WHERE a.RegistrantId = c.Id 
               AND c.TypeId = f.Id),'') as 'ProviderName',  
            CONCAT( trim( b.LastName) , ', ', trim(b.FirstName),' Guardian Info : ', CONCAT( trim( GuardianFirstName) , ' ', trim(GuardianLastName)), ' ',GuardianPhone, ' ',GuardianEmail ) as 'StudentName', 
            StudentExtId as 'OSIS #',
            COALESCE(CONCAT(trim(b.StreetAddress1), ' ',trim(b.StreetAddress2),' ', b.City, ', NY ',b.ZipCode, ' ',b.MobilePhone, ' ',b.HomePhone  ),'') as 'StudentInfo',
                        DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) as 'DOB',
            CASE a.ParaTransportPerc
            WHEN '0' THEN CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize )
            ELSE CONCAT( SessionFrequency , ' X ', ParaTransportPerc , ' %')
            END AS 'MandateDesc',
            ServiceTypeDesc as 'ServiceTypeDesc' ,
            

            CASE a.ParaStartTimeMon1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeMon1, '%l:%i %p' )
            END AS ParaStartTimeMon1, 

            CASE a.ParaEndTimeMon1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeMon1, '%l:%i %p' )
            END AS ParaEndTimeMon1,
            COALESCE(ParaTotalHoursMon1,'') as ParaTotalHoursMon1,  
  
            CASE a.ParaStartTimeMon2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeMon2, '%l:%i %p' )
            END AS ParaStartTimeMon2, 

            CASE a.ParaEndTimeMon2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeMon2, '%l:%i %p' )
            END AS ParaEndTimeMon2,
            COALESCE(ParaTotalHoursMon2,'') as ParaTotalHoursMon2,  

            CASE a.ParaStartTimeTue1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeTue1, '%l:%i %p' )
            END AS ParaStartTimeTue1, 

            CASE a.ParaEndTimeTue1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeTue1, '%l:%i %p' )
            END AS ParaEndTimeTue1,
            COALESCE(ParaTotalHoursTue1,'') as ParaTotalHoursTue1,  
  
            CASE a.ParaStartTimeTue2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeTue2, '%l:%i %p' )
            END AS ParaStartTimeTue2, 

            CASE a.ParaEndTimeTue2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeTue2, '%l:%i %p' )
            END AS ParaEndTimeTue2,
            COALESCE(ParaTotalHoursTue2,'') as ParaTotalHoursTue2,  

            CASE a.ParaStartTimeWed1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeWed1, '%l:%i %p' )
            END AS ParaStartTimeWed1, 

            CASE a.ParaEndTimeWed1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeWed1, '%l:%i %p' )
            END AS ParaEndTimeWed1,
            COALESCE(ParaTotalHoursWed1,'') as ParaTotalHoursWed1,  
  
            CASE a.ParaStartTimeWed2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeWed2, '%l:%i %p' )
            END AS ParaStartTimeWed2, 

            CASE a.ParaEndTimeWed2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeWed2, '%l:%i %p' )
            END AS ParaEndTimeWed2,
            COALESCE(ParaTotalHoursWed2,'') as ParaTotalHoursWed2,  

            CASE a.ParaStartTimeThu1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeThu1, '%l:%i %p' )
            END AS ParaStartTimeThu1, 

            CASE a.ParaEndTimeThu1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeThu1, '%l:%i %p' )
            END AS ParaEndTimeThu1,
            COALESCE(ParaTotalHoursThu1,'') as ParaTotalHoursThu1,  
 
            CASE a.ParaStartTimeThu2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeThu2, '%l:%i %p' )
            END AS ParaStartTimeThu2, 

            CASE a.ParaEndTimeThu2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeThu2, '%l:%i %p' )
            END AS ParaEndTimeThu2,
            COALESCE(ParaTotalHoursThu2,'') as ParaTotalHoursThu2,  

            CASE a.ParaStartTimeFri1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeFri1, '%l:%i %p' )
            END AS ParaStartTimeFri1, 

            CASE a.ParaEndTimeFri1 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeFri1, '%l:%i %p' )
            END AS ParaEndTimeFri1,
            COALESCE(ParaTotalHoursFri1,'') as ParaTotalHoursFri1,  
  
            CASE a.ParaStartTimeFri2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaStartTimeFri2, '%l:%i %p' )
            END AS ParaStartTimeFri2, 

            CASE a.ParaEndTimeFri2 
              WHEN '00:00:00' THEN ''
            ELSE DATE_FORMAT( ParaEndTimeFri2, '%l:%i %p' )
            END AS ParaEndTimeFri2,
            COALESCE(ParaTotalHoursFri2,'') as ParaTotalHoursFri2,  
            
            COALESCE(CONCAT(trim(b.StreetAddress1), ' ',trim(b.StreetAddress2),' ', b.City, ', NY ',b.ZipCode, ' ',b.MobilePhone, ' ',b.HomePhone  ),'') as 'StudentInfo',
            COALESCE(b.MedicalNeeds,'') as MedicalNeeds,
            
        /*    
            COALESCE((SELECT CONCAT(trim(SchoolName), ' (District: ',DistrictName,') ', trim(c.StreetAddress1),' ', c.City,' ', c.State,' ', c.ZipCode,' ',  c.LiaisonFirstName, ' ',  c.LiaisonLastName, ' ',   c.OfficePhone) 
                  from SchSchools c, SchDistricts d
               WHERE b.SchoolId = c.Id 
               AND c.DistrictId = d.Id),'') as SchoolName,   
        */
        COALESCE((SELECT CONCAT(trim(e.SchoolName),
               ' (District: ',DistrictName,') ',
               trim(e.StreetAddress1),' ', e.City,' ', e.State,' ', e.ZipCode,' ',
               e.LiaisonFirstName, ' ',  e.LiaisonLastName, ' ',
               e.OfficePhone,' ', e.MobilePhone,' ',e.Email ) 
            from SchSchools c, SchDistricts d, SchSubSchools e
         WHERE b.SchoolId = c.Id 
         AND c.DistrictId = d.Id
         AND b.SchoolId = e.SchoolId
         AND e.SubSchoolTypeId = b.SubSchoolTypeId  
         ),'') as SchoolName,   

        /*
            CONCAT(trim(OverrideSchoolName), ' ', trim(OverrideDistrict),' ',trim(OverrideSchoolAddress1),' ', OverrideSchoolCity,' ', OverrideSchoolZipCode,' ', 
            OverrideSchoolContactName,' ', OverrideSchoolContactPhone) 
                  as OverrideSchoolInfo,  
        */
             b.Comments      

				FROM 	SchStudentMandates a, 
						SchStudents b,
						SchSchoolYear c,
						SchServiceTypes f,
					    Users e
					WHERE a.StudentId = b.Id	
					AND a.ServiceTypeId = f.Id
					AND a.UserId = e.UserId
					AND SESISParaServiceTypeId != 0 
					AND a.AssignmentGeneratedFL = 1
					AND c.Id = '{$SchoolSeasonId}'
					AND StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate
 
				ORDER BY ServiceTypeId, b.LastName, b.FirstName	"; 



	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 1;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$linecount++;


            $row_num++;

		    //$objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['ParaStartTimeMon1']);


          
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['TransDate']);
		  
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['StartDate']);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['ProviderName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['StudentName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['OSIS #']);
		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $row['DOB']);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['MandateDesc']);
		    $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['ServiceTypeDesc']);
		 
		    $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['ParaStartTimeMon1']);
		 
		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['ParaEndTimeMon1']);
		    $objPHPExcel->getActiveSheet()->setCellValue('K'.$row_num, $row['ParaTotalHoursMon1']);
		    $objPHPExcel->getActiveSheet()->setCellValue('L'.$row_num, $row['ParaStartTimeMon2']);
		    $objPHPExcel->getActiveSheet()->setCellValue('M'.$row_num, $row['ParaEndTimeMon2']);
		    $objPHPExcel->getActiveSheet()->setCellValue('N'.$row_num, $row['ParaTotalHoursMon2']);
		 
        $objPHPExcel->getActiveSheet()->setCellValue('O'.$row_num, $row['ParaStartTimeTue1']);
        $objPHPExcel->getActiveSheet()->setCellValue('P'.$row_num, $row['ParaEndTimeTue1']);
        $objPHPExcel->getActiveSheet()->setCellValue('Q'.$row_num, $row['ParaTotalHoursTue1']);
        $objPHPExcel->getActiveSheet()->setCellValue('R'.$row_num, $row['ParaStartTimeTue2']);
        $objPHPExcel->getActiveSheet()->setCellValue('S'.$row_num, $row['ParaEndTimeTue2']);
        $objPHPExcel->getActiveSheet()->setCellValue('T'.$row_num, $row['ParaTotalHoursTue2']);

        $objPHPExcel->getActiveSheet()->setCellValue('U'.$row_num, $row['ParaStartTimeWed1']);
        $objPHPExcel->getActiveSheet()->setCellValue('V'.$row_num, $row['ParaEndTimeWed1']);
        $objPHPExcel->getActiveSheet()->setCellValue('W'.$row_num, $row['ParaTotalHoursWed1']);
        $objPHPExcel->getActiveSheet()->setCellValue('X'.$row_num, $row['ParaStartTimeWed2']);
        $objPHPExcel->getActiveSheet()->setCellValue('Y'.$row_num, $row['ParaEndTimeWed2']);
        $objPHPExcel->getActiveSheet()->setCellValue('Z'.$row_num, $row['ParaTotalHoursWed2']);
 
        $objPHPExcel->getActiveSheet()->setCellValue('AA'.$row_num, $row['ParaStartTimeThu1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AB'.$row_num, $row['ParaEndTimeThu1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AC'.$row_num, $row['ParaTotalHoursThu1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AD'.$row_num, $row['ParaStartTimeThu2']);
        $objPHPExcel->getActiveSheet()->setCellValue('AE'.$row_num, $row['ParaEndTimeThu2']);
        $objPHPExcel->getActiveSheet()->setCellValue('AF'.$row_num, $row['ParaTotalHoursThu2']);

        $objPHPExcel->getActiveSheet()->setCellValue('AG'.$row_num, $row['ParaStartTimeFri1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AH'.$row_num, $row['ParaEndTimeFri1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AI'.$row_num, $row['ParaTotalHoursFri1']);
        $objPHPExcel->getActiveSheet()->setCellValue('AJ'.$row_num, $row['ParaStartTimeFri2']);
        $objPHPExcel->getActiveSheet()->setCellValue('AK'.$row_num, $row['ParaEndTimeFri2']);
        $objPHPExcel->getActiveSheet()->setCellValue('AL'.$row_num, $row['ParaTotalHoursFri2']);

        if ($row['StudentInfo'] == '  , NY   ') {

          $student_info = ''; 
        } else {

           $student_info = $row['StudentInfo']; 

        }


        $objPHPExcel->getActiveSheet()->setCellValue('AM'.$row_num, $student_info);

  
        $medical_needs = $connection->escapeSimple($row['MedicalNeeds']); 

        //echo 'Student: '.$row['StudentName'].' $medical_needs: '.$medical_needs.'</br>';  

        $objPHPExcel->getActiveSheet()->setCellValue('AN'.$row_num, $medical_needs);

        $school_name = $connection->escapeSimple($row['SchoolName']); 
        $objPHPExcel->getActiveSheet()->setCellValue('AO'.$row_num, $school_name);

      //  $overr_school_name = $connection->escapeSimple($row['OverrideSchoolInfo']); 
      //  $objPHPExcel->getActiveSheet()->setCellValue('AP'.$row_num, $overr_school_name);
   
        //$comments = $connection->escapeSimple($row['Comments']); 
        $comments = $row['Comments'];
        $objPHPExcel->getActiveSheet()->setCellValue('AP'.$row_num, $comments);

	}	

 
		$connection->disconnect();
 


 

	/*=========================================================================*/

// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";

$SheetName = 'Covered Mandates';
$objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/CoveredMandates.xlsx";

$objWriter->save($out_File);

// Echo done
echo  "{ success: true, transactions: '{$linecount}'}";

/*==============================*/
   
 
   $DownloadedFileName = 'CoveredParaMandatesFile-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    //unlink($out_File);    

    exit;
 
   

?>