<?php 
	
  require_once("db_GetSetData.php");

  $conn = getCon();

  $ServiceDate = $_GET['ServiceDate'];
   
  $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchRNDailyReportEmail.php";


  $query = "SELECT DISTINCT
              i.DistrictId, 
              TRIM(j.DistrictName) AS DistrictName,
              concat(j.LiaisonFirstName,' ',j.LiaisonLastName) as LiaisonName,
              j.Email as LiaisonEmail
          FROM
              WeeklyServices a
                  JOIN
              SchSchools i ON a.SchoolId = i.Id
                  JOIN
              SchDistricts j ON i.DistrictId = j.Id
          WHERE
              a.ServiceDate = '{$ServiceDate}'
                  AND a.ServiceTypeId BETWEEN 39 AND 42 
                  -- AND j.Email != ''
                  LIMIT 1  
"; 
  
  // echo 'query: '.$query;


  $result =  mysqli_query($conn, $query);
     
  while ($row = $result->fetch_assoc()) {
   
    
      $DistrictId = $row['DistrictId'];
      $DistrictName = $row['DistrictName'];
      $LiaisonName = $row['LiaisonName'];
      $LiaisonEmail = $row['LiaisonEmail'];

      $fields = array('ServiceDate' => $ServiceDate, 
                      'DistrictId' => $DistrictId,
                      'DistrictName' => $DistrictName, 
                      'LiaisonName' => $LiaisonName,
                      'LiaisonEmail' => $LiaisonEmail  
                   );


     
     // print_r($fields);

     $curl = curl_init();
       
     curl_setopt($curl, CURLOPT_URL, $url);
     curl_setopt($curl, CURLOPT_POSTFIELDS, $fields); 
     curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false

     $str = curl_exec($curl);

     echo '$str :'.$str;
        
     curl_close($curl);

    }



  setDisConn($conn);

  echo $ret;


  
?>

