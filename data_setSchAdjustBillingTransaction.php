<?php 


require "ewDataHandler.php";
  
$rcr_transaction = new dataHandler(); 

$ScheduleId =  $_POST['ScheduleId'];
$StartTime =  $_POST['StartTime'];
$EndTime =  $_POST['EndTime'];	
$TotalHours =  $_POST['TotalHours'];	
$ConfirmationNumber =  $_POST['ConfirmationNumber'];
$InvoiceNumber =  $_POST['InvoiceNumber'];
$BillingComments =  $_POST['BillingComments'];
$InvoiceNumberExt =  $_POST['InvoiceNumberExt'];

$UserId = $_POST['UserId'];


$result = $rcr_transaction->setSchAdjustBillingTransaction(	$ScheduleId,
															$StartTime,
															$EndTime,
															$TotalHours,
															$ConfirmationNumber,
															$InvoiceNumber,
															$BillingComments,
															$InvoiceNumberExt,
															$UserId); 
												

$rcr_transaction->disconnectDB (); 

echo $result;

?>
