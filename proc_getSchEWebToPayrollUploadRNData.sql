			DELIMITER $$

			DROP PROCEDURE IF EXISTS proc_getSchEWebToPayrollUploadRNData$$

			CREATE PROCEDURE proc_getSchEWebToPayrollUploadRNData (IN 	p_payroll_batch_number INT,
		                                                                p_billing_contract_id INT,
		                                                                p_billing_contract_name VARCHAR(54)
		 													    )	    
		 														    

			BEGIN



	
				 create temporary table tmp_batch
 			   SELECT RegistrantId,
			          ServiceDate,
			          StartTime,
			          EndTime,
			          ServiceTypeId,
			          SchoolId,
			          StudentId,
			          TotalHours,
			          ConfirmationNumber

			    from WeeklyServices
			   WHERE PayrollBatchNumber =   p_payroll_batch_number
			   AND ServiceDate >= curdate() - interval 1 year ;


				create temporary table tmp

				SELECT   distinct          a.RegistrantId,
		                CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName,
		                HrId,
		                DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
		                a.TotalHours, 
		                
		                CONCAT(TRIM(c.SchoolName),' (',d.DistrictName,') ') as   SchoolName,
		                CONCAT(TRIM(c.SchoolName),' (',d.DistrictName,') ') as   SchoolNameDBN,
						CONCAT( trim( e.LastName) , ', ', trim( e.FirstName), ' (',e.ExtId,')') as StudentName,
						

		                d.DistrictName,
	 
		                DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
		                a.ServiceTypeId,
		                ServiceTypeDesc,
		                p_billing_contract_name as BillingContractName,
		                n.BoroughCode,
		                b.RNLicenseNumber,
		        	    a.ConfirmationNumber

		        FROM  tmp_batch a   
                         JOIN
		              Registrants b ON a.RegistrantId = b.id
                         JOIN
		              SchSchools c ON a.SchoolId = c.Id
                         JOIN 
		              SchDistricts d ON c.DistrictId = d.Id
                         LEFT JOIN
		              SchStudents e ON a.StudentId = e.Id
                         JOIN
		              SchServiceTypes g ON a.ServiceTypeId = g.Id
                         LEFT JOIN
		              SchSubSchools m ON a.SchoolId = m.SchoolId 
                                     and e.SubSchoolTypeId = m.SubSchoolTypeId
 	 		          	JOIN 
 	 		          SchBoroughs n ON d.BoroughId = n.Id
		        ;
			 			
			 			
				SELECT RegistrantName,
				       HrId,
				       ServiceDate,
				       StartTime,
				       EndTime,
				       SchoolName,
				       case  
				          when StudentName is not null  then StudentName
				          else   SchoolNameDBN
				       end as  SchoolNameDBN,
				       DistrictName,
				       ServiceTypeId,
				       ServiceTypeDesc,
				       BillingContractName,
				       SUM(TotalHours) as TotalHours,
				       BoroughCode,
				       RNLicenseNumber,
				       ConfirmationNumber
				FROM   tmp
				GROUP BY RegistrantName,
				      	 HrId,
				         ServiceDate,
				         SchoolName,
				         ServiceTypeId
				          ;   	

 	         	
			 
				drop temporary table if exists tmp;
				drop temporary table if exists tmp_batch;
				 
				
			END $$

			DELIMITER ;		
			 