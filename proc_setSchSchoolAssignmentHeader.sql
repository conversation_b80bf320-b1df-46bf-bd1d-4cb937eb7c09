

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_setSchSchoolAssignmentHeader$$

	CREATE PROCEDURE proc_setSchSchoolAssignmentHeader (IN 	p_school_id INT, 
															p_status_id INT,
															p_assignment_id INT,
															p_assignment_type_id INT,
															p_conf_number VARCHAR(32),
															p_start_date DATE,
															p_end_date DATE,
															p_service_type_id INT,
															p_user_id BIGINT)  


BEGIN


	DECLARE v_Existing_ServType_AssignId INT;


	/* Get Assigment ID if Service Type Already Exists 
	  =================================================*/ 	

 	
	SELECT count(*) INTO v_Existing_ServType_AssignId
		FROM SchSchoolAssignmentHeader
	WHERE SchoolId = p_school_id
	AND   ServiceTypeId = p_service_type_id ;	  

 
	/*IF (v_Existing_ServType_AssignId > 0) THEN /* Existing Service Type - Update  */
	IF (p_assignment_id > 0) THEN /* Existing Service Type - Update  */
  	
		/*====*/
			UPDATE SchSchoolAssignmentHeader
			 
			SET		StatusId = p_status_id,
					AssignmentTypeId = p_assignment_type_id,
					ConfirmationNumber = p_conf_number,
					StartDate = p_start_date,
					EndDate = p_end_date,
					UserId = p_user_id,
					TransDate = NOW()
			WHERE Id = p_assignment_id ;


			SELECT 'update';


		/*====*/


 	ELSE /* New Service Type - Insert */
	
		/*====*/
 					INSERT INTO SchSchoolAssignmentHeader
					( 
					SchoolId,
					ServiceTypeId,
					StatusId,
					AssignmentTypeId,
					ConfirmationNumber,
					StartDate,
					EndDate,
					UserId,
					TransDate)
					VALUES
					(
					p_school_id,
					p_service_type_id,
					p_status_id,
					p_assignment_type_id,
					p_conf_number,
					p_start_date,
					p_end_date,
					p_user_id,
					NOW() );

			SELECT 'Insert';


		/*====*/
	END IF;
 
END $$

	DELIMITER ;	