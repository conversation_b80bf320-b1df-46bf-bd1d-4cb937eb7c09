<?php


	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
 
 
// require_once("IMAP_access.php");

	$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
	$username = '<EMAIL>';
	$password = 'tyaffqbzbhgsfhbp';
  	//$username = '<EMAIL>';
	//$password = 'SSGTeam123!';





$connection = imap_open($hostname, $username, $password);
$emails = imap_search($connection,'UNSEEN');






if($emails) {
	
	/* begin output var */
	$output = '';
	
	/* put the newest emails on top */
	rsort($emails);
	
	/* for every email... */
	foreach($emails as $messageNumber) {

//	$headers = imap_fetchheader($connection, $messageNumber, FT_PREFETCHTEXT);
    $body_eml = imap_body($connection, $messageNumber);	

	//echo ' body_eml: '.$body_eml.'</br></br>';


	$structure = imap_fetchstructure($connection, $messageNumber);
	
	var_dump($structure->parts).'</br></br>';

	$flattenedParts = flattenParts($structure->parts);

	//print_r($flattenedParts);

	$x = 0;
		foreach($flattenedParts as $partNumber => $part) { // $flattenedParts - start

			
			$x++;
			echo  $x.'  part->type: '.$part->type.'</br></br>';


			switch($part->type) {
				

				case 0:
					// the HTML or plain text part of the email
					$message = getPart($connection, $messageNumber, $partNumber, $part->encoding);
					
					//var_dump($message).'</br></br>';

					if ($part->subtype == 'HTML') {
						$embeded_string = strstr($message, '#d');
	        			echo '$embeded_string: '.$embeded_string.'</br>'; 

				        $MandateId = get_string_between($embeded_string, '#d', 'd$');
				        
				        $Month = get_string_between($embeded_string, '#m', 'm$');
				        $Year = get_string_between($embeded_string, '#y', 'y$');

				        $FormFromDate = $Year.'-'.$Month.'-01';
 
 				        echo '$MandateId: '.$MandateId.' Month: '.$Month.' Year: '.$Year.'</br>'; 


        			}

					if ($part->subtype == 'HTML') {

						$new_file_name =  generateRandomString();
						$new_file_path =  '../em/'.$new_file_name.'.eml';
						$db_new_file_name =  $new_file_name.'.eml';

						//file_put_contents($new_file_path, $headers . "\n" . $body_eml);

					}


					// now do something with the message, e.g. render it
				break;
			
				case 1:
					// multi-part headers, can ignore
			
				break;
				case 2:
					// attached message headers, can ignore
				break;
			
				case 3: // application
				case 4: // audio
				case 5: // image
				case 6: // video
				case 7: // other
					$filename = getFilenameFromPart($part);
					if($filename) {
						// it's an attachment
						$attachment = getPart($connection, $messageNumber, $partNumber, $part->encoding);
						// now do something with the attachment, e.g. save it somewhere
					}
					else {
						// don't know what it is
					}
				break;
			
			}
			
		} // $flattenedParts - end
	}	


}


	imap_close($connection);
	

 
 

	function flattenParts($messageParts, $flattenedParts = array(), $prefix = '', $index = 1, $fullPrefix = true) {

		foreach($messageParts as $part) {
			$flattenedParts[$prefix.$index] = $part;
			if(isset($part->parts)) {
				if($part->type == 2) {
					$flattenedParts = flattenParts($part->parts, $flattenedParts, $prefix.$index.'.', 0, false);
				}
				elseif($fullPrefix) {
					$flattenedParts = flattenParts($part->parts, $flattenedParts, $prefix.$index.'.');
				}
				else {
					$flattenedParts = flattenParts($part->parts, $flattenedParts, $prefix);
				}
				unset($flattenedParts[$prefix.$index]->parts);
			}
			$index++;
		}

		return $flattenedParts;
				
	}



	function getPart($connection, $messageNumber, $partNumber, $encoding) {
		
		$data = imap_fetchbody($connection, $messageNumber, $partNumber);
		switch($encoding) {
			case 0: return $data; // 7BIT
			case 1: return $data; // 8BIT
			case 2: return $data; // BINARY
			case 3: return base64_decode($data); // BASE64
			case 4: return quoted_printable_decode($data); // QUOTED_PRINTABLE
			case 5: return $data; // OTHER
		}
		
		
	}

	function getFilenameFromPart($part) {

		$filename = '';
		
		if($part->ifdparameters) {
			foreach($part->dparameters as $object) {
				if(strtolower($object->attribute) == 'filename') {
					$filename = $object->value;
				}
			}
		}

		if(!$filename && $part->ifparameters) {
			foreach($part->parameters as $object) {
				if(strtolower($object->attribute) == 'name') {
					$filename = $object->value;
				}
			}
		}
		
		return $filename;
		
	}


	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	



	function get_string_between($string, $start, $end){
	    $string = ' ' . $string;
	    $ini = strpos($string, $start);
	    if ($ini == 0) return '';
	    $ini += strlen($start);
	    $len = strpos($string, $end, $ini) - $ini;
	    return substr($string, $ini, $len);
	}



// /* try to connect */
// $inbox = imap_open($hostname,$username,$password) or die('Cannot connect to Gmail: ' . imap_last_error());


 // $im = new IMAP_access();
 // if(!$im->open($hostname,$username,$password)){
 //   echo 'Failed...';
 // }
 // $overview = $im->get_overview();
 
 // //var_dump($overview);

 //  foreach($overview as $msg){
 

 //     //var_dump($msg).'</br></br>';

 // //     //...loop through doing stuff
 //        $msg_body  =   $im->get_body($msg);
 //        echo ' $msg_body : '.$msg_body.'</br></br>'; 

 //          //e.g. do something with $im->body_plain
 // }
 
 //  $im->close();
   
  

 ?>