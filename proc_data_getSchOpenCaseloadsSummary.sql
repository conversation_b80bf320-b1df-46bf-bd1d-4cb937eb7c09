

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchOpenCaseloadsSummary$$

CREATE PROCEDURE proc_getSchOpenCaseloadsSummary (IN   p_service_type_id int,
																		     p_from_date date,
																		     p_to_date date
												                  )  

BEGIN

 

	
 
	
	create temporary table tmp engine=memory

	SELECT 
					    CONCAT(TRIM(SchoolName),
					            ' (',
					            c.ExtId,
					            ') ',
					            c.StreetAddress1,
					            ' ',
					            c.City,
					            ' ',
					            c.ZipCode) AS SchoolName,
					            a.SchoolId,

					    a.ServiceTypeId,       
					    f.DOEServiceTypeGrpId as CandidateTypeDesc, 
					    DATE_FORMAT(a.TransDate, '%m-%d-%Y') AS TransmitDate,
					    DATE_FORMAT(a.TransDate, '%Y-%m-%d') AS TransmitDateUnf,
					    c.City as SchoolCity,
					    c.ZipCode as SchoolZipCode,

					    COUNT(a.Id) AS NumberOfStudents,
					    SUM(a.SessionFrequency) AS NumberOfSessions,
					    DATE_FORMAT(d.TransDate, '%m-%d-%Y')  as LastEmailDate,
					    0 as LastEmailCount,
					    d.Id as LastEmailId,
					    d.Comments,
					    concat(a.SchoolId,a.ServiceTypeId,DATE_FORMAT(a.TransDate, '%Y%m%d')) as OpenCaseloadSearchId
   
					FROM
					    SchStudentMandates a
					        LEFT JOIN
					    SchSchools c ON a.SchoolId = c.Id
					        LEFT JOIN
					    SchStudents b ON a.StudentId = b.Id
					        LEFT JOIN
					    SchServiceTypes f ON a.ServiceTypeId = f.Id
					        LEFT JOIN
					    Users e ON a.UserId = e.UserId
					        LEFT JOIN
					    SchOpenCaseloadsEmailHeader d ON a.SchoolId = d.SchoolId
									                          AND  DATE_FORMAT(a.TransDate, '%Y-%m-%d') = d.TransmitDate
									                          AND  a.ServiceTypeId = d.ServiceTypeId


					WHERE
					            a.StatusId = 1 
					        AND a.RegistrantId = 0
					        AND a.SchoolId != 0
					        AND a.ServiceTypeId = p_service_type_id
					        AND cast(a.TransDate as Date) between p_from_date and p_to_date

					GROUP BY TransmitDate , SchoolName
					ORDER BY a.TransDate DESC , SchoolName
 			;
			
	

 
/* Set Last Text Message Count   */
/* =======================================*/
  -- UPDATE tmp a
  --  SET LastEmailCount = (SELECT count(*)  from SchOpenCaseloadsEmailDetails d
  --  							WHERE  d.EmailHdrId = a.LastEmailId
                              
  --  						 ); 

 


/* =======================================*/

	select SchoolName,
         SchoolId,
				 ServiceTypeId,       
				 CandidateTypeDesc, 
				 TransmitDate,
				 TransmitDateUnf,
				 SchoolCity,
				 SchoolZipCode,
				 NumberOfStudents,
				 NumberOfSessions,
				 LastEmailDate,
				 LastEmailCount,
				 coalesce(LastEmailId, '') as LastEmailId,
				 coalesce(Comments, '') as Comments,
				 OpenCaseloadSearchId


	From tmp ;   

	drop temporary table if exists tmp;	
	
END $$

DELIMITER ;	