<?php 



     require_once("db_GetSetData.php");

	$conn = getCon();

 
      $query = "SELECT  a.Id as id,  
						CONCAT( trim( a.LastName) , ', ', trim(a.FirstName) ,' (', a.ExtId,')' ) as StudentName,
 						a.SubSchoolTypeId,				
						a.SearchId
				FROM SchStudents a 
				WHERE exists (select 1 from SchStudentMandates b
				               where a.Id = b.StudentId  
				               and  b.<PERSON>ran<PERSON>Perc = 0


				)
				order by LastName, FirstName ";	 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;


 
?>
