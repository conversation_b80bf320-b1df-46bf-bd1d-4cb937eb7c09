<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
  $RegistrantTypeId = $_GET['RegistrantTypeId'];  
	$PayrollWeek = $_GET['PayrollWeek'];
 

    if ($RegistrantTypeId == '23') {

     $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                          a.ServiceDate AS ServiceDateSort,
                          a.WeekDay,
                          DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      CONCAT(b.FirstName, ' ', b.LastName) as StudentName,
                      a.Id as ScheduleId,
                      
                      COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                        WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                      
                      '1'  as SessionGrpSize,

                      a.RegistrantId,
                      CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                     /*SchoolName,*/  
                     CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolName,
                      ServiceTypeDesc                        

                  FROM  WeeklyServices a, 
                        SchStudents b, 
                        Registrants e,
                        ScheduleStatuses g, 
                        SchSchools d,
                        SchServiceTypes h,
                        SchDistricts f 

                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.RegistrantId = e.Id 
                        AND   a.PayrollWeek = '{$PayrollWeek}'  
                       AND   a.ScheduleStatusId = 7   
                         AND   b.Id = a.StudentId
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   d.DistrictId = f.Id 
                        AND   a.ServiceTypeId = h.Id
                  ORDER BY    a.ServiceDate, a.StartTime, a.EndTime, a.TotalHours" ;           

    } else {

    $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                          a.ServiceDate AS ServiceDateSort,
                          a.WeekDay,
                          DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      /*FORMAT((a.TotalHours * 60), 0) as TotalHours,*/
                      TotalHours,
                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as ScheduleId,
                      
                      COALESCE((SELECT SessionFrequency FROM SchStudentMandates i 
                        WHERE a.MandateId = i.Id),'5') as SessionFrequency,

                      CASE  SessionGrpSize 
                        WHEN '0' THEN '1'
                      ELSE SessionGrpSize
                      END AS SessionGrpSize,

                      a.RegistrantId,
                      CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as RegistrantName,  
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                     /* SchoolName,*/
                     CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolName,

                      ServiceTypeDesc                        

                  FROM  WeeklyServices a, 
                        SchStudents b, 
                        Registrants e,
                        ScheduleStatuses g, 
                        SchSchools d,
                        SchServiceTypes h,
                        SchDistricts f  

                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.RegistrantId = e.Id 
                        AND   a.PayrollWeek = '{$PayrollWeek}'  
                       AND   a.ScheduleStatusId = 7   
                         AND   b.Id = a.StudentId
                         AND   d.DistrictId = f.Id 
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   a.ServiceTypeId = h.Id
                  GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours  LIMIT 10";


    }
  

	$ret = getData ($conn, $query);
	setDisConn($conn);

  //echo $ret;
  echo $query;

?>

