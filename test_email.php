<?php 

    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\Exception;

    require 'PHPMailer/src/Exception.php';
    require 'PHPMailer/src/PHPMailer.php';
    require 'PHPMailer/src/SMTP.php';

	$mail  = new PHPMailer();  


	$mail->AddReplyTo("<EMAIL>","RCM");

    $mail->SetFrom('<EMAIL>', 'RCM');

    $mail->AddReplyTo("<EMAIL>","RCM");

    $address = '<EMAIL>';
    $mail->AddAddress($address, $provider_name);
    
    $mail->addBCC('<EMAIL>');

	$mail->Subject    = "Subject:  Test PHPMailer";

	$body = 'test email'; 
	$mail->MsgHTML($body);

	
    if(!$mail->Send()) {
      echo "Mailer Error: " . $mail->ErrorInfo;
    } else {
      echo "Emailed!";
    }

?>
