<?php 

 
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
 


	require_once("db_GetSetData.php"); 	

	

  // 100 %
  // ---------------

  $conn = getCon();

    // $SchoolId = $_GET['SchoolId'];  
    // $SubShoolId = $_GET['SubShoolId'];  
    // $WeekDayId = $_GET['WeekDayId'];  
    // $AllDayFL = $_GET['AllDayFL'];  
    // $Perc = $_GET['Perc'];  


    $SchoolId = '1895';  
    $SubShoolId = '0';  
    $WeekDayId = '1';  
    $AllDayFL = '1';  
    $Perc = '100';  


    $query = "call proc_setSchParaAdjustedHours('{$SchoolId}', '{$SubShoolId}', '{$WeekDayId}', '{$Perc}') ";
    

     $result =  mysqli_query($conn, $query) or die
      ("Error in Selecting " . mysqli_error($conn));


      while ($row = $result->fetch_assoc()) {

            $SchoolStartTimeAM = $row['SchoolStartTimeAM'];
            $SchoolEndTimeAM = $row['SchoolEndTimeAM'];
             $SchoolHoursAM = $row['SchoolHoursAM'];          

            $SchoolStartTimePM = $row['SchoolStartTimePM'];
            $SchoolEndTimePM = $row['SchoolEndTimePM'];
            $SchoolHoursPM = $row['SchoolHoursPM'];          
   
        }

      mysqli_free_result($result);
      mysqli_close($conn);
  

      // echo ' TotalHours: '.$GLOBALS['TotalHours'].'</b>';
      // echo ' SchoolStartTimeAM: '.$GLOBALS['SchoolStartTimeAM'].'</b>';
      // echo ' SchoolEndTimeAM: '.$GLOBALS['SchoolEndTimeAM'].'</b>';
      // echo ' SchoolHoursAM: '.$GLOBALS['SchoolHoursAM'].'</b>';

      
 
      if ($AllDayFL == 0) { // Not all days = Start


      switch ($WeekDayId) {

                 case '1':
                   $WeekDayName = 'Mon';
                   break;

                 case '2':
                   $WeekDayName = 'Tue';
                   break;

                 case '3':
                   $WeekDayName = 'Wed';
                   break;

                 case '4':
                   $WeekDayName = 'Thu';
                   break;

                 case '5':
                   $WeekDayName = 'Fri';
                   break;


                 
      }         

      
      // Set 100% Sessions Schedule
      //============================


      $conn = getCon();


      $query  = "UPDATE  SchStudentMandates a,
                            SchStudents b,
                            SchServiceTypes c
       set  ParaStartTime".$WeekDayName."1 =  '{$SchoolStartTimeAM}',
            ParaEndTime".$WeekDayName."1 =    '{$SchoolEndTimeAM}',
            ParaTotalHours".$WeekDayName."1 = '{$SchoolHoursAM}', 
            ParaStartTime".$WeekDayName."2 =  '{$SchoolStartTimePM}',
            ParaEndTime".$WeekDayName."2 =    '{$SchoolEndTimePM}',
            ParaTotalHours".$WeekDayName."2 = '{$SchoolHoursPM}' 


            where a.StatusId = '1'
            and  a.SchoolId = '{$SchoolId}'
            and  a.StudentId = b.Id
            and  a.ServiceTypeId = c.Id
            and  c.SESISParaServiceTypeId = '1'
            and  a.ParaTransportPerc = '{$Perc}'
            and  curdate() <= a.EndDate 
            and  a.StudentId = 26969";



      $ret =  setData ($conn, $query);  
      setDisConn($conn);


 
 

        echo ' query: '.$query.'</b>';
    




      } // Not all days = End

      else { // All days = Start
 

      $conn = getCon();


      $query  = "UPDATE  SchStudentMandates a,
                            SchStudents b,
                            SchServiceTypes c
       set  ParaStartTimeMon1 =  '{$SchoolStartTimeAM}',
            ParaEndTimeMon1 =    '{$SchoolEndTimeAM}',
            ParaTotalHoursMon1 = '{$SchoolHoursAM}', 
            ParaStartTimeMon2 =  '{$SchoolStartTimePM}',
            ParaEndTimeMon2 =    '{$SchoolEndTimePM}',
            ParaTotalHoursMon2 = '{$SchoolHoursPM}',
            ParaStartTimeTue1 =  '{$SchoolStartTimeAM}',
            ParaEndTimeTue1 =    '{$SchoolEndTimeAM}',
            ParaTotalHoursTue1 = '{$SchoolHoursAM}', 
            ParaStartTimeTue2 =  '{$SchoolStartTimePM}',
            ParaEndTimeTue2 =    '{$SchoolEndTimePM}',
            ParaTotalHoursTue2 = '{$SchoolHoursPM}', 
            ParaStartTimeWed1 =  '{$SchoolStartTimeAM}',
            ParaEndTimeWed1 =    '{$SchoolEndTimeAM}',
            ParaTotalHoursWed1 = '{$SchoolHoursAM}', 
            ParaStartTimeWed2 =  '{$SchoolStartTimePM}',
            ParaEndTimeWed2 =    '{$SchoolEndTimePM}',
            ParaTotalHoursWed2 = '{$SchoolHoursPM}', 
            ParaStartTimeThu1 =  '{$SchoolStartTimeAM}',
            ParaEndTimeThu1 =    '{$SchoolEndTimeAM}',
            ParaTotalHoursThu1 = '{$SchoolHoursAM}', 
            ParaStartTimeThu2 =  '{$SchoolStartTimePM}',
            ParaEndTimeThu2 =    '{$SchoolEndTimePM}',
            ParaTotalHoursThu2 = '{$SchoolHoursPM}', 
            ParaStartTimeFri1 =  '{$SchoolStartTimeAM}',
            ParaEndTimeFri1 =    '{$SchoolEndTimeAM}',
            ParaTotalHoursFri1 = '{$SchoolHoursAM}', 
            ParaStartTimeFri2 =  '{$SchoolStartTimePM}',
            ParaEndTimeFri2 =    '{$SchoolEndTimePM}',
            ParaTotalHoursFri2 = '{$SchoolHoursPM}'  



            where a.StatusId = '1'
            and  a.SchoolId = '{$SchoolId}'
            and  a.StudentId = b.Id
            and  a.ServiceTypeId = c.Id
            and  c.SESISParaServiceTypeId = '1'
            and  a.ParaTransportPerc = '{$Perc}'
            and  curdate() <= a.EndDate
            and  a.StudentId = 26969
             ";



      $ret =  setData ($conn, $query);  
      setDisConn($conn);



      } // All days = End 
      

      echo $query;
 
?>

