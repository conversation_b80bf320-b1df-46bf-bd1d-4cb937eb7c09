<?php

 
	require('fpdf/fpdf.php'); 

	require_once("db_GetSetData.php");


	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];
	$RegistrantName = $_GET['RegistrantName'];
	$PayrollWeek = $_GET['PayrollWeek'];
	$PayrollWeekDisp = $_GET['PayrollWeekDisp'];

	$SignatureDate = $_GET['SignatureDate'];

	$GLOBALS['RegistrantName'] = $_GET['RegistrantName'];
	$GLOBALS['PayrollWeekDisp'] = $_GET['PayrollWeekDisp'];


/*	 

	$GLOBALS['report_date'] = $_GET['ReportDate'];

	$system_date = new DateTime($GLOBALS['report_date']);

	$GLOBALS['report_date_frm'] = $system_date->format('m/d/Y');
*/
	 



 
	 
class PDF extends FPDF
{

	function PDF($orientation='L',$unit='mm',$format='A4')
	{
		//Call parent constructor
		$this->FPDF($orientation,$unit,$format);
	}


		//Page header
		function Header()
		{
			
		//$this->Ln(28);

		//$this->Cell(5);
		$this->SetFont('Arial','B',16);

 		$this->Cell(0,5,'W E E K L Y  T I M E C A R D',0,1,'C');
 		$this->Cell(0,5,'',0,1,'L');
		$this->SetFont('Arial','B',14);

		
		$this->Cell(30,5,'Provider: ',0,0,'L');
		$this->SetFont('Arial','',14);
		$this->Cell(90,5,$GLOBALS['RegistrantName'],1,0,'L');
		$this->SetFont('Arial','B',14);
		$this->Cell(30,5,'WE Date: ',0,0,'L');
		$this->SetFont('Arial','',14);
		$this->Cell(35,5,$GLOBALS['PayrollWeekDisp'],1,0,'L');
 		$this->Cell(0,5,'',0,1,'L');
 		$this->Cell(0,5,'',0,1,'L');

		$this->SetFont('Arial','B',14);
		$this->Cell(70,5,'TimeCard was signed on: ',0,0,'L');
		$this->SetFont('Arial','',14);
		$this->Cell(35,5,$GLOBALS['SignatureDate'],1,1,'L');
 		$this->Cell(0,5,'',0,1,'L');
 		$this->Cell(0,5,'',0,1,'L');
 		$this->Cell(0,5,'',0,1,'L');

		$this->SetFont('Arial','B',10);
		$this->Cell(30,5,'Session Date',1,0,'C');
		$this->Cell(25,5,'Start Time',1,0,'C');
		$this->Cell(25,5,'End Time',1,0,'C');
		$this->Cell(20,5,'Grp Size',1,0,'C');
		$this->Cell(20,5,'Dur.',1,0,'C');
		$this->Cell(130,5,'Student(s)',1,1,'L');
		
	
		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-25);
		//Arial italic 8
		$this->SetFont('Times','I',9);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
}
 
	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	$pdf->SetFont('Arial','',9);
	
	//+++++++++++++++++++++++++++++++++++++++++++++++++
    

 	$query = "SELECT  DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDateFrm,
						DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
				 		DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
						FORMAT((a.TotalHours * 60), 0) as TotalHours,
						group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
						group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
						
						SessionGrpSize,
						a.RegistrantId,
						DATE_FORMAT( c.TransDate, '%m-%d-%Y' ) AS DateSigned
				FROM 	WeeklyServices a, SchStudents b, SchRegistrantSignedTimeCards c
							WHERE a.RegistrantId = '{$RegistrantId}' 
							AND   a.PayrollWeek =  '{$PayrollWeek}'  
							AND   a.RegistrantId = c.RegistrantId
							AND   a.PayrollWeek =  c.PayrollWeek  							
							AND   a.ScheduleStatusId > 5
							AND   b.Id = a.StudentId
				GROUP BY a.ServiceDate	, a.StartTime, a.EndTime, a.TotalHours	";
	
	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));
	
	//$pdf->Ln(10);

	$cnt = 0;

	while ($row = $result->fetch_assoc()) {

		$pdf->Cell(30,5,$row['ServiceDateFrm'],1,0,'C');
		$pdf->Cell(25,5,$row['StartTime'],1,0,'C');
		$pdf->Cell(25,5,$row['EndTime'],1,0,'C');
		$pdf->Cell(20,5,$row['SessionGrpSize'],1,0,'C');
		$pdf->Cell(20,5,$row['TotalHours'],1,0,'C');
		$pdf->Cell(130,5,$row['StudentName'],1,1,'L');
		
		$cnt++;
		if ($cnt == 20) {

			$pdf->AddPage();
			$cnt = 0;

		}

	
	}	
	   



	//+++++++++++++++++++++++++++++++++++++++++++++++++
	 
	
	$pdf->Output();
	 
	$connection->disconnect();

	 	
?>
