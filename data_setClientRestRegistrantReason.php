<?php 


require "ewDataHandler.php"; 
  
$rcr_transaction = new dataHandler(); 

$RegistrantId =  $_POST['RegistrantId'];
$ClientId =  $_POST['ClientId'];
$RestrictType =  $_POST['RestrictType'];
$RestrictReason = $this->connection->escapeSimple($_POST['RestrictReason']); 
$UserId = $_POST['UserId']; 

$result = $rcr_transaction->setClientRestRegistrantReason($RegistrantId,
														$ClientId,	  
														$RestrictType,
														$RestrictReason,
														$UserId
														); 

$rcr_transaction->disconnectDB ();  

//echo  { success: true };
echo $result; 

//echo 'return';
?>
