	

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchRNDailyReportData$$

	CREATE PROCEDURE proc_getSchRNDailyReportData (IN p_service_date DATE,
													  p_district_id INT
												  )  
														 	 

	BEGIN

	DECLARE v_LongTermNurseId INT ; 

	 

	 		create temporary table tmp engine=memory
			
		select   
				a.ServiceTypeId,
	            a.AssignmentId,
	            case  
	             when a.AssignmentId !=  0 Then 'Long'
	             else 'Short' 
	            end as AssignmentType, 
	            CAST('' AS CHAR(10)) AS LongTermRnId, 
	            CAST('' AS CHAR(512)) AS LongTermRnName, 

		        a.Id as ScheduleId,
		        CONCAT(DATE_FORMAT( a.StartTime, '%l:%i %p' ),'-',(DATE_FORMAT( a.EndTime, '%l:%i %p' )),' (',a.TotalHours,' Hrs)') as ScheduleDesc,   
		        -- DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,   
		        a.ServiceDate, 
				a.RegistrantId as ScheduledRNId,  
				CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as ScheduledRNName,
				COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)),'All') as StudentName,
				COALESCE(c.ExtId,'') as StudentOsisNumber,
				a.SchoolId,
				TRIM(i.SchoolName) as SchoolName,
				i.Extid as SchoolDBN,
				i.DistrictId,
				TRIM (DistrictName) as DistrictName,
				CASE 
                  when CallInStatus is null then 'Unconfirmed'
                  else CallInStatus
            	end as CallInStatus,

				d.Id as CallInTimeId,
				COALESCE(DATE_FORMAT( d.CallInTime, '%l:%i %p' ),'') as CallInTime,
				COALESCE(CAST(d.CallInComments as char(512)),'') as CallInComments,
				h.ServiceTypeDesc,
				a.ConfirmationNumber

	 	from WeeklyServices a
	 	       JOIN
	 	     Registrants b on a.RegistrantId = b.Id
	 	       JOIN
			 RegistrantTypes f ON b.TypeId = f.Id
	 	       LEFT JOIN
	 	     SchStudents c on a.StudentId = c.Id 
	 	       JOIN
	         SchServiceTypes h ON a.ServiceTypeId = h.Id
			   JOIN 	           
			SchSchools i ON a.SchoolId = i.Id
			   JOIN
			SchDistricts j ON i.DistrictId = j.Id 
			   LEFT JOIN
			SchRNDailyCallInTimes d on a.RegistrantId = d.RegistrantId
			                       and a.ServiceDate = d.ServiceDate   

		WHERE   a.ServiceDate = p_service_date
		AND b.TypeId = 12
		AND i.DistrictId = p_district_id
		AND a.ServiceTypeId between 39 and 42
		;
	
	 

	/* Get long Term Nurse Id - School*/
	/*=================================*/
	update tmp a
	   set a.LongTermRnId = (
	   
	   		Select b.RegistrantId
	   		  FROM SchSchoolAssignmentDetails b
	   		  WHERE a.AssignmentId = b.AssignmentId
	   		  LIMIT 1	
	   		   
	   )
	WHERE a.AssignmentId != 0
	AND a.ServiceTypeId between 39 and 42;  	 

 

	/* Get long Term Nurse Id - Student*/
	/*=================================*/

	update tmp a
	   set a.LongTermRnId = (
	   
	   		Select b.RegistrantId
	   		  FROM SchStudentAssignmentDetails b
	   		  WHERE a.AssignmentId = b.AssignmentId
	   		  LIMIT 1	
	   		   
	   )
	WHERE a.AssignmentId != 0
	AND a.ServiceTypeId > 42;  	 
 

	/* Get long Term Nurse Name*/
	/*=================================*/

	update tmp a,
		   Registrants b
	 set a.LongTermRnName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) 	   
	   
	WHERE a.AssignmentId != 0
	AND   a.LongTermRnId = b.Id
	 ;  	 

	/*========================*/


	select  

			DistrictName as 'District',
			SchoolName as 'School' ,
			SchoolDBN as 'School ID',
			CallInTime  as 'Time of Arrival/ Call in time',
			CallInStatus as "Today's Nurse",
			LongTermRnName as 'Long Term Nurse',
			StudentName as 'Student',
			StudentOsisNumber as 'Student ID',
			group_concat( ScheduleDesc SEPARATOR ', ' ) as 'Trip Location and Time',
			ConfirmationNumber as 'Confirmation Number',
			group_concat( ServiceTypeDesc SEPARATOR ', ' ) as 'Placement',
			AssignmentType as 'Term',
			CallInComments as 'Comments'

 
	from tmp 
       GROUP BY ScheduledRNId 
	;	


	drop temporary table if exists tmp;



	END $$

	DELIMITER ;		