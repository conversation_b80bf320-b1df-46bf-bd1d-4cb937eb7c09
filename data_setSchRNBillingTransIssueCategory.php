<?php 


  // error_reporting(E_ALL);
  // ini_set('display_errors', TRUE);
  // ini_set('display_startup_errors', TRUE);


	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$BillingIssueCategoryId = $_POST['BillingIssueCategoryId'];  	
	$UserId = $_POST['UserId'];  

	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);

  
  $IssueAmount = 0;
  $WriteOffAmount = 0;

  	foreach ($Data as $TransData) {

	    $BillingCommentsId = $TransData['BillingCommentsId'];
	    $ScheduleIdList = $TransData['ScheduleIdList'];
	    // $IssueAmount = $TransData['IssueAmount'];
	 




	    switch ($BillingIssueCategoryId) {
	     	case '6':
	     		$IssueAmount = $TransData['IssueAmount'];
	     		$WriteOffAmount = 0;
	     		break;
	     	
	     	case '7':
	     		$IssueAmount = 0;
	     		$WriteOffAmount = $TransData['IssueAmount'];; 
	     		break;

	     	case '1':
	     		$IssueAmount = 0;
	     		$WriteOffAmount = 0; 
	     		break;

	     	default:
	     		$IssueAmount = $TransData['IssueAmount'];
	     		$WriteOffAmount = 0; 
	     		break;
	     } 


	     if(isset($BillingCommentsId)) {  


	      $query ="UPDATE SchRNBilling
	                 SET
	                  BillingIssueCategoryId = '{$BillingIssueCategoryId}',
	                  BillingComments = '',
	                  IssueAmount = '{$IssueAmount}',
	                  WriteOffAmount = '{$WriteOffAmount}', 
	                  AdditionalComments = '',
	                  UnBilledHours = '0',
	                  UserId = '{$UserId}' 
	              WHERE BillingCommentsId = '{$BillingCommentsId}';



	          ";     

	   
	                        
	      } else {

	       $query ="INSERT INTO  SchRNBilling
	            ( 
	              BillingCommentsId,
	              BillingIssueCategoryId,
	              IssueAmount,
	              WriteOffAmount,
	              UserId 
	            )
	            VALUES
	            
	            ( '{$ScheduleIdList}',
	              '{$BillingIssueCategoryId}',
	              '{$IssueAmount}',
	              '{$WriteOffAmount}',
	              '{$UserId}'
	            );
	   

	        ; 


	          ";      

	      }

 

	    $ret = setData ($conn, $query);
 



	}	
	


	setDisConn($conn);
	echo $ret;

 

?>

