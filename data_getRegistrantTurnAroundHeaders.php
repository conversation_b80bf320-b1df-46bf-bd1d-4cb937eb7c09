<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];
	$Month = $_GET['Month'];
	$Year = $_GET['Year'];


    $query = "	SELECT    	a.SchoolId, 
				b.<PERSON>ame, 
	            MID(DistrictName,10,2) as DistrictName,
	            BoroughName,
	            count(distinct a.StudentId) as NumberOfStudents
			from WeeklyServices a, 
				 SchSchools b, 
				 SchStudentMandates c,
				 SchDistricts f, 
				 SchBoroughs g
			where a.RegistrantId = '{$RegistrantId}'
			and   a.ScheduleStatusId > 6
			and   month(a.ServiceDate) = '{$Month}'
			and   year(a.ServiceDate) = '{$Year}'
			and   a.SchoolId = b.Id
			and   b.DistrictId = f.Id
			and   f.BoroughId = g.Id
			and   a.MandateId = c.Id
            and   c.BillingContractId = '1'
            AND   a.SessionDeliveryModeId != 'V'
            AND   a.SessionDeliveryModeId != 'A'


			Group By a.SchoolId, b.SchoolName, DistrictName, BoroughName
 	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	//echo $query;


?>
