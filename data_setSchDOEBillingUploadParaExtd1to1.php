<?php
       
/*    
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
*/    


    require_once('DB.php');
    include('db_login.php');
    

    $user_id = $_POST['UserId'];
    if (!$user_id) {
        $user_id = '1';
    }   

    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
        $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }


    //Portal Input Input File  
    //==========================================================
    $file_date = date('Y-m-d H:i:s').'-'.mt_rand();

    $inputFileName = '../uploads/doe_billing_upload_input_ext -'.$file_date.'.csv';
 
    
   if($ufile != none){ 
      
        $rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
        
    } else {
        
        $err_flag = '1';
        //print "Error uploading extracted file. Please try again!!! "; 
      
        $linecount = 0;
        echo  "{ success: true, transactions: '{$linecount}'}";

        Return ; 

    }
     
   

    $input_file_handle = fopen($inputFileName, "r");
  
    $outputFileName = '../uploads/doe_billing_upload_output_ext -'.$file_date.'.csv';
    $output_file_handle = fopen($outputFileName, "w");
     
    $out_File = '../uploads/doe_billing_upload_ext'.$file_date.'.txt';
    $fh = fopen($out_File, 'w') or die("can't open file");

  




   while (($row_xls = fgetcsv($input_file_handle)) !== FALSE) { // for foreach - start



                /* Check if Wrog file was selected 
             ====================================*/


                $cnt++;
         
                 //=========================
                // Write Header
                //========================  
                
                if ($cnt == 1) {


                //$check_file = preg_replace('/[\x00-\x1F\x7F]/', '', $row_xls[0]);
                $row_xls[0] = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $row_xls[0]);
                //echo '$row_xls[0]: '.$row_xls[0].'</br>';     

                /* Check if Wrog file was selected 
                ====================================*/

                        if (($row_xls[0] != 'SRAP FISCAL YR') && ($row_xls[0] != 'SIAP FISCAL YR') && ($row_xls[0] != 'RSAP FISCAL YR')) {

                            $err_flag = '1';


                        } else {

                            fputcsv($output_file_handle, $row_xls);
                            $out_Line_str = implode("\t", $row_xls); 
                            $out_Line_str = $out_Line_str."\n";
                            fwrite($fh, $out_Line_str);


                            /* Write Heading 
                             ==================*/
                            $write_flag = '1';


                        }




                }


                   if ($cnt != 1) {//   cnt !=1 - srart  
   

                    $auth_start_time = '';
                    $auth_end_time = '';
                    $auth_hours = '';
                    
                    $addon_start_time = ''; 
                    $addon_end_time = ''; 
                    $addon_hours = '';
                    $addon_schedule_id = '';
                    $addon_start_time_unf = ''; 
                    $addon_end_time_unf = ''; 
 
                    //==============================
                    // Get Therapist Id
                    //==============================

                    $therapist_id = $row_xls[10];
                    $therapist_id = str_pad($therapist_id,9, '0', STR_PAD_LEFT); 

                    //==============================
                    // Get Student Id
                    //==============================
                    
                    $student_id = $row_xls[11];

                    //==============================
                    // Get Student Last Name 
                    //==============================

                    $student_last_name = $row_xls[13];


                    //==============================
                    // Get Service Code
                    //==============================

                    $doe_service_code = $row_xls[14];

                    //==============================
                    // Allowed Minutes
                    //==============================

                    $allowed_minutes = $row_xls[19];

                    //==============================
                    // Get Service Date
                    //==============================
                    
                    //$doe_service_date = $row_xls[22];
                    $doe_service_date = date("Y-m-d", strtotime($row_xls[24]));

 

                   $query = "SELECT a.Id,
                                    DATE_FORMAT(StartTime, '%h:%i %p') AS StartTime,
                                    StartTime AS StartTimeUnf,
                                    DATE_FORMAT(EndTime, '%h:%i %p') AS EndTime,
                                    EndTime AS EndTimeUnf,
                                    TotalHours,
                                    BillRate,
                                    Allowedminutes as PrevAllowedminutes 
                                FROM
                                    SchStudentParaUnBilledTransactions a, SchServiceTypes b
                                where  StatusId = '0'
                                  and DOEServiceDate = '{$doe_service_date}'  
                                  and DOEServiceTypeIndId like '%{$doe_service_code}%'
                                  and DOEServiceCode = '{$doe_service_code}'  
                                  and StudentExtId = '{$student_id}'
                                  and RegistrantExtId = '{$therapist_id}' 
                                  and SESISParaServiceTypeId = '1'   

                     ";



                   $result = $connection->query ($query);
                        

                   $new_allowed_minutes = ''; 
                            
                    while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {




                        $Id = $row['Id'];
                        $start_time = $row['StartTime'];
                        $end_time = $row['EndTime'];
                        
                        $start_time_unf = $row['StartTimeUnf'];
                        $end_time_unf = $row['EndTimeUnf'];
                        $total_hours = $row['TotalHours'];
                        $bill_rate = $row['BillRate'];
                        $prev_allowed_minutes = $row['PrevAllowedminutes'];


                        if (!$new_allowed_minutes) {


                          $new_allowed_minutes = $allowed_minutes - $prev_allowed_minutes;

                        }

                        $total_minutes = $total_hours * 60;


                        $addon_minutes  = $new_allowed_minutes - $total_minutes;  

                        //echo '$new_allowed_minutes: '.$new_allowed_minutes.' $total_minutes: '.$total_minutes.' $addon_minutes'.$addon_minutes.'</br>';  

                         if ($addon_minutes >= 0) { // No Ext Hours

                              $auth_end_time = $end_time;
                              $auth_end_time_frm = date('g:i A', strtotime($end_time)); 
                              $auth_hours = $total_hours;


                         } else {


                              $start_date_time = date("2000-01-01 ".$start_time_unf); 

                              //echo 'Start Time: '.$start_date_time;

                              /*======= Authorized End Time Calculation =======*/
                              $auth_end_date_time = date('H:i:s', strtotime($start_date_time . ' +'.abs($new_allowed_minutes).' minutes'));
                              $auth_end_time = date('H:i:s', strtotime($start_date_time . ' +'.abs($new_allowed_minutes).' minutes'));

                              $auth_end_time_frm = date('g:i A', strtotime($start_date_time . ' +'.abs($new_allowed_minutes).' minutes'));
 

                              /*======= Addon Start Time (Unformated) =======*/

                              $addon_start_time_unf = $auth_end_date_time;


                              /*======= Addon End Time Calculation =======*/
                              $addon_end_time_unf = date('H:i:s', strtotime($auth_end_date_time . ' +'.abs($addon_minutes).' minutes'));
                              $addon_end_time = date('H:i:s', strtotime($auth_end_date_time . ' +'.abs($addon_minutes).' minutes'));
                               
                                


                              $auth_hours = abs($new_allowed_minutes) / 60;
                              $auth_hours = number_format($auth_hours,2);

                              $addon_hours = abs($addon_minutes) / 60;
                              $addon_hours = number_format($addon_hours,2);



                          } 

 
                            if ($auth_hours == 0.67) {

                                $multiplier = 0.66666666667;
                            
                            } else {

                                $multiplier = $auth_hours;


                            }

                            $bill_amount = $bill_rate * $multiplier;
                            $bill_amount = number_format($bill_amount,2,'.',',');

                            $row_xls[25] = 'P';
                            $row_xls[26] =  '1';
                            $row_xls[27] =  $start_time;
                            $row_xls[28] =  $auth_end_time_frm;
                            $row_xls[29] =  'S'; 
                            $row_xls[32] =  $bill_amount;
                                
                            $linecount++;
                            $i++;
                            $write_flag = '1';
                            
                            fputcsv($output_file_handle, $row_xls);
                            $out_Line_str = implode("\t", $row_xls); 
                            $out_Line_str = $out_Line_str."\n";
                            fwrite($fh, $out_Line_str);
                            //echo   $out_Line_str.'</br>'; 
                            

                            // Update Para Extd Billing File
                            //==========================  
                            if ($addon_hours > 0) {


                                $new_start_time =  $auth_end_time;


                            if ($addon_hours == 0.67) {

                                $multiplier = 0.66666666667;
                            
                            } else {

                                $multiplier = $addon_hours;


                            }

                            $bill_amount = $bill_rate * $multiplier;
                            $bill_amount = number_format($bill_amount,2,'.',',');


 
                               $query1 = " UPDATE SchStudentParaUnBilledTransactions
                                                 
                                              set StartTime = '{$auth_end_time}',
                                                  TotalHours = '{$addon_hours}',
                                                  BillAmount = '{$bill_amount}'

                                           WHERE  Id = '{$Id}'
                                  ";



                            }  else {


                               $query1 = " UPDATE SchStudentParaUnBilledTransactions
                                                 
                                              set StatusId = '1' 

                                           WHERE  Id = '{$Id}'
                                  ";


                            } 

  
                          $result1 = $connection->query ($query1);

                          $new_allowed_minutes = $new_allowed_minutes - $total_minutes;
 
 


                     }  
  




            } // Cnt !=1  - end

            /* Wrong file selected error
             =====================================*/
            if ($err_flag == '1') {

                //echo "Wrong file was selected. Please try again!!!</br>"; 
                

                $linecount = 0;
                echo  "{ success: true, transactions: '{$linecount}'}";
                Return ; 

            }



   } // for foreach - end

    fclose($input_file_handle);
    fclose($output_file_handle);
    fclose($fh); 
    $connection->disconnect();

    $zip = new ZipArchive();
    $filename = "../uploads/eWebToVPortalZipFile.zip";
    unlink($filename);


    $downloaded_filename = "eWebToVPortalZipFile.zip";

    if ($zip->open($filename, ZipArchive::CREATE)!==TRUE) {
        exit("cannot open <$filename>\n");
    }

    $zip->addFile($out_File,'eWebToVPortalUploadFile.txt');
    $zip->addFile($outputFileName, 'eWebToVPortalVerificationFile.csv');
    $zip->close();


//    echo  "{ success: true, transactions: '{$linecount}'}";
    echo  "{ success: true, transactions: '{$linecount}'}";


 
  
?>