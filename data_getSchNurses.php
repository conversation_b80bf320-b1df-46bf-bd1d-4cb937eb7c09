<?php 
	
	 

   require_once("db_GetSetData.php");

  $conn = getCon();

   
 



 $query = "		SELECT DISTINCT
			c.Id AS id,
			c.Id AS RegistrantId,
			CONCAT(TRIM(c.LastName),
					', ',
					TRIM(c.FirstName),
					' (',
					RegistrantTypeDesc,
					')') AS RegistrantName
		FROM
			Registrants c,
			RegistrantTypes f
		WHERE
			c.TypeId = f.Id AND c.TypeId = '12'
		ORDER BY c.LastName , c.FirstName
					

			"; 


  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;
  	
  
?>
