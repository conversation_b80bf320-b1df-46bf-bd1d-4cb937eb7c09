<?PHP

 
	$UserId = $_GET['UserId'];
	if (!$UserId) {
		$UserId = '0';
	}

	$Data = $_GET['Data'];
	$Data=json_decode($Data,true);


	$incl_schedules =  implode(",",$Data);
 

		//AS400 Input File  
	//==========================================================

	$out_File = "../uploads/eweb_timecards_to_400.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");


	require_once('DB.php');
	include('db_login.php');
	
	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 

	

	$query = "SELECT distinct 									

				CASE f.PayBillRateTypeId 
					WHEN '1' THEN 'RSA'
					WHEN '2' THEN 'CONTRACT'
					ELSE ''
				END AS Contract,
             
				a.SessionGrpSize,
                
                g.ExtId as EmplId,	
				a.ServiceTypeId as ServiceTypeId,
				DATE_FORMAT( a.ServiceDate, '%m%d%Y' ) as ServiceDate,
				DATE_FORMAT( a.StartTime, '%h%i%p' ) as StartTime, 
				DATE_FORMAT( a.EndTime, '%h%i%p' ) as EndTime,
				ABS(a.TotalHours) as TotalHours


			FROM 	WeeklyServices a, 
					SchServiceTypes b,
					Users e,
				    Registrants g,
				    SchStudentMandates f
			WHERE 	FIND_IN_SET(a.Id, '$incl_schedules')   
			    AND a.UserId = e.UserId
				AND a.RegistrantId = g.Id	
				AND a.ServiceTypeId = b.Id	
				AND a.PayrollBatchNumber = ''
				AND g.TypeId not in (12,23)  
				AND a.ScheduleStatusId = '8' 
				AND f.Id = a.MandateId
			";


	$result = $connection->query ($query);

	$BatchCount = 0;


	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$count++;

			//====================================
			// Batch Count
			//====================================

			$BatchCount = $BatchCount +  $row['SessionGrpSize'];				
			

			$client = '';

			if ($row['Contract'] == 'CONTRACT'  ) {

				$client = 'DOCO';

			}



			if ($row['Contract'] ==  'RSA') {

				$client = 'DORS';

			}

			//====================================
			// Department Code
			//====================================
		/*	
			$dept_long = '';		
			$dept_long = $row['BillingClientArea']; 	
			$dept = substr($dept_long,0,6);
		*/

			$dept = '';

			//====================================
			// Service Date
			//====================================
			
			$serv_date = $row['ServiceDate'];
		 
			//====================================
			// Shift Code
			//====================================
			
			$start_time = $row['StartTime'];
			
			$date_arr = date_parse_from_format("g:i A", $start_time); 

			   switch ($date_arr[hour] ) {  		  
	  			 
					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "23") and ($date_arr[hour] <= "24")):  
					$shift_code = "1";   
					break;

					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "0") and ($date_arr[hour] < "7")):  
					$shift_code = "1";   
					break;
					
					// Employee Shift - 2 (Day)
					case (($date_arr[hour] >= "7") and ($date_arr[hour] < "15")):  
					$shift_code = "2";   
					break;

		        
					// Employee Shift - 3 (Evening)
					case (($date_arr[hour] >= "15") and ($date_arr[hour] < "23")):  
					$shift_code = "3";   
					break; 
					
					
				} 

			//====================================
			// Start Time 
			//====================================
			
			$StartTime = $row['StartTime']; 
			$StartTime = substr($StartTime,0, 5); 
			//====================================
			// End Time 
			//====================================
			
			$EndTime = $row['EndTime']; 
			$EndTime = substr($EndTime,0, 5);
			//====================================
			// Total Hours 
			//====================================
			
			$hours_worked = $row['TotalHours']; 
				
			//====================================
			// Employee ID 
			//====================================
			
			$emplid = $row['EmplId']; 
			
	



			$patientid = '';	


			// Patient Code (CONTRACT) 
			//=======================
			if ($client == 'DOCO') {




				switch ($row['SessionGrpSize']) {
				    case 1:
				        $patientid = '990001';
				        break;
				    case 2:
				        $patientid = '990002';
				        break;
				    case 3:
				        $patientid = '990003';
				        break;
				    case 4:
				        $patientid = '990004';
				        break;
				    case 5:
				        $patientid = '990005';
				        break;


				}

			}

			// Patient Code (RSA) 
			//=======================
			if ($client == 'DORS') {


				switch ($row['SessionGrpSize']) {
				    case 1:
				        $patientid = '990006';
				        break;
				    case 2:
				        $patientid = '990007';
				        break;
				    case 3:
				        $patientid = '990008';
				        break;
				    case 4:
				        $patientid = '990009';
				        break;
				    case 5:
				        $patientid = '990010';
				        break;


				}

			}


			//==============================================				
			//=====================================

			$client = str_pad($client,6, ' ', STR_PAD_RIGHT);
			$dept = str_pad($dept,6, ' ', STR_PAD_RIGHT);
			$name = str_pad($line_of_text[1],20, ' ', STR_PAD_RIGHT);
			//$serv_date = date("mdY",strtotime($serv_date));
			$serv_date = str_pad($serv_date,10, ' ', STR_PAD_RIGHT);
			$emplid = str_pad($emplid,9, '0', STR_PAD_LEFT);
			$patientid = str_pad($patientid,6, '0', STR_PAD_LEFT);
			$StartTime = str_replace(":", "", $StartTime);
			$StartTime = str_pad($StartTime,4, '0', STR_PAD_LEFT);
			$EndTime = str_replace(":", "", $EndTime);
			$EndTime = str_pad($EndTime,4, '0', STR_PAD_LEFT);
			$shift_code = str_pad($shift_code,2, ' ', STR_PAD_RIGHT);
			$hours_worked = sprintf("%01.2f", $hours_worked);
			$hours_worked = str_replace(".", "", $hours_worked);
			$hours_worked = str_pad($hours_worked,4, '0', STR_PAD_LEFT);

	/* 			
			echo (' Client: '.$client);
			echo (' Grp Size: '.$row['SessionGrpSize']);
			echo (' Id: '.$emplid);
			echo (' Patient Id: '.$patientid);
			echo (' Date Worked: '.$serv_date);
			echo (' Start Time: '.$start_time);
			echo (' Start Hour: '.$date_arr[hour]);
			echo (' Shift Code: '.$shift_code);
			echo (' Hours Worked: '.$hours_worked.'</br>');
	*/ 		 

				$out_Line = $client." ".$dept." ".$emplid." ".$serv_date." ".$shift_code." ".$StartTime." ".$EndTime." ".$hours_worked." ".$patientid." EOL"."\n";
				fwrite($fh, $out_Line);
				//echo $out_Line.'</br>';	

				$linecount++;


	}
	



  

	//================================
	// Get Next "Payroll Batch Number"
	//==================================
	 
	$next_payroll_batch_num = 0; 
	

	$query1 = "SELECT (MAX(Id) + 1) as NextBatchNumber 
				FROM SchPayrollBatchHeader";
	
	$result1 = $connection->query ($query1);
 	
 	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {	

 		$next_payroll_batch_num = $row1['NextBatchNumber']; 
 	}	
	


	//================================
	// Add "PayrollBatchHeader" Record
	//==================================
	 
	 

	$query2 = "INSERT INTO SchPayrollBatchHeader
				(	Id,
					BatchDate,
					BatchCount,
					UserId,
					TransDate)
				VALUES
				(
					'{$next_payroll_batch_num}',
					curdate(),
					'{$BatchCount}',
					'{$UserId}',
					NOW()
				);";
	
	$result2 = $connection->query ($query2);
	 

	//================================
	// Update "Billing Extract Date"
	//==================================
	 
	
	$query3 = "UPDATE WeeklyServices a 
				  SET PayrollBatchNumber ='{$next_payroll_batch_num}'
			WHERE FIND_IN_SET(Id, '$incl_schedules') 
			";
	
	$result3 = $connection->query ($query3);
  	 
 
	$connection->disconnect();

	 
	  
	fclose($file_handle);
	fclose($fh);

 


	/* Download Create File
	 ================================*/
 
    $DownloadedFileName = 'eWebSchoolsPayrollFile-'.Date('m-d-Y').'.txt';
 
	header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);
  
	
 

	
?>