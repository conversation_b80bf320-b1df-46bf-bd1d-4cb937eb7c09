<?php
	/** Error reporting */
/*  	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/
  
	require_once('DB.php');
	include('db_login.php');
 
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


 

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection1)){
		$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }
 

 
	$FromDate = $_GET['FromDate'];
    $ToDate = $_GET['ToDate'];
    $UserId = $_GET['UserId'];


	$objPHPExcel = new PHPExcel();

	 
   $objPHPExcel->setActiveSheetIndex(0);
   $objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Student Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Student OSIS #');
   $objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Provider Name');

   $objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Provider HR ID');	
   $objPHPExcel->getActiveSheet()->SetCellValue('E1', 'Provider SSN');

   $objPHPExcel->getActiveSheet()->SetCellValue('F1', 'School');
   $objPHPExcel->getActiveSheet()->SetCellValue('G1', 'District');
   $objPHPExcel->getActiveSheet()->SetCellValue('H1', 'Service Type');
   $objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Mandate');

   $objPHPExcel->getActiveSheet()->SetCellValue('J1', 'Start Date');
   $objPHPExcel->getActiveSheet()->SetCellValue('K1', 'End Date');
   $objPHPExcel->getActiveSheet()->SetCellValue('L1', 'Print Status');

  


	$query = " CALL proc_getSchStudentParaTempCoverageChanges ('{$FromDate}' ,  '{$ToDate}') "; 
                         



	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 1;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	


			$linecount++;


            $row_num++;

			
            $print_status = 'Printed';

			if	($row['PrintFL'] == 0) {

				$print_status = 'Not Printed';


			} 

          
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['Student Name']);
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['Student OSIS #']);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['Provider Name']);

		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['Provider HR ID']);

		    $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['Provider SSN']);
		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $row['School']);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['District']);
		    $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['Service Type']);
		    $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['Mandate']);

		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['Start Date']);
		    $objPHPExcel->getActiveSheet()->setCellValue('K'.$row_num, $row['End Date']);
		    $objPHPExcel->getActiveSheet()->setCellValue('L'.$row_num, $print_status);

		/*    
		if	($row['PrintFL'] == 0) {

			$query1 = "INSERT into SchStudentParaTempCoverageReported
						( 
							StudentId,
							RegistrantId,
							SchoolId,
							StartDate,
							EndDate,
							UserId,
							TransDate
						)
						VALUES (
							'{$row['StudentId']}',
							'{$row['RegistrantId']}',
							'{$row['SchoolId']}',
							'{$row['StartDateUnf']}',
							'{$row['EndDateUnf']}',
							'{$UserId}',
                             NOW()


						) ";
			  
			$result1 = $connection1->query ($query1);

 
		}
		*/

		 
 
	}	

 
		$connection->disconnect();
		$connection1->disconnect();
 


//sleep(10);  

	/*=========================================================================*/

// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";

$SheetName = 'Para Temp Coverage Chnages';
$objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/ParaTempCoverageChanges.xlsx";

$objWriter->save($out_File);

// Echo done
echo  "{ success: true, transactions: '{$linecount}'}";

/*==============================*/

    
 
   $DownloadedFileName = 'ParaTempCoverageChanges-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    //unlink($out_File);    

    exit;
  
   

?>