	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchSchoolRegistrantsAvailability$$

	CREATE PROCEDURE proc_getSchSchoolRegistrantsAvailability (IN 	p_registrant_type_id INT, 
																	p_payroll_week DATE
														)  

	BEGIN

			DECLARE v_MonDate, v_TueDate, v_WedDate, v_ThuDate, v_FriDate DATE ; 


			/* Fri
			 ========*/ 
			SELECT DATE_ADD(p_payroll_week, INTERVAL -1 DAY) INTO v_FriDate;

			/* Thu
			 ========*/ 
			SELECT DATE_ADD(p_payroll_week, INTERVAL -2 DAY) INTO v_ThuDate;

			/* Wed
			 ========*/ 
			SELECT DATE_ADD(p_payroll_week, INTERVAL -3 DAY) INTO v_WedDate;


			/* Tue
			 ========*/ 
			SELECT DATE_ADD(p_payroll_week, INTERVAL -4 DAY) INTO v_TueDate;

			/* Mon
			 ========*/ 
			SELECT DATE_ADD(p_payroll_week, INTERVAL -5 DAY) INTO v_MonDate;



	 		

	 		create temporary table tmp engine=memory
			
			SELECT RegistrantId,
			       p_payroll_week as PayrollWeek,
				   v_MonDate as ServiceDateMon,
			       v_TueDate as ServiceDateTue,
			       v_WedDate as ServiceDateWed,
			       v_ThuDate as ServiceDateThu,
			       v_FriDate as ServiceDateFri,
			       0 as AvailMonFL,
			       0 as AvailTueFL,
			       0 as AvailWedFL,
			       0 as AvailThuFL,
			       0 as AvailFriFL,
				   CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
				   CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers


				FROM ClientApprovedRegistrants a,
				     Registrants c
			WHERE EXISTS (SELECT 1 FROM Clients b
                WHERE a.ClientId = b.Id 
                AND   c.StatusId = '1'
                AND SchoolFL = '1'
                AND c.TypeId = p_registrant_type_id )
				AND a.RegistrantId = c.Id
            AND NOT EXISTS (SELECT 1 FROM SchStudentAssignmentDetails d, 
                                          SchStudentAssignmentHeader e
                              WHERE a.RegistrantId = d.RegistrantId
                              AND   d.AssignmentId = e.Id 
                              AND   e.StatusId = '1'
                              AND p_payroll_week between e.StartDate and e.EndDate   
            
            )    
          
            AND NOT EXISTS (SELECT 1 FROM SchSchoolAssignmentDetails d, 
                                          SchSchoolAssignmentHeader e
                              WHERE a.RegistrantId = d.RegistrantId
                              AND   d.AssignmentId = e.Id 
                              AND   e.StatusId = '1'
                              AND p_payroll_week between e.StartDate and e.EndDate   
            
            )    
			
			;

			
			/* Set (SCH) Registrants Availability 
			 ====================================*/

			 UPDATE tmp a 
			 	SET AvailMonFL =  (SELECT 1 FROM  SchRegistrantsAvailability b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateMon = b.ServiceDate
								  ),
			 	    AvailTueFL =  (SELECT 1 FROM  SchRegistrantsAvailability b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateTue = b.ServiceDate
								  ),

			 	    AvailWedFL =  (SELECT 1 FROM  SchRegistrantsAvailability b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateWed = b.ServiceDate
								  ),

			 	    AvailThuFL =  (SELECT 1 FROM  SchRegistrantsAvailability b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateThu = b.ServiceDate
								  ),
			 	    AvailFriFL =  (SELECT 1 FROM  SchRegistrantsAvailability b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateFri = b.ServiceDate
								  ) ;


			/* Set (SCH) Registrants Availability 
			 ====================================*/

			 UPDATE tmp a, WeeklyServices b 
			 	SET a.AvailMonFL =  '2' 
                WHERE a.RegistrantId = b.RegistrantId
                AND   a.ServiceDateMon = b.ServiceDate ;
								  
			 UPDATE tmp a, WeeklyServices b 
			 	SET a.AvailTueFL =  '2' 
                WHERE a.RegistrantId = b.RegistrantId
                AND   a.ServiceDateTue = b.ServiceDate ;

			 UPDATE tmp a, WeeklyServices b 
			 	SET a.AvailWedFL =  '2' 
                WHERE a.RegistrantId = b.RegistrantId
                AND   a.ServiceDateWed = b.ServiceDate ;

			 UPDATE tmp a, WeeklyServices b 
			 	SET a.AvailThuFL =  '2' 
                WHERE a.RegistrantId = b.RegistrantId
                AND   a.ServiceDateThu = b.ServiceDate ;

			 UPDATE tmp a, WeeklyServices b 
			 	SET a.AvailFriFL =  '2' 
                WHERE a.RegistrantId = b.RegistrantId
                AND   a.ServiceDateFri = b.ServiceDate ;

	/*		 
			 UPDATE tmp a 
			 	SET AvailMonFL =  (SELECT 2 FROM  WeeklyServices b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateMon = b.ServiceDate
								  ),
			 	    AvailTueFL =  (SELECT 2 FROM  WeeklyServices b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateTue = b.ServiceDate
								  ),

			 	    AvailWedFL =  (SELECT 2 FROM  WeeklyServices b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateWed = b.ServiceDate
								  ),

			 	    AvailThuFL =  (SELECT 2 FROM  WeeklyServices b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateThu = b.ServiceDate
								  ),
			 	    AvailFriFL =  (SELECT 2 FROM  WeeklyServices b
			 		                WHERE a.RegistrantId = b.RegistrantId
			 		                AND   a.ServiceDateFri = b.ServiceDate
								  ) ;
	*/



			/* Add Comments 
			 ======================*/

			 ALTER TABLE tmp ADD Comments VARCHAR(512);
			 ALTER TABLE tmp ADD CommentId BIGINT(11);
			 ALTER TABLE tmp ADD UserId BIGINT(11);
			 ALTER TABLE tmp ADD UserName VARCHAR(128);
			 
			 ALTER TABLE tmp ADD TransDate DATETIME;


			UPDATE tmp a, SchRegistrantsAvailabilityComments b
				SET a.CommentId = b.Id,
				    a.Comments = b.Comments,
					a.UserId = b.UserId,
					a.TransDate = b.TransDate
			WHERE a.PayrollWeek = b.PayrollWeek
			AND   a.RegistrantId = b.RegistrantId;		

			UPDATE tmp a, Users b
				SET a.UserName = 	CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) )
			WHERE a.UserId IS NOT NULL
			ANd   a.UserId = b.UserId ;  	



			SELECT * FROM tmp
			ORDER BY RegistrantName;

			drop temporary table if exists tmp;


	END $$

	DELIMITER ;		
