  
<?php
    
/*  
 error_reporting(E_ALL);
 ini_set('display_errors', TRUE);
 ini_set('display_startup_errors', TRUE);
*/  

	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');
	require_once("db_GetSetData.php");

	include 'db_login.php';

	$conn=mysqli_connect($db_host, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  die( "Could not query the database: " . mysqli_connect_error());
	  }
	 
	$inputFileName = '../uploads/Student_Info.xlsx';
	$inputFileType = 'Excel2007';


    $ExtId = $_POST['ExtId'];
    $UserId = $_POST['UserId'];

	if (!$UserId) {

		$UserId = '1';
	}


   	  
 
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}
    	  

	/**  Create a new Reader of the type defined in $inputFileType  **/
	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	//$objReader->setLoadSheetsOnly($sheetname);
	$objReader->setReadDataOnly(true);


	$objPHPExcel = $objReader->load($inputFileName);


	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
	$i = 0;	
	$x = 0;
	$linecount = 0;	
 	$first_name_row = 0;
 	$home_phone_row = 0;
    $last_name_row = 0;
	$mobile_phone_row = 0;  
	$street_address_1_row = 0;
	$work_phone_row = 0;
	$street_address_2_row = 0;
	$email_row = 0;
	$city_row = 0;
 	$state_row = 0;
 	$zip_row = 0;

	$x = 0;

	foreach ($sheetData as &$row) { // read excel - start  

			$linecount++;




			$search_string  = 'Summary of Student Mandates#:';
			if  (strstr($row["A"], $search_string)) { // Student OSIS # - Start
 
 	        	$student_osis =  $row["A"];
 	        	$student_osis = preg_replace('/[^0-9]/', '', $student_osis);

 	        	//echo 'Student OSIS: '.trim($student_osis).'</br>'; 

 	        	//==============================================

                if ($student_osis == $ExtId) {

                    $i++;
 
                }




 	        } // Student OSIS # - End

 	        if ($i > 0) { //Preferred Written Language: - Start

				$search_string  = 'Preferred Written Language:';
				if  (strstr($row["A"], $search_string)) { // Student OSIS # - Start
	 

	 	        	$x = $linecount;
	 	        }	
 	         	

 	        } //Preferred Written Language: - End	

			
 	        if ($x > 0) { // $x - start
				
 	         	

 	        	// - First Name ====================== Start 
				$search_string  = 'First Name:';
				if  (strstr($row["A"], $search_string)) { // Search String - Start

					$first_name_row = $linecount + 1; 
					//echo 'First Name Found.../$linecount/$first_name_row: '.$row["A"].' '.$linecount.' '.$first_name_row.'</br>'; 

				} // Search String - End

 

				if ($linecount == $first_name_row) { // Get First Name - Start



					if ($row["A"] != 'Home Phone:') {

						$guardian_first_name = $row["A"];
						//echo 'Guardian First Name: '.trim($guardian_first_name).'</br>'; 
					}

					$first_name_row = 0;
				} // Get First Name - End
 	        	// - First Name ====================== End 

			 
 	        	// - Home Phone ====================== Start 
				$search_string  = 'Home Phone:';
				if  (strstr($row["A"], $search_string)) { // Search String - Start

					$home_phone_row = $linecount + 1; 

				} // Search String - End

 

				if ($linecount == $home_phone_row) { // Get Home Phone - Start



					if ($row["A"] != 'Last Name:') {

						$home_phone = $row["A"];
						//echo 'Guardian Home Phone: '.trim($home_phone).'</br>'; 
					}

					$home_phone_row = 0;
				} // Get Home Phone - End
 	        	
 	        	// - Home Phone ====================== End 
			 

                 // - Last Name ====================== Start 
                $search_string  = 'Last Name:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $last_name_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $last_name_row) { // Get Last Name - Start



                    if ($row["A"] != 'Cell Phone:') {

                        $guardian_last_name = $row["A"];
                        //echo 'Guardian Last Name: '.trim($guardian_last_name).'</br>'; 
                    }

                    $last_name_row = 0;
                } // Get Last Name - End
                // - Last Name ====================== End 


                // - Cell Phone ====================== Start 
                $search_string  = 'Cell Phone:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $mobile_phone_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $mobile_phone_row) { // Get Cell Phone - Start



                    if ($row["A"] != 'Street Address 1:') {

                        $guardian_mobile_phone = $row["A"];
                        //echo 'Guardian Cell Phone: '.trim($guardian_mobile_phone).'</br>'; 
                    }

                    $mobile_phone_row = 0;
                } // Get Cell Phone - End
                // - Cell Phone ====================== End 

                // - Street Address 1 ====================== Start 
                $search_string  = 'Street Address 1:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $street_address_1_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $street_address_1_row) { // Get Street Address 1 - Start



                    if ($row["A"] != 'Work Phone:') {

                        $guardian_street_address_1 = $row["A"];
                        //echo 'Guardian Street Address 1: '.trim($guardian_street_address_1).'</br>'; 
                    }

                    $street_address_1_row = 0;
                } // Get Street Address 1 - End
                // - Street Address 1 ====================== End 


                // - Work Phone ====================== Start 
                $search_string  = 'Work Phone:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $work_phone_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $work_phone_row) { // Get Work Phone - Start



                    if ($row["A"] != 'Street Address 2:') {

                        $guardian_work_phone = $row["A"];
                        //echo 'Guardian Work Phone: '.trim($guardian_work_phone).'</br>'; 
                    }

                    $work_phone_row = 0;
                } // Get Work Phone - End
                // - Work Phone ====================== End 

                 // - Street Address 2 ====================== Start 
                $search_string  = 'Street Address 2:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $street_address_2_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $street_address_2_row) { // Get Street Address 2 - Start



                    if ($row["A"] != 'Email:') {

                        $guardian_street_address_2 = $row["A"];
                        //echo 'Guardian Street Address 2: '.trim($guardian_street_address_2).'</br>'; 
                    }

                    $street_address_2_row = 0;
                } // Get Street Address 2 - End
                // - Street Address 2 ====================== End 

                 // - Email ====================== Start 
                $search_string  = 'Email:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $email_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $email_row) { // Get Email - Start



                    if ($row["A"] != 'City:') {

                        $guardian_email = $row["A"];
                        //echo 'Guardian Email: '.trim($guardian_email).'</br>'; 
                    }

                    $email_row = 0;
                } // Get Email - End
                // - Email ====================== End 

                 // - City ====================== Start 
                $search_string  = 'City:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $city_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $city_row) { // Get City - Start



                    if ($row["A"] != 'State:') {

                        $guardian_city = $row["A"];
                        //echo 'Guardian City: '.trim($guardian_city).'</br>'; 
                    }

                    $city_row = 0;
                } // Get City - End
                // - City ====================== End 

                // - State ====================== Start 
                $search_string  = 'State:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $state_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $state_row) { // Get State - Start



                    if ($row["A"] != 'Zip:') {

                        $guardian_state = $row["A"];
                        //echo 'Guardian State: '.trim($guardian_state).'</br>'; 
                    }

                    $state_row = 0;
                } // Get State - End
                // - State ====================== End 



                 // - Zip ====================== Start 
                $search_string  = 'Zip:';
                if  (strstr($row["A"], $search_string)) { // Search String - Start

                    $zip_row = $linecount + 1; 

                } // Search String - End

 

                if ($linecount == $zip_row) { // Get Zip - Start



                    if ($row["A"] != '') {

                        $guardian_zip = $row["A"];
                        //echo 'Guardian Zip: '.trim($guardian_zip).'</br>'; 
                    }

                    $zip_row = 0;
                } // Get Zip - End
                // - Zip ====================== End 


			} // $x - end 	
	



		}	
 	

  	        if ($i > 0) { //Preferred Written Language: - Start


	            $query = "UPDATE  SchStudents
							SET
							StreetAddress1 = '{$guardian_street_address_1}',
							StreetAddress2 = '{$guardian_street_address_2}',
							City = '{$guardian_city}',
							State = '{$guardian_state}',
							ZipCode = '{$guardian_zip}',
							MobilePhone = '{$guardian_mobile_phone}',
							GuardianName = CONCAT('{$guardian_first_name}', ' ','{$guardian_last_name}'),
							GuardianPhone = '{$home_phone}',
							GuardianEmail = '{$guardian_email}',
							UserId = '{$UserId}',
							TransDate = NOW()
							WHERE ExtId = '{$student_osis}' ";    

				$ret =  setData ($conn, $query);   			
				

                $upload_comments = "Student Contact Information was uploaded!";
 	         	
            } else {      
 	        
                $upload_comments = "Selected file does not contain valid Student Contact Information!";

            }    //Preferred Written Language: - End	
	         



 	mysqli_close($conn);


	//$upload_comments = 'Profile Uploadeded Mandates: '.$linecount->ChildFirstName;

	echo "{ success: true, upload_comments: '{$upload_comments}'}";

	/*
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";
	*/

?>