<?php


	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);


	$clientId = "35dc1fe2-02d4-432f-a5d4-71e876f1c6b2";
	$responseType = "code";
	$action = "Login";
	$clientSecret = "qnCZuTw0ZCxGGSgVuOjzgI59";
	$username = "rcmhealthcare.api";
	$password = "Welcome123!";
	$redirectUri = "https://www.ewebstaffing.com/rcm/data/RedirectlBullhornAPI.php";




		// get Code
		// ===========

		$url_code = "https://auth-east.bullhornstaffing.com/oauth/authorize?client_id=" . urlencode($clientId) . "&response_type=" . urlencode($responseType) . "&action=" . urlencode($action) . "&username=" . urlencode($username) . "&password=" . urlencode($password) . "&redirect_uri=" . urlencode($redirectUri);

	    $code = '';

		// Initialize cURL
		$ch = curl_init();

		// Set cURL options
		curl_setopt($ch, CURLOPT_URL, $url_code);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

		// Execute the request
		$response = curl_exec($ch);

		// var_dump($response);

		// Check for errors
		if (curl_errno($ch)) {
		    echo "cURL error: " . curl_error($ch);
		} else {
		    // Decode the response

			
			$code = $response;
			// echo " code: $code<br><br>";
		}

		// Close the cURL session
		curl_close($ch);


		//=========
		// Get Token
		// ============

		$grantType = "authorization_code";
 
		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://auth.bullhornstaffing.com/oauth/token?grant_type='. $grantType. '&code=' . $code . '&client_id=' . $clientId .'&client_secret=' . $clientSecret .'&redirect_uri=' . $redirectUri,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		));

			$response1 = curl_exec($curl);

			$result1 = json_decode($response1, true);
			$access_token = $result1["access_token"];	

			// echo " access_token: $access_token<br><br>";


		//=============
		// Get REST URL
		// ============


		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://rest.bullhornstaffing.com/rest-services/login?version=2.0&access_token=' . $access_token,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		  // CURLOPT_HTTPHEADER => array(
		  //   'Cookie: BhRestToken=24058_7549830_631d563b-cc4b-48a5-892f-0d2759d0cde2'
		  // ),
		));

		$response2 = curl_exec($curl);


		$result2 = json_decode($response2, true);
		$BhRestToken = $result2["BhRestToken"];	
		$rest_url = $result2["restUrl"];	

		// echo " BhRestToken: $BhRestToken rest_url:  $rest_url<br><br>";






         // ====================
        // Get Recent Placements
 		//=====================   
 
		$curl = curl_init();

 
		curl_setopt_array($curl, array(
		  CURLOPT_URL =>   $rest_url.'search/Placement?BhRestToken='.$BhRestToken . '&fields=id,candidate,jobOrder&query=dateAdded:[20230301%20TO%20*]&count=1000',
 

		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));

		$response4 = curl_exec($curl);

		curl_close($curl);
		
		$data = json_decode($response4, true);
		// foreach ($data  as $row) {
	
		// 	print_r($row['jobOrder'] );
		// 	echo '<br>';
		// }	

		$corp_id_arr = array();

		foreach ($data['data'] as $item) {

	 
		    // Get the jobOrder information
		    $jobOrderId = $item['jobOrder']['id'];
		    $job_order_title = $item['jobOrder']['title'];

		    if ($item['candidate']['id']) {

			    $candidateId = $item['candidate']['id'];

		    }
		    
		    $clientCorpID = getOneJobOrder($rest_url, $BhRestToken, $jobOrderId);



		    IF ($clientCorpID == 29429) {


		    echo 'CientCorpId: '.$clientCorpID.' JobOrder:'.$jobOrderId.' Candiate: '.$candidateId.'<br>';	

		    	getCandidate($rest_url, $BhRestToken, $candidateId);


 
		    } 

		    // if(!in_array($corp_id, $corp_id_arr, true)){
        	// 	array_push($corp_id_arr, $corp_id);
    		// }		

		    // Do something with the jobOrder information, such as print it
		    // echo "Job Order ID: " . $job_order_id . "<br>";
		    // echo "Job Order Title: " . $job_order_title . "<br>";
		}
		// echo($response4);
		// echo '<br>';
 
		// foreach($corp_id_arr as $clientCorpID){
	    
			// echo $clientCorpID;
	 		// echo '<br>';

	    // }
 

	function getOneJobOrder($rest_url, $BhRestToken, $jobOrderId) {
        // ====================
        // Get Job Order
 		//=====================   

		$curl = curl_init();

 
		curl_setopt_array($curl, array(
		  CURLOPT_URL =>   $rest_url.'query/JobOrder?BhRestToken='.$BhRestToken . '&fields=id,clientCorporation&where=id='.$jobOrderId,
 

		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));

		$response5 = curl_exec($curl);

		curl_close($curl);
		
		$data = json_decode($response5, true);
		
		$clientCorpID = $data['data'][0]['clientCorporation']['id'] ; 
		$clientCorpName = $data['data'][0]['clientCorporation']['name'] ; 

		return $clientCorpID;
		


    } 

	function getCandidate($rest_url, $BhRestToken, $candidateId) {

       // ====================
       // Get Candidates data
 		//=====================   

		$curl = curl_init();


		curl_setopt_array($curl, array(
		// CURLOPT_URL =>   $rest_url.'search/Candidate?BhRestToken='.$BhRestToken . '&query=(id='.$candidateId .')&fields=id,companyName,submissions,phone,mobile,ssn,customInt10,occupation,specialties,employeeType,firstName,lastName,address,sendouts&start=0&count=50',

		CURLOPT_URL =>   $rest_url.'search/Candidate?BhRestToken='.$BhRestToken .'&fields=ssn,occupation,gender,firstName,lastName,address,phone,mobile,email,customInt10,employeeType,owner,comments&query='.$candidateId,

		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));

		$response3 = curl_exec($curl);

		curl_close($curl);

		// echo $response3;

		$data = json_decode($response3, true);
		//print_r ($result3["data"][0]) ;
 
		foreach ($data['data'] as $row) {
		

			    echo " ssn: " . $row['ssn'] . "<br>";
			    echo " occupation: " . $row['occupation'] . "<br>";
			    echo " gender: " . print_r($row['gender']) . "<br>";
			    echo " firstName: " . $row['firstName'] . "<br>";
			    echo " lastName: " . $row['lastName'] . "<br>";
			    echo " phone: " . $row['phone'] . "<br>";
			    echo " mobile: " . $row['mobile'] . "<br>";
			    echo " email: " . $row['email'] . "<br>";
			    echo " HRID: " . $row['customInt10'] . "<br>";
			    echo " owner: " . print_r($row['owner']) . "<br>";
			    echo " employeeType: " . $row['employeeType'] . "<br>";
			    echo " comments: " . $row['firstName'] . "<br>";
			    echo " address: " . print_r($row['address']) . "<br>";
			    
			    // $send_out_arr = $row['sendouts']; 
			    
			    // echo " # of sendouts: " . $send_out_arr['total'] . "<br>";

			    // echo " sendouts details: " . print_r($send_out_arr	) . "<br>";

			    // echo " sendouts: " . print_r($send_out_arr) . "<br>";
			    echo " ====================================<br>";

			 

		}
    }

?>

 