<?php

	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);

	require_once('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/Writer/Excel2007.php');

	try {
		$conn = new PDO("mysql:host=$db_host;dbname=$db_database;charset=utf8", $db_username, $db_password);
		$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	} catch (PDOException $e) {
		die("Connection failed: " . $e->getMessage());
	}

	$PayrollBatchNumber = $_GET['PayrollBatchNumber'] ?? '';
	$objPHPExcel = new PHPExcel();
	$sheet = $objPHPExcel->setActiveSheetIndex(0);

	$headers = ['Applicant Name', 'HRID', 'Service Date', 'Start Time', 'End Time', 'District', 'Service Type', 'Billing Contract', 'Hours', 'School Name DBN', 'Borough'];
	$col = 'A';
	foreach ($headers as $header) {
		$sheet->setCellValue($col . '1', $header);
		$col++;
	}

	try {
		$stmt = $conn->prepare("CALL proc_getSchEWebToPayrollUploadData(?, '1', 'Contract')");
		$stmt->execute([$PayrollBatchNumber]);

		$row_num = 2;
		while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {

            // $district_number = preg_replace('/\D/', '', $row['DistrictName']);
            // Step 1: Remove the part of the string that starts with "CSE..."
            $stringWithoutCSE = preg_replace('/\sCSE.*/', '', $row['DistrictName']);

            // Step 2: Extract digits from the remaining string
            preg_match('/\d+/', $stringWithoutCSE, $matches);
            $district_number = $matches[0];

			$sheet->setCellValue("A$row_num", $row['RegistrantName'])
			->setCellValue("B$row_num", $row['HrId'])
			->setCellValue("C$row_num", $row['ServiceDate'])
			->setCellValue("D$row_num", $row['StartTime'])
			->setCellValue("E$row_num", $row['EndTime'])
			->setCellValue("F$row_num", $district_number)
			->setCellValue("G$row_num", $row['ServiceTypeDesc'])
			->setCellValue("H$row_num", $row['BillingContractName'])
			->setCellValue("I$row_num", $row['TotalHours'])
			->setCellValue("J$row_num", $row['SchoolNameDBN'])
			->setCellValue("K$row_num", $row['BoroughCode']);

			$row_num++;
		}

		$conn = null;

		$sheet->setTitle('Employee');
		$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
		$out_File = "../uploads/eweb_to_payroll_upload_th__duplicate.xlsx";
		$objWriter->save($out_File);

		$DownloadedFileName = "PayrollUploadFileBatch-$PayrollBatchNumber.xlsx";
		header('Content-Description: File Transfer');
		header('Content-Type: application/octet-stream');
		header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');
		header('Content-Transfer-Encoding: binary');
		header('Expires: 0');
		header('Cache-Control: must-revalidate');
		header('Pragma: public');
		header('Content-Length: ' . filesize($out_File));
		ob_clean();
		flush();
		readfile($out_File);
		unlink($out_File);
		exit;
	} catch (PDOException $e) {
		die("Error: " . $e->getMessage());
	}

?>