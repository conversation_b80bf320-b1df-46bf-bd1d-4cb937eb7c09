<?php 
	

    require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];

    $query = "	SELECT   distinct   a.RegistrantId as id,  
    								a.RegistrantId, 
 									CONCAT( trim( d.LastName) , ', ', trim( d.FirstName)  ) as RegistrantName 
						 


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
 			    Registrants d 
		WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
		    AND a.ScheduleStatusId = '7'
 			AND a.ServiceTypeId = b.Id	
			AND b.ServiceCategoryId = 1
			AND a.StudentId = c.Id
			AND d.TypeId = 23  	
            AND a.Registrantid = d.Id
 		ORDER BY RegistrantName, a.ServiceDate, a.StartTime	

			  ";

	$ret = getData ($conn, $query);
	
	setDisConn($conn);

	echo $ret;


 
?>
