<?php 
	

     require_once("db_GetSetData.php");

	$conn = getCon();

    $PayrollWeek = $_GET['PayrollWeek'];
    $ParaTherapyTypeId = $_GET['ParaTherapyTypeId'];
    $HrTypeId = $_GET['HrTypeId'];

 	if ($ParaTherapyTypeId == '23') {

					    $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <='{$PayrollWeek}' 
					                        AND   c.HrTypeId =  '{$HrTypeId}'
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId = '23'
					                        AND   a.ScheduleStatusId > 5
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-09-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours 
  											LIMIT 500		

					                  ";



					    } 	elseif ($ParaTherapyTypeId == '12') {

					    $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <='{$PayrollWeek}' 
					                        AND   c.HrTypeId =  '{$HrTypeId}'
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId != '23'
					                        AND   a.ScheduleStatusId > 5
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-09-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
					                    ";


					     


					 }   else {

					    $query = "  SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
					                        a.ServiceDate AS ServiceDateSort,
					                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
					                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
					                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
					                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
					                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
					                      
					                      SessionGrpSize,
					                      a.RegistrantId,
					                      c.SearchId as RegistrantSearchId,
					                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
					                                                    where a.Id = c.id LIMIT 1  
					                                            ) as ScheduleStatusId,
					                                            ScheduleStatusDesc,
					                      TextColor,
					                      BackgroundColor,
					                      d.SchoolName as SchoolNameDBN,
					                      f.SchoolName as SchoolName,
					                      ServiceTypeDesc,
					                      CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
					                      
					                      CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
					                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate

					                  FROM  WeeklyServices a, 
					                        SchStudents b,
					                        Registrants c, 
					                        ScheduleStatuses g,
					                        RegistrantTypes h, 
					                        SchSchools d,
					                        SchSubSchools f,
					                        Users e,
					                        SchServiceTypes i
					                        WHERE a.PayrollWeek <='{$PayrollWeek}' 
					                        AND   c.HrTypeId =  '{$HrTypeId}'
					                        AND   a.PaidFL = '0'
					                        and   c.TypeId not in('12','23') 
					                        AND   a.ScheduleStatusId > 6
					                        AND   b.Id = a.StudentId
					                        AND   a.ScheduleStatusId = g.Id
					                        AND   a.RegistrantId = c.Id
					                        AND   c.TypeId = h.Id
					                        AND   a.SchoolId = d.Id   
					                        AND   a.ServiceTypeId = i.Id                     
					                        AND   a.UserId = e.UserId
					                        AND   a.SchoolId = f.SchoolId
					                        AND   b.SubSchoolTypeId = f.SubSchoolTypeId 
					                        AND   a.ServiceDate >= '2023-09-01'
					                  GROUP BY RegistrantName, ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
					                    

					                    ";


					    }
                                

	 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;


 

?>
  