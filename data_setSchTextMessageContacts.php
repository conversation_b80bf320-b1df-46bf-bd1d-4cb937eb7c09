<?php 

  require_once("db_GetSetData.php");

	$conn = getCon();

	  error_reporting(E_ALL);
	  ini_set('display_errors', TRUE);
	  ini_set('display_startup_errors', TRUE);

  // Get SendHub Credentials
   //=========================

   $query = " SELECT 
					    ShUserId, ShPass
					FROM
					    Company
                         ";

		$result=mysqli_query($conn,$query); 

		while ($row = $result->fetch_assoc()) {

			$vs_user = 	$row["ShUserId"];	
			$vs_api_key = 	$row["ShPass"];	

		};	
		
		setDisConn($conn);   
	 
		
		// $vs_user = '<EMAIL>';
		// $vs_api_key = 'e41f5c4b4f445b8932e1dfdaace4e2d47c023dd9';


		$vs_url = "https://api.sendhub.com/v1/contacts/?username={$vs_user}&api_key={$vs_api_key}";

		

   $a = array();

	 $conn = getCon();
   
   $query = " SELECT 
					    a.Id as 'EmployeeID',
					    'DOE' as 'EmployeeType',
					    CONCAT(a.FirstName, ' ', a.LastName) AS 'name',
					    MobilePhone as 'number',
					    Email as 'email', 
					    StreetAddress1 as 'address_street',
					    City as 'address_city' ,
					    ZipCode as 'address_zip',					    
					    RegistrantTypeDesc as 'ServiceArea'
					FROM
					    Registrants a
					        JOIN
					    RegistrantTypes b ON a.TypeId = b.Id
					WHERE
					    a.StatusId = '1'
					    AND trim(EmailContactId) = '' 
					LIMIT 10	 ";

					$result=mysqli_query($conn,$query); 


					


							$o_ch = curl_init();



							while ($row = $result->fetch_assoc()) {

								$contact_json = json_encode($row);  	

							curl_setopt($o_ch, CURLOPT_URL, $vs_url);
							curl_setopt($o_ch, CURLOPT_HEADER, false);
							curl_setopt($o_ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
							curl_setopt($o_ch, CURLOPT_POSTFIELDS, $contact_json	);

								

								var_dump($contact_json);
								echo '<br><br>';		

              //===================


							curl_setopt($o_ch, CURLOPT_RETURNTRANSFER, 1);

							$vs_return = curl_exec($o_ch);

				   		$va_return_decoded = json_decode($vs_return, TRUE);


							//==================== 	


							$b = array(

												'RegistrantId' => $row["EmployeeID"],
												'EmailContactId' => $va_return_decoded["id"]  
 										 
											); 
									
											array_push($a, $b);



						  }		


					mysqli_free_result($result);
          setDisConn($conn);   

				  curl_close($o_ch); 	

          print_r($a); 


					$conn = getCon();

 					
          // Update Text Msg  Contact ID
          //===========================

				foreach($a as $contact) {

				$provider_id = $contact["RegistrantId"];
				$text_msg_id = $contact["EmailContactId"];
						

				$query = "UPDATE Registrants
									  SET EmailContactId = '{$text_msg_id}'
									WHERE Id = '{$provider_id}'  

				 ";  
				 
				echo '<pre>'; echo($query);

					$result=mysqli_query($conn,$query); 


				}

        setDisConn($conn);   


 
  
?>




 