<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$StartDate = $_GET['StartDate'];
	$EndDate = $_GET['EndDate'];
 

    $query = "SELECT 
    			a.StudentId,
    			a.Id as aMandateId,
                b.Id as bMandateId,
                a.<PERSON>ntId as endRegRegistrantId,
                b.<PERSON>ntId as strRegRegistrantId,
                DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) as 'CovEndDate',
                DATE_FORMAT( b.StartDate, '%m-%d-%Y' ) as 'CovStartDate',
                CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as 'StudentName',
                c.ExtId as 'OSIS#',
                CONCAT(d.SchoolName, ' ',d.ExtId) as 'SchoolDBN',
                CONCAT(i.SchoolName, ' ',d.ExtId) as 'School',
                DistrictName as 'District',
                ServiceTypeDesc as 'ServiceTypeDesc',
            CONCAT( a.SessionGrpSize , ' ', a.SessionFrequency , 'xWeekly ', a.ParaTransportPerc,'%' ) as' Mandate',
            CONCAT( trim( e.LastName) , ', ', trim(e.FirstName)) as 'endProviderName',
              e.HrId as 'endProviderHRID',
              e.ExtId as 'endProviderSSN',
            CONCAT( trim( g.LastName) , ', ', trim(g.FirstName)) as 'strProviderName',
              g.HrId as 'strProviderHRID',
              g.ExtId as 'strProviderSSN',
			COALESCE((SELECT count(*) from SchStudentParaPermCoverageReported i 

                 WHERE a.StudentId = i.StudentId
                 AND   a.Id = i.aMandateId
                 AND   b.Id = i.bMandateId LIMIT 1

            ),0) as PrintFL                

         from       SchStudentMandates a,
                    SchStudentMandates b,
                    SchStudents c,
                    SchSchools d,
                    SchSubSchools i,
                    SchServiceTypes f,
                    Registrants e,
                    Registrants g,
                    SchDistricts h 

        where   a.EndDate BETWEEN '{$StartDate}' and '{$EndDate}'   
         and    DATE_ADD(a.EndDate, INTERVAL 1 DAY) = b.StartDate    
         and    a.StudentId = b.StudentId
         and    a.ServiceTypeId = b.ServiceTypeId
         and    a.SessionFrequency = b.SessionFrequency
         and    a.SessionLength  = b.SessionLength
         and    a.SessionGrpSize  = b.SessionGrpSize
         and    a.StudentId = c.Id
         and    a.ServiceTypeId = f.id 
         and    a.SchoolId = d.Id        
         and    a.SchoolId = i.SchoolId
         AND    c.SubSchoolTypeId = i.SubSchoolTypeId      
         and    a.RegistrantId != b.RegistrantId 
         and    a.RegistrantId = e.Id 
         and    b.RegistrantId = g.Id
         and    d.DistrictId = h.Id
         AND    e.TypeId = '23'
   ORDER BY c.LastName, c.FirstName, a.EndDate
          ";          

 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	//echo $query;


?>


