



   
  <html> 

    <head>

    <link rel="stylesheet" type="text/css" href="../WebRecruitingStyle.css">

    </head>


    <body>

      <div>
      

      <?php

        require_once("db_login.php");
        
        

        $ClientId = ($_GET["ClientId"]); 
        $ClientUnitId = ($_GET["ClientUnitId"]); 
        $UnitName = ($_GET["UnitName"]); 
        $StartDate = ($_GET["StartDate"]); 


        $conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
        // Check connection
        if (mysqli_connect_errno())
          {
          echo "Could not query the database: " . mysqli_connect_error();
          }

        $BackgroundColorACS = 'grey';
        $TextColor = 'white';
        $EmployeeTypeACS  = 'ACS Employee';
        $EmployeeTypeGotham  = 'ACS Employee';

      /*  
        $Legend = '</br><div> <span style= "font-size: 16px; width: 150;  padding: 5px; border:2px solid #a1a1a1; box-shadow: 10px 10px 5px #888888; border-radius:15px;  background:grey;color:'.$TextColor.';">'.$EmployeeTypeACS.'></span>';

        $Legend .= '<span style= "font-size: 16px; width: 150;  padding: 5px; border:2px solid #a1a1a1; box-shadow: 10px 10px 5px #888888; border-radius:15px;  background:green  ;color:'.$TextColor.';">'.$EmployeeTypeGotham.'</div></br>';
      */

        $Legend =    '<div>
<span style= "font-size: 14px; width: 200;  padding: 5px; border:2px solid #a1a1a1; box-shadow: 10px 10px 5px #888888; border-radius:15px;  background:grey; color:white ">ACS EMPLOYEE</span>
<span style= "font-size: 14px; width: 200;  padding: 5px; border:2px solid #a1a1a1; box-shadow: 10px 10px 5px #888888; border-radius:15px;  background:green; color:white ">GOTHAM EMPLOYEE</span>
</div></br></br>';

        $query = "Call proc_ClientACSMontlyCalendar ('{$ClientId}', '{$ClientUnitId}' , '{$StartDate}')  ";

        $result =  mysqli_query($conn, $query) or die
        ("Error in Selecting " . mysqli_error($conn));

        $TableHeader = '';


        while ($row = mysqli_fetch_array($result)) 
         { // read - start 

              
              // Table Header
              //===================
              $MonthYearName = $row['MonthYearName'];
              
              $TableHeader .= '<div class="calendar-title">'.$MonthYearName.' - Location: '.$UnitName.'</div>';  
              $TableHeader .= $Legend;  


              $TableHeader .= '<table border="1" class="calendar-table">';
              $TableHeader .= '<tr>';

              $TableHeader .= '<th class="calendar-th">Sun</th>';
              $TableHeader .= '<th class="calendar-th">Mon</th>';
              $TableHeader .= '<th class="calendar-th">Tue</th>';
              $TableHeader .= '<th class="calendar-th">Wed</th>';
              $TableHeader .= '<th class="calendar-th">Thu</th>';
              $TableHeader .= '<th class="calendar-th">Fri</th>';
              $TableHeader .= '<th class="calendar-th">Sat</th>';
              echo $TableHeader;


              // Table Row 1
              //===================

              $TableRow1 = '<tr>';
              $TableRow1 .= $row['TableCell_1'];
              $TableRow1 .= $row['TableCell_2'];
              $TableRow1 .= $row['TableCell_3'];
              $TableRow1 .= $row['TableCell_4'];
              $TableRow1 .= $row['TableCell_5'];
              $TableRow1 .= $row['TableCell_6'];
              $TableRow1 .= $row['TableCell_7'];
              $TableRow1 .= '</tr>';
              echo $TableRow1;


              // Table Row 2
              //===================

              $TableRow2 = '<tr>';
              $TableRow2 .= $row['TableCell_8'];
              $TableRow2 .= $row['TableCell_9'];
              $TableRow2 .= $row['TableCell_10'];
              $TableRow2 .= $row['TableCell_11'];
              $TableRow2 .= $row['TableCell_12'];
              $TableRow2 .= $row['TableCell_13'];
              $TableRow2 .= $row['TableCell_14'];
              $TableRow2 .= '</tr>';
              echo $TableRow2;

              // Table Row 3
              //===================

              $TableRow3 = '<tr>';
              $TableRow3 .= $row['TableCell_15'];
              $TableRow3 .= $row['TableCell_16'];
              $TableRow3 .= $row['TableCell_17'];
              $TableRow3 .= $row['TableCell_18'];
              $TableRow3 .= $row['TableCell_19'];
              $TableRow3 .= $row['TableCell_20'];
              $TableRow3 .= $row['TableCell_21'];
              $TableRow3 .= '</tr>';
              echo $TableRow3;

              // Table Row 4
              //===================

              $TableRow4 = '<tr>';
              $TableRow4 .= $row['TableCell_22'];
              $TableRow4 .= $row['TableCell_23'];
              $TableRow4 .= $row['TableCell_24'];
              $TableRow4 .= $row['TableCell_25'];
              $TableRow4 .= $row['TableCell_26'];
              $TableRow4 .= $row['TableCell_27'];
              $TableRow4 .= $row['TableCell_28'];
              $TableRow4 .= '</tr>';
              echo $TableRow4;

              // Table Row 5
              //===================

              $TableRow5 = '<tr>';
              $TableRow5 .= $row['TableCell_29'];
              $TableRow5 .= $row['TableCell_30'];
              $TableRow5 .= $row['TableCell_31'];
              $TableRow5 .= $row['TableCell_32'];
              $TableRow5 .= $row['TableCell_33'];
              $TableRow5 .= $row['TableCell_34'];
              $TableRow5 .= $row['TableCell_35'];
              $TableRow5 .= '</tr>';
              echo $TableRow5;

              // Table Row 6
              //===================

              $TableRow6 = '<tr>';
              $TableRow6 .= $row['TableCell_36'];
              $TableRow6 .= $row['TableCell_37'];
              $TableRow6 .= $row['TableCell_38'];
              $TableRow6 .= $row['TableCell_39'];
              $TableRow6 .= $row['TableCell_40'];
              $TableRow6 .= $row['TableCell_41'];
              $TableRow6 .= $row['TableCell_42'];
              $TableRow6 .= '</tr>';
              echo $TableRow6;

 

         } //read - end       
        

              // Table Tralser
              //===================

              $TableTrailer = '</tr>';
              $TableTrailer .= '</table>';

              //echo '<div class="calendar-title">'.$MonthYearName.'</div>';
              echo $TableTrailer;


        mysqli_free_result($result);
        mysqli_close($conn);

      ?>

      <!--<div class="calendar-title">{MonthYearName}</div>
      <table border="1" class="calendar-table">
        <tr>
          <th class="calendar-th">Sun</th>
          <th class="calendar-th">Mon</th>
          <th class="calendar-th">Tue</th>
          <th class="calendar-th">Wed</th>
          <th class="calendar-th">Thu</th>
          <th class="calendar-th">Fri</th>
          <th class="calendar-th">Sat</th>
        </tr>
       

       </table>--> 
      </div>  


    </body>

  </html>
 

