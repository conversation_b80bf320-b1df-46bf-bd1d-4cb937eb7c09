<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

$username = '<EMAIL>';
$password = 'dbmnvusghphictlf';

$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';

$inbox = imap_open($hostname, $username, $password) or die('Cannot connect to Gmail: ' . imap_last_error());

// Search for unread emails
$emails = imap_search($inbox, 'UNSEEN');

if ($emails) {
    rsort($emails); // Newest to oldest
    echo "Found " . count($emails) . " unread email(s).\n";

    foreach ($emails as $email_number) {
        try {

           // Skip if already seen (defensive)
            $flags = imap_fetch_overview($inbox, $email_number, 0)[0];
            if (isset($flags->seen) && $flags->seen) {
                echo "Email #$email_number already marked as read, skipping.\n";
                continue;
            }

            // Fetch email header
            $header = imap_headerinfo($inbox, $email_number);
            $subject = $header->subject ?? '';

            // Check subject
            if (
                stripos($subject, 'Posting') === false &&
                stripos($subject, 'posted') === false
            ) {
                echo "Skipped email #$email_number: Subject does not contain 'Posting' or 'posted'.\n";
                imap_setflag_full($inbox, $email_number, "\\Seen"); // Mark as read
                
                continue;
            }

            // Fetch structure
            $structure = imap_fetchstructure($inbox, $email_number);
            if (!isset($structure->parts) || !is_array($structure->parts)) {
                echo "Email #$email_number has no attachments.\n";
                continue;
            }

            // Loop through parts
            foreach ($structure->parts as $i => $part) {
                if (
                    isset($part->ifdisposition) &&
                    strtoupper($part->disposition) === 'ATTACHMENT'
                ) {
                    $filename = $part->dparameters[1]->value ?? 'attachment.csv';
                    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                    // Only process CSVs
                    if ($ext !== 'csv') {
                        echo "Skipping non-CSV attachment: $filename\n";
                        continue;
                    }

                    $body = imap_fetchbody($inbox, $email_number, $i + 1);
                    if ($part->encoding == 3) {
                        $body = base64_decode($body);
                    } elseif ($part->encoding == 4) {
                        $body = quoted_printable_decode($body);
                    }

                    // Save attachment
                    $timestamp = date("Y-m-d_H-i-s");
                    $file_name = "daily_report-$timestamp.csv";
                    $target_dir = __DIR__ . '/../rn_reports/';
                    $full_path = $target_dir . $file_name;

                    file_put_contents($full_path, $body);
                    echo "Saved: $file_name\n";

                    // Step 1: Import data
                    $import_url = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/data_setSchImportDailyPostingReportFile.php?Filename=' . urlencode($file_name);
                    echo "Calling: $import_url\n";
                    $import_result = file_get_contents($import_url);
                    echo "Import result: $import_result\n";

                    sleep(5);

                    // Step 2: Process data
                    $process_url = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/data_setSchProcessDailyPostingReportFile.php';
                    echo "Calling: $process_url\n";
                    $process_result = file_get_contents($process_url);
                    echo "Process result: $process_result\n";

                    sleep(5);
                }
            }
            imap_setflag_full($inbox, $email_number, "\\Seen");
        } catch (Exception $e) {
            echo "Error processing email #$email_number: " . $e->getMessage() . "\n";
            continue; // Move on to the next email
        }
    }
} else {
    echo "No unread emails found.\n";
}

// Close the IMAP connection
imap_close($inbox);

// Optional: Final process call outside loop
try {
    $final_url = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/data_setSchProcessDailyPostingReportFile.php';
    echo "Final call to: $final_url\n";
    $final_ret = file_get_contents($final_url);
    echo "Final process result: $final_ret\n";
} catch (Exception $e) {
    echo "Final processing error: " . $e->getMessage() . "\n";
}

?>
