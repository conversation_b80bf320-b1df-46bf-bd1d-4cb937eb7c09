<?php 

	require_once("db_GetSetData.php");
	$conn = getCon();

     $query = "CALL proc_getSchPayrollUploadBatchHeaders ()";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  

/*
	require "ewDataHandler.php"; 


	$rcr_transaction = new dataHandler(); 

	$result = $rcr_transaction->getSchPayrollUploadBatchHeaders();

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/  

?>
