<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$Month = $_GET['Month'];  
	$Year = $_GET['Year'];  
	$CPSEFormsStatus = $_GET['CPSEFormsStatus'];  
	$SchoolId = $_GET['SchoolId'];  

	/*======== CPSE Statuses Listing ========*/
		/*
		0 - Awaiting Provider Signature,
		1 - Awaiting Supervisor Signature,
		2 - Awaiting Parent Signature,
		3 - Awaiting Principal Signature,
		4 - Awaiting Agency Signature,
		5 - Fully Signed  
		*/
	/*=====================================================================*/


	/*======== Sel CPSE Forms for a give Year/Month - ALL STATUSES ========*/
	/*=====================================================================*/
	if (!isset($CPSEFormsStatus))  { // 0 - Awaiting Provider Signature,

		$query = " SELECT  	
	                        a.Id as MandateId,
							a.ServiceTypeId, 
						   	c.ServiceTypeDesc,	
					       	a.SessionLength,
					       	a.SessionGrpSize,
					       	a.SessionFrequency,
					       	a.StudentId,
					       	a.PlaceOfService,
					       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
					       	(SELECT COUNT(*) FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId = 8
					       	   	AND YEAR(d.ServiceDate) =  '{$Year}'
					       	  	AND MONTH(d.ServiceDate) =  '{$Month}'  


							) as NumbeOfSessions,
	                    /*    
	                        COALESCE((SELECT b.StatusId 
	                                    FROM SchCPSEFormSignaturesHeader b
	                                   WHERE a.Id = b.MandateId  
							       	   	AND b.Year =  '{$Year}'
							       	  	AND b.Month =  '{$Month}' LIMIT 1 

	                        ),0) as CPSEHeaderSignatuteStatus
						*/
	                    'Undefined' as CPSEHeaderSignatuteStatus    
					FROM SchStudentMandates a, 
					     SchStudents b, 
					     SchServiceTypes c 
					WHERE a.RegistrantId = '{$RegistrantId}'   
					/*AND a.StatusId = '1'*/
					AND a.StudentId = b.Id
					AND a.ServiceTypeId = c.Id
					AND BillingContractId = '3'
	 		  		
					AND EXISTS ( SELECT 1 FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId = 8
					        	  	AND YEAR(d.ServiceDate) =  '{$Year}'
					       	   	AND MONTH(d.ServiceDate) =  '{$Month}'  

					)
	                  

	                Order By b.LastName, b.FirstName
	                  ";


	}


	/*======== Sel CPSE Forms Awaiting PROVIDER Supervision ========*/
	/*=====================================================================*/
	if ($CPSEFormsStatus == '0')  {

		$query = " SELECT  	
	                        a.Id as MandateId,
							a.ServiceTypeId, 
						   	c.ServiceTypeDesc,	
					       	a.SessionLength,
					       	a.SessionGrpSize,
					       	a.SessionFrequency,
					       	a.StudentId,
					       	a.PlaceOfService,
					       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
					       	(SELECT COUNT(*) FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId = 8
					       	   	AND YEAR(d.ServiceDate) =  '{$Year}'
					       	  	AND MONTH(d.ServiceDate) =  '{$Month}'  

							) as NumbeOfSessions,
	                        '0'  as CPSEHeaderSignatuteStatus


					FROM SchStudentMandates a, 
					     SchStudents b, 
					     SchServiceTypes c 
					WHERE a.RegistrantId = '{$RegistrantId}'   
					AND a.StatusId = '1'
					AND a.StudentId = b.Id
					AND a.ServiceTypeId = c.Id
					AND StudentTypeId = '2'
	 		  		
					AND EXISTS ( SELECT 1 FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId = 8
					        	  	AND YEAR(d.ServiceDate) =  '{$Year}'
					       	   	AND MONTH(d.ServiceDate) =  '{$Month}'  

					)
	                AND NOT EXISTS 	(SELECT 1 
	                                    FROM SchCPSEFormSignaturesHeader b
	                                   WHERE a.Id = b.MandateId  
							       	   	AND b.Year =  '{$Year}'
							       	  	AND b.Month =  '{$Month}'  

	                        ) 
  

	                Order By b.LastName, b.FirstName
	                  ";



	}	

	/*======== Sel CPSE Forms Awaiting SUPERVISOR Supervision ========*/
	/*=====================================================================*/
	if ($CPSEFormsStatus == '1')  {

		$query = " SELECT    
                          a.Id as MandateId,
                  a.ServiceTypeId, 
                  c.ServiceTypeDesc,  
                  a.SessionLength,
                  a.SessionGrpSize,
                  a.SessionFrequency,
                  a.StudentId,
                  a.PlaceOfService,
                  CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                  (SELECT COUNT(*) FROM WeeklyServices d 
                      WHERE d.MandateId = a.Id 
                      AND d.ScheduleStatusId = 8
                      AND YEAR(d.ServiceDate) =  d.Year
                      AND MONTH(d.ServiceDate) =  d.Month

              ) as NumbeOfSessions,
              d.StatusId as CPSEHeaderSignatuteStatus,
              d.Id as CPSEHeaderSignatuteId,
              d.Year as FormYear,
              d.Month as FormMonth


          FROM SchStudentMandates a, 
               SchStudents b, 
               SchServiceTypes c,
               SchCPSEFormSignaturesHeader d,
               SchRegistrantSupervision h 
          WHERE a.Id = d.MandateId  
          AND d.StatusId = '1'
          AND a.StudentId = b.Id
          AND a.ServiceTypeId = c.Id
          and a.RegistrantId = h.RegistrantId
          and h.SupervisorId = '{$RegistrantId}'   
 
          Order By b.LastName, b.FirstName
	                  ";



	}	


	/*======== Sel CPSE Forms Awaiting PARENT Signatures ========*/
	/*=====================================================================*/
	if ($CPSEFormsStatus == '2')  {

		$query = " SELECT a.Id as MandateId,   
			              a.RegistrantId,
                          d.Year as FormYear,
                          d.Month as FormMonth,
		                  a.ServiceTypeId, 
		                  c.ServiceTypeDesc,  
		                  a.SessionLength,
		                  a.SessionGrpSize,
		                  a.SessionFrequency,
		                  a.StudentId,
		                  a.PlaceOfService,
		                  CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
		                  (SELECT COUNT(*) FROM WeeklyServices d 
		                      WHERE d.MandateId = a.Id 
		                      AND d.ScheduleStatusId = 8
		                      AND YEAR(d.ServiceDate) =  d.Year
		                      AND MONTH(d.ServiceDate) =  d.Month

		              ) as NumbeOfSessions,
		              d.StatusId as CPSEHeaderSignatuteStatus,
		              d.Id as CPSEHeaderSignatuteId


		          FROM SchStudentMandates a, 
		               SchStudents b, 
		               SchServiceTypes c,
		               SchCPSEFormSignaturesHeader d 
		          WHERE a.Id = d.MandateId  
		          AND d.StatusId = '2'
		          AND a.StudentId = b.Id
		          AND a.ServiceTypeId = c.Id
		          AND StudentTypeId = '2'
		            
		 
		                  Order By b.LastName, b.FirstName
	                  ";


	}	

	/*======== Sel CPSE Forms Awaiting SCHOOL PRINCIPAL Signatures ========*/
	/*=====================================================================*/
	if ($CPSEFormsStatus == '4')  {

		$query = " SELECT a.Id as MandateId,   
			              a.RegistrantId,
                          d.Year as FormYear,
                          d.Month as FormMonth,
		                  a.ServiceTypeId, 
		                  c.ServiceTypeDesc,  
		                  a.SessionLength,
		                  a.SessionGrpSize,
		                  a.SessionFrequency,
		                  a.StudentId,
		                  a.PlaceOfService,
		                  CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
		                  (SELECT COUNT(*) FROM WeeklyServices d 
		                      WHERE d.MandateId = a.Id 
		                      AND d.ScheduleStatusId = 8
		                      AND YEAR(d.ServiceDate) =  d.Year
		                      AND MONTH(d.ServiceDate) =  d.Month

		              ) as NumbeOfSessions,
		              d.StatusId as CPSEHeaderSignatuteStatus,
		              d.Id as CPSEHeaderSignatuteId


		          FROM SchStudentMandates a, 
		               SchStudents b, 
		               SchServiceTypes c,
		               SchCPSEFormSignaturesHeader d 
		          WHERE a.Id = d.MandateId  
		          AND d.StatusId = '4'
		          AND a.StudentId = b.Id
		          AND a.ServiceTypeId = c.Id  
		        /*  AND a.SchoolId =  '{$SchoolId}' */ 
		          AND '{$SchoolId}' = 4154
		          AND StudentTypeId = '2'
		            
		 
		                  Order By b.LastName, b.FirstName
	                  ";

	}	


	/*======== Sel CPSE Forms Fully ========*/
	/*=====================================================================*/
	if ($CPSEFormsStatus == '5')  {

		$query = " SELECT a.Id as MandateId,   
			              a.RegistrantId,
                          d.Year as FormYear,
                          d.Month as FormMonth,
		                  a.ServiceTypeId, 
		                  c.ServiceTypeDesc,  
		                  a.SessionLength,
		                  a.SessionGrpSize,
		                  a.SessionFrequency,
		                  a.StudentId,
		                  a.PlaceOfService,
		                  CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
		                  (SELECT COUNT(*) FROM WeeklyServices d 
		                      WHERE d.MandateId = a.Id 
		                      AND d.ScheduleStatusId = 8
		                      AND YEAR(d.ServiceDate) =  d.Year
		                      AND MONTH(d.ServiceDate) =  d.Month

		              ) as NumbeOfSessions,
		              d.StatusId as CPSEHeaderSignatuteStatus,
		              d.Id as CPSEHeaderSignatuteId


		          FROM SchStudentMandates a, 
		               SchStudents b, 
		               SchServiceTypes c,
		               SchCPSEFormSignaturesHeader d 
		          WHERE a.Id = d.MandateId  
		          AND d.StatusId = '2'
		          AND a.StudentId = b.Id
		          AND a.ServiceTypeId = c.Id
		    /*      AND '{$SchoolId}' = 4154 */
		          AND StudentTypeId = '2'
		            
		 
		                  Order By b.LastName, b.FirstName
	                  ";



	}	


	$ret =  getData ($conn, $query);   			
	setDisConn($conn);
	echo $ret;
 

?>
 
 