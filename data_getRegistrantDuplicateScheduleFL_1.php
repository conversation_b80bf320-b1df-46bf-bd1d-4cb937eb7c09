<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$RegistrantId = $_GET['RegistrantId'];
	$ServiceDate = $_GET['ServiceDate'];
	$StartTime = $_GET['StartTime'];
	

	$result = $rcr_transaction->getRegistrantDuplicateScheduleFL_1(	$RegistrantId, 
																	$ServiceDate,
																	$StartTime);

	var_dump($result);

	$rcr_transaction->disconnectDB (); 

	//echo  "{ success: true,  data: ".json_encode($result)."}";

?>
	