<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$SchoolSeasonId = $_GET['SchoolSeasonId'];  

	$query = "SELECT  DISTINCT	a.Id as MandateId,
								a.ServiceTypeId, 
							   	c.<PERSON>TypeDesc,	
						     
								CONCAT( a.SessionFrequency , ' X ', a.<PERSON>th , ' X ', a.SessionGrpSize ) AS MandateDesc,
						       	
						       	a.StudentId,
						       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
								b.LastName,
								 b.FirstName,
								b.SearchId as StudentSearchId,
								b.ExtId,
								a.SchoolId, 
								
								CASE a.SchoolId 
								WHEN '0' THEN 'School Undefined'
								ELSE (SELECT CONCAT(TRIM(m.SchoolName),' (',DistrictName,') ') 
											FROM    SchSchools f,
													SchDistricts d,
													SchSubSchools m
													WHERE a.SchoolId = f.Id  
													AND   f.DistrictId = d.Id	
													AND   a.SchoolId = m.SchoolId 
													AND   b.SubSchoolTypeId = m.SubSchoolTypeId
											) 
								END AS SchoolName,
								
								CASE a.SchoolId 
								WHEN '0' THEN 'School Undefined'
								ELSE (SELECT CONCAT(TRIM(f.SchoolName),' (',DistrictName,') ') 
											FROM    SchSchools f,
													SchDistricts d
													 
													WHERE a.SchoolId = f.Id  
													AND   f.DistrictId = d.Id	
													 
												 
											) 
								END AS SchoolNameDBN 
								

						FROM 	SchStudentMandates a, 
								SchStudents b, 
								SchServiceTypes c,
								SchSchoolYear d
						WHERE a.RegistrantId = '{$RegistrantId}'  
						AND a.StatusId = '1'
						AND a.StudentId = b.Id
						AND a.ServiceTypeId = c.Id
						AND d.Id = '{$SchoolSeasonId}'
						AND StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate
						

	                    Order By b.LastName, b.FirstName ";


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  



 
?>
