<?php
require_once('DB.php');

/*===========================================================
// Class - data_handler*/
class dataHandler {
		 

        var $connection;

	   		function  dataHandler (){
            
			 	include('db_login.php');
					
                $this->connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
                if (DB::isError($this->connection)){
                    $this->connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
                }
				
				if (DB::isError($this->connection)){
                           die("Could not connect to the database: <br />".DB::errorMessage($this->connection));
                } 
 
            }  
 
			/* Close Open Connection
            //=======================*/			
			function disconnectDB () {
                          $this->connection->disconnect();
            }	


			/* Get Districts Listing
            //======================= */			
			function getDistricts() {
                        $query = "SELECT Id as id, 
										Id, 
										DistrictName,
										ExtId as BillingClientCode
							FROM SchDistricts 
					order by DistrictSort  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Selected Client's General Info */
            /*======================= */			
			function getSelDistrict($DistrictId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										ExtId,
										DistrictStatusId,
										DistrictName,
										BoroughId,
										StreetAddress1,
										StreetAddress2,
										City,
										State,
										ZipCode,
										LiaisonFirstName,
										LiaisonLastName,
										OfficePhone,
										MobilePhone,
										Fax,
										Email,
										Comments,
										UserId,
										TransDate
									FROM SchDistricts a  
										where Id = '{$DistrictId}'  "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Set Selected District's General Info */
            /*======================= */			
			function setSelDistrict(  	$Id,
										$ExtId,
										$DistrictStatusId,
										$DistrictName,
										$BoroughId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$DistrictSort,
										$UserId )  
			{
	
				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchDistricts
								set ExtId =  '{$ExtId}', 
								DistrictStatusId =  '{$DistrictStatusId}',
								DistrictName =  '{$DistrictName}',
								BoroughId =  '{$BoroughId}',
								StreetAddress1	 =  '{$StreetAddress1}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								LiaisonFirstName =  '{$LiaisonFirstName}',
								LiaisonLastName	 =  '{$LiaisonLastName}',
								OfficePhone	 =  '{$OfficePhone}',
								MobilePhone	 =  '{$MobilePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Email	 =  '{$Email}',
								Comments =  '{$Comments}',
								DistrictSort = '{$DistrictSort}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchDistricts 
								(ExtId, 
								DistrictStatusId,
								DistrictName,
								BoroughId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								DistrictSort,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$DistrictStatusId}',
						'{$DistrictName}',
						'{$BoroughId}',  
						'{$StreetAddress1}',  
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$LiaisonFirstName}',
						'{$LiaisonLastName}',
						'{$OfficePhone}', 
						'{$MobilePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Comments}',
						'{$DistrictSort}',
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						
			}	
			
			
			
			/* Get District Site Navigation Data
            //=======================	*/		
			function getDistrictSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchDistrictSideNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Boroughs Listing
            //=======================	*/		
			function getBoroughs($State) {
			
                        $query = "SELECT 	Id as id, 
											Id as BoroughId, 	
											BoroughName		
								FROM  SchBoroughs 
								Where State = '{$State}'
							Order by BoroughName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get District Messages
            //======================= */			
		 	function getDistrictMessages($DistrictId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchDistrictMessages a, Users b  
								WHERE DistrictId = '{$DistrictId}'
								AND a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	
			
			/* Set District's Message */
            /*======================= */			
			function setDistrictMessages($DistrictId,
										$HighPriority,
										$Msg,
										$UserId )  
			{
				
                       $query ="Insert into SchDistrictMessages 
								(DistrictId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$DistrictId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						/*return $result;*/ 
						return $query;
						
			}	
			
			/* Get District's Services Details Info */
            /*======================= */			
			function getSchDistrictServiceDetails ($DistrictId) {
			
                        $query ="SELECT a.Id as id,
										DistrictId, 
										ServiceTypeDesc,   
										ServiceTypeId, 
										PriorityFL, 
										PayRate,
										BillRate,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate 
									FROM SchDistrictServiceTypes a,
										 SchServiceTypes b,
										 Users c
								WHERE DistrictId = '{$DistrictId}'
								AND a.UserId = c.UserId
								AND ServiceTypeId = b.Id ";  	
  
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Set District's Services Details Info */
            /*======================= */			
			function setSchDistrictServiceDetails(	$ServiceId,
													$PriorityFL,
													$PayRate,
													$BillRate,
													$UserId ) {
			
                        $query =" Update SchDistrictServiceTypes
									Set PriorityFL =  '{$PriorityFL}', 
										PayRate =  '{$PayRate}',
										BillRate =  '{$BillRate}',
										UserId =  '{$UserId}',
										TransDate = NOW()
									where Id = '{$ServiceId}'";  	
  
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get District's School Contracts Info */
            /*======================= */			
			function getDistrictSchoolContracts($DistrictId) {
			
                        $query ="SELECT a.Id as id,
										DistrictId, 
										SchoolContractName,   
										BillingCode, 
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate 
									FROM SchDistrictSchoolContracts a,
										 Users c
								WHERE DistrictId = '{$DistrictId}'
								AND a.UserId = c.UserId ";
								 
  
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Set District's School services Contract Info */
            /*======================= */			
			function setDistrictSchoolContracts(	$DistrictContractId,
													$BillingCode,
													$UserId ) {
			
                        $query =" Update SchDistrictSchoolContracts
									Set BillingCode =  '{$BillingCode}', 
										UserId =  '{$UserId}',
										TransDate = NOW()
									where Id = '{$DistrictContractId}'";  	
  
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			
			/* Get Schools Listing
            //======================= */			
			function getSchools($DistrictId) {
                        $query = "SELECT Id as id, 
										Id, 
										SchoolName,
										SUBSTRING_INDEX(ExtId, '#', 1 ) as BillingClientArea,  
										SUBSTRING_INDEX(ExtId, '#', -1 ) as BillingSchoolId
							FROM SchSchools
                               Where DistrictId = '{$DistrictId}'   							
					order by SchoolName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get School Types Listing
            //=======================	*/		
			function getSchoolTypes() {
			
                        $query = "SELECT Id as id, 
										Id as SchoolTypeId,
										SchoolTypeDesc 
										FROM SchSchoolTypes 
								Order by Id   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get School Serivce Types Listing
            //=======================	*/		
			function getServiceTypes() {
			
                        $query = "SELECT Id as id, 
										Id as ServiceTypeId,
										ServiceTypeDesc,
										RegistrantTypeId,
										StudentRequiredFL
										FROM SchServiceTypes 
								Order by SortOrder   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get School Serivce Types By Registrant Type Listing
            //=======================	*/		
			function getServiceTypesByRegistrantType($RegistrantTypeId) {
			
                        $query = "SELECT Id as id, 
										Id as ServiceTypeId,
										ServiceTypeDesc,
										RegistrantTypeId,
										StudentRequiredFL
									FROM SchServiceTypes 
									Where RegistrantTypeId = '{$RegistrantTypeId}' 
										Order by SortOrder   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get School Site Navigation Data
            //=======================	*/		
			function getSchoolSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolSideNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Selected School's General Info */
            /*======================= */			
			function getSelSchool($SchoolId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										ExtId,
										SchoolStatusId,
										SchoolName,
										SchoolTypeId,
										SchoolTypeDesc,
										DistrictId,
										StreetAddress1,
										StreetAddress2,
										City,
										State,
										ZipCode,
										LiaisonFirstName,
										LiaisonLastName,
										OfficePhone,
										MobilePhone,
										Fax,
										Email,
										Comments,
										UserId,
										TransDate
									FROM SchSchools a, SchSchoolTypes b  
										where a.Id = '{$SchoolId}' 
										AND SchoolTypeId = b.Id "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Set Selected School's General Info */
            /*======================= */			
			function setSelSchool(  	$Id,
										$ExtId,
										$DistrictId,
										$SchoolStatusId,
										$SchoolName,
										$SchoolTypeId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$UserId )  
			{
	
				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchools
								set ExtId =  '{$ExtId}', 
								DistrictId =  '{$DistrictId}',
								SchoolStatusId =  '{$SchoolStatusId}',
								SchoolName =  '{$SchoolName}',
								SchoolTypeId =  '{$SchoolTypeId}',
								StreetAddress1	 =  '{$StreetAddress1}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								LiaisonFirstName =  '{$LiaisonFirstName}',
								LiaisonLastName	 =  '{$LiaisonLastName}',
								OfficePhone	 =  '{$OfficePhone}',
								MobilePhone	 =  '{$MobilePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Email	 =  '{$Email}',
								Comments =  '{$Comments}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchSchools 
								(ExtId, 
								DistrictId,
								SchoolStatusId,
								SchoolName,
								SchoolTypeId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$DistrictId}',
						'{$SchoolStatusId}',
						'{$SchoolName}',
						'{$SchoolTypeId}',  
						'{$StreetAddress1}',  
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$LiaisonFirstName}',
						'{$LiaisonLastName}',
						'{$OfficePhone}', 
						'{$MobilePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Comments}',
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result; 
						
			}				
			
			/* Get School Messages
            //======================= */			
		 	function getSchoolMessages($SchoolId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchSchoolMessages a, Users b  
								WHERE SchoolId = '{$SchoolId}'
								AND a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	
			
			/* Set School's Message */
            /*======================= */			
			function setSchoolMessages($SchoolId,
										$HighPriority,
										$Msg,
										$UserId )  
			{
				
                       $query ="Insert into SchSchoolMessages 
								(SchoolId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$SchoolId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						/*return $result;*/ 
						return $query;
						
			}	

			/* Get School Services Comment Info */
            /*======================= */			
			function getSchoolServicesDetails($SchoolId) {
			
                        $query ="SELECT a.Id as id, 
										a.SchoolId , 
										a.ServiceTypeId , 
										ServiceTypeDesc, 
                                        PriorityFL, 
										StudentRequiredFL,
										ServiceComment , 
								CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
								FROM SchSchoolServicesDetails a,
									SchServiceTypes b,
									Users c,
                                    SchDistrictServiceTypes d,
                                    SchSchools f 
								WHERE a.SchoolId = '{$SchoolId}'
								AND a.UserId = c.UserId 
								AND a.ServiceTypeId = b.Id 
                                AND a.SchoolId = f.Id 
								AND f.DistrictId = d.DistrictId 
                                AND a.ServiceTypeId = d.ServiceTypeId   ";
  

    	
								    
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Set School Services Comment Info */
            /*======================= */			
			function setSchoolServicesDetails($ServiceDetailsId, $ServiceComment, $UserId) {
			
                        $query ="Update SchSchoolServicesDetails
									set ServiceComment =  '{$ServiceComment}', 
										UserId =  '{$UserId}',
										TransDate = NOW()
							where Id = '{$ServiceDetailsId}'   ";
  
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}
			
			/* Get School Registrants Listing
            //======================= */			
			function getSchoolRegistrants($Statuses) {
                        $query = "SELECT a.Id as id,  
										TypeId,	
										CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										SearchId
							FROM SchSchoolRegistrants a, SchSchoolRegistrantTypes b
							WHERE Typeid  = b.Id 
							AND StatusID in {$Statuses}
							order by LastName, FirstName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get School Registrant Site Navigation Data
            //=======================	*/		
			function getSchoolRegistrantSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolRegistrantSideNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get Selected School Registrant's General Info */
            /*======================= */			
			function getSelSchoolRegistrant($RegistrantId) {
			
                        $query ="SELECT  Id as id, 
					                    Id,
										SearchId,
										StatusId,
										ExtId,
										TerminationType,
										TerminationReason,
										TypeId,
										FirstName,
										LastName,
										CONCAT( trim( LastName) , ', ', trim(FirstName)) as RegistrantName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										Fax,
										Email,
										CASE Email 
											WHEN '' THEN 'No'
										ELSE 'Yes'
										END AS HasEmail,
										
										CASE NextDayPay 
											WHEN '0' THEN 'No'
										ELSE 'Yes'
										END AS NextDayPayFlag,

										Availability,
										HospitalExp,
										Shifts,
										DATE_FORMAT( LastPayDate, '%m-%d-%Y' ) as LastPayDate,
                                        CASE COALESCE(LastPayDate, 0)  
                                            WHEN '0' THEN ''
                                            ELSE DATE_FORMAT( LastPayDate, '%m-%d-%Y' )
                                        END as LastPayDate,
										PerDiem,
										ThrnWeekContract,
										NewGraduate,
										ForeignTrained,
										NextDayPay,
										UserId,
										TransDate,
										(SELECT  RegistrantStatusDesc from RegistrantStatuses f
                  								where StatusId = f.Id ) as RegistrantStatusDesc,
												
										(SELECT count(*) from SchSchoolRegistrantCredItems d
                  								where a.Id = d.RegistrantId
												AND ComplianceLevelId = 1 ) as CredItemsNonCompliant,    
												
										(SELECT count(*) from SchSchoolRegistrantCredItems d
                  								where a.Id = d.RegistrantId
												AND ComplianceLevelId != 1 ) as CredItemsCompliant,

										(SELECT count(*) from SchSchoolRegistrantCredItems d
                  								where a.Id = d.RegistrantId
												AND ComplianceLevelId != 1
												AND DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30) as CredItemsExp30Days,
												
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END as SpecialtiesList
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  c.RegistrantId = a.Id) as SpecialtiesList,
										(Select RegistrantTypeDesc from SchSchoolRegistrantTypes
											where Typeid  = SchSchoolRegistrantTypes.Id) as  RegistrantTypeDesc
									FROM SchSchoolRegistrants a
								  where Id = '{$RegistrantId}' "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Set Selected School Registrants's general Info
            //=======================	*/		
			function setSelSchoolRegistrant(  	$Id,
										$SearchId,
										$StatusId,	
										$ExtId,
										$TerminationType,
										$TerminationReason,					
										$TypeId,
										$FirstName,
										$LastName,
										$MiddleInitial,
										$StreetAddress1,
										$StreetAddress2,
										$City,
										$State,
										$ZipCode,
										$MobilePhone,
										$HomePhone,
										$Fax,
										$Email,
										$Availability,
										$HospitalExp,
										$Shifts,
										$NextDayPay,
										$PerDiem,
										$ThrnWeekContract,
										$NewGraduate,
										$ForeignTrained,
										$UserId  )
			 
			
			{

				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchoolRegistrants 
								set StatusId =  '{$StatusId}', 
								TerminationType =  '{$TerminationType}',
								TerminationReason =  '{$TerminationReason}',
								TypeId =  '{$TypeId}',
								ExtId =  '{$ExtId}',
								FirstName =  '{$FirstName}',
								LastName	 =  '{$LastName}',
								MiddleInitial	 =  '{$MiddleInitial}',
								StreetAddress1	 =  '{$StreetAddress1}',
								StreetAddress2	 =  '{$StreetAddress2}',
								City	 =  '{$City}',
								State	 =  '{$State}',
								ZipCode	 =  '{$ZipCode}',
								MobilePhone	 =  '{$MobilePhone}',
								HomePhone	 =  '{$HomePhone}',
								Fax	 =  '{$Fax}',
								Email	 =  '{$Email}',
								Availability = '{$Availability}',
								HospitalExp = '{$HospitalExp}',
								Shifts = '{$Shifts}',
								PerDiem = '{$PerDiem}',
								NextDayPay = '{$NextDayPay}',
								ThrnWeekContract = '{$ThrnWeekContract}',
								NewGraduate = '{$NewGraduate}',
								ForeignTrained = '{$ForeignTrained}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchSchoolRegistrants  
								(StatusId, 
                                SearchId, 		
                                ExtId,								
								TerminationType,
								TerminationReason,
								TypeId,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								Fax,
								Email,
								Availability,
								HospitalExp,
								Shifts,
								NextDayPay,
								PerDiem,
								ThrnWeekContract,
								NewGraduate,
								ForeignTrained,
								UserId,
								TransDate )
				values 	('{$StatusId}',  
						'{$SearchId}',	
						'{$ExtId}',
						'{$TerminationType}',
						'{$TerminationReason}',
						'{$TypeId}',  
						'{$FirstName}',  
						'{$LastName}',  
						'{$MiddleInitial}',  
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}', 
						'{$State}', 
						'{$ZipCode}', 
						'{$MobilePhone}', 
						'{$HomePhone}', 
						'{$Fax}', 
						'{$Email}',
						'{$Availability}',
						'{$HospitalExp}',
						'{$Shifts}',
						'{$NextDayPay}',
						'{$PerDiem}',
						'{$ThrnWeekContract}',
						'{$NewGraduate}',
						'{$ForeignTrained}',
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
						
			}

			/* Get School Registrant Messages
            //======================= */			
		 	function getSchoolRegistrantMessages($RegistrantId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchSchoolRegistrantMessages a, Users b  
								WHERE RegistrantId = '{$RegistrantId}'
								AND a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	
			
			/* Set School Registrant's Message */
            /*======================= */			
			function setSchoolRegistrantMessages($RegistrantId,
										$HighPriority,
										$Msg,
										$UserId )  
			{
				
                       $query ="Insert into SchSchoolRegistrantMessages 
								(RegistrantId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$RegistrantId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						/*return $result;*/ 
						return $query;
						
			}	
			
			/* Get School Registrant Credentialing Items  
            //=======================	*/		
			function getSchoolRegistrantCredItems($RegistrantId) {
			
                        $query ="SELECT a.Id as RegistrantCredItemTransId,
										RegistrantId,
										a.CredItemId,
										CredItemType,

										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										CredItem,
										a.CredItemStatus,
										CredItemStatusColor,
										CredItemStatusBGColor,
										CredItemStatusDesc,
										ComplianceLevelId,
										'' as CondItemSelId,
										''as CredItemCondGrpId,
										
										CASE  ComplianceLevelId 
											WHEN '1' THEN 'Non-Compliant'
										ELSE 'Compliant'
										END AS ComplianceLevelDesc,
										
										COALESCE(b.Comments,'') as Comments,
										COALESCE(a.Results,'') as Results,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										COALESCE(DATE_FORMAT( a.OrigDocumentDate, '%m-%d-%Y' ),'')  as OrigDocumentDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	SchSchoolRegistrantCredItems a,
									CredentialingItems b,
									CredentialingItemStatuses g,
									Users e 
						WHERE 	a.CredItemId = b.Id 
								AND a.UserId = e.UserId
								AND a.CredItemStatus = g.CredItemStatus
								AND RegistrantId = '{$RegistrantId}'
								AND CredItemType < '3' 
							 
								UNION
							SELECT 		a.Id as RegistrantCredItemTransId,
										RegistrantId, 
										a.CredItemId, 
										CredItemType,
										'Conditional (Needs Renewal)' AS CredItemTypeDesc,
										c.CredItem, 
										a.CredItemStatus, 
										CredItemStatusColor,
										CredItemStatusBGColor,
										CredItemStatusDesc,
										
										ComplianceLevelId,
										CondItemSelId,
										b.CredItemCondGrpId,
										
										CASE  ComplianceLevelId 
											WHEN '1' THEN 'Non-Compliant'
										ELSE 'Compliant'
										END AS ComplianceLevelDesc,
										
										COALESCE( b.Comments, '' ) AS Comments, 
										COALESCE(a.Results,'') as Results,
										COALESCE( DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ) , '' ) AS ExpirationDate, 
										COALESCE( DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ) , '' ) AS PrevExpirationDate, 
										COALESCE(DATE_FORMAT( a.OrigDocumentDate, '%m-%d-%Y' ),'')  as OrigDocumentDate,
										
										CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate
								FROM 	SchSchoolRegistrantCredItems a,
										CredentialingItems b, 
										CredentialingItemCondGroupDetails c,
										CredentialingItemStatuses g,
										Users e
									WHERE 	a.CredItemId = b.Id
											AND a.UserId = e.UserId
											AND a.CredItemStatus = g.CredItemStatus
											AND b.CredItemCondGrpId = c.CredItemCondGrpId
											AND a.CondItemSelId= c.Id
											AND RegistrantId = '{$RegistrantId}'
											AND CredItemType = '3'
										ORDER BY CredItem	 	"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

          /* Set School Registrant Credentialing Items  
          //=======================	*/		
			function setSchoolRegistrantCredItems($RegistrantId,
													$CredItemId, 
													$CredItemStatus,
													$ComplianceLevelId,
													$OrigDocumentDate,
													$ExpirationDate,
													$Results,
													$UserId) {
			
                        $query ="Update SchSchoolRegistrantCredItems 
								Set CredItemStatus = '{$CredItemStatus}',
								    ComplianceLevelId = '{$ComplianceLevelId}', 
									PrevExpirationDate = ExpirationDate,
									OrigDocumentDate = '{$OrigDocumentDate}',
									ExpirationDate = '{$ExpirationDate}',
									Results = '{$Results}',
								    UserId = '{$UserId}',
									TransDate = now()
							WHERE 	RegistrantId = '{$RegistrantId}' 
									AND CredItemId = '{$CredItemId}' ";     
                                   	 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			/* Get Registrant Cred Item Messages
            //=======================	*/		
			function getSchoolRegistrantCredItemMsgs($RegistrantCredItemTransId) {
			
                        $query ="SELECT a.Id as id ,
						        a.Id as Id ,
								Msg,
								HighPriority,
								CommunicationModeId,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchSchoolRegistrantCredItemMessages a, Users b, CommunicationModes c  
								WHERE RegistrantCredItemTransId =  '{$RegistrantCredItemTransId}'	
								AND a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			/* Set School Registrant Cred Item Messages
            //=======================	*/		
			function setSchoolRegistrantCredItemMsgs($RegistrantCredItemTransId, 
									$Msg,
								    $HighPriority,	  
									$UserId)
			
			{ 
				 
                       $query ="Insert into SchSchoolRegistrantCredItemMessages ( RegistrantCredItemTransId,
															Msg,
															HighPriority,
															UserId,		
															TransDate )
							values  ('{$RegistrantCredItemTransId}',
									'{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}				
			
			/* Set Switch School Registrant Conditional Crededntialing Item
            //======================= */ 			
			function setSwitchSchoolRegistrantCondItem(	$RegistrantCredItemTransId, 
											$CredItemCondGrpId,
											$CondItemSelId,
											$UserId) {
			
 
							
                        $query = "Call proc_SwitchSchoolRegistrantCredItem ('{$RegistrantCredItemTransId}', '{$CredItemCondGrpId}', '{$CondItemSelId}', '{$UserId}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get Registrant Credentialing Items Email Info
            //============================================== */			
			function getEmailSchoolRegistrantCredItems ($RegistrantId) {
						
						$query = "Call  proc_SchoolRegistrantCredEmail ('{$RegistrantId}')  "; 
 						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);	
		
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query; 
						
			}	
			/* Set School Registrant Cred Item Email Messages
            //======================================== */			
			function setEmailSchoolRegistrantCredItemsMsg($RegistrantId, 
												$UserId) 	{ 
				 
                       $query ="Insert into RegistrantCredItemMessages ( RegistrantCredItemTransId,
																		Msg,
																		HighPriority,
																		UserId,		
																		TransDate )
		
													Select  Id,
															'Cred. Item related Email was sent',
															'0', 
															'{$UserId}',
															NOW() 
													From SchSchoolRegistrantCredItems
														Where RegistrantId = '{$RegistrantId}' 
														AND ComplianceLevelId = 1  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}	

			/* Get School's Days Off Info */
            /*======================= */			
			function getSchoolDaysOff($SchoolId) {
			
                        $query ="SELECT a.Id as id, 
										SchoolId, 
										StatusId,
										SchoolYearId, 
										Concat(CAST(StartYear as Char), ' - ', CAST(EndYear as Char)) as SchoolYearDesc, 
										StartDayOffDate as StartDayOffDateSort,
										DATE_FORMAT( StartDayOffDate, '%m-%d-%Y' ) as StartDayOffDate,
										DATE_FORMAT( EndDayOffDate, '%m-%d-%Y' ) as EndDayOffDate,
										DayOffDesc, 
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName, 
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
								   FROM SchSchoolsDaysOff a, SchSchoolYear b, Users c 
									WHERE SchoolId = '{$SchoolId}'
									AND a.UserId = c.UserId 
									AND SchoolYearId = b.Id 
                                   Order By StartDayOffDateSort ";
  							    
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			
			
			/* Set School's Days Off Info
            //=======================	*/		
			function setSchoolDaysOff(  $Id,
										$StatusId,
										$SchoolId,
										$SchoolYearId,
										$StartDayOffDate,
										$EndDayOffDate,
										$DayOffDesc,
										$UserId )
			
			{

				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchoolsDaysOff 
								set StatusId =  '{$StatusId}', 
								SchoolId =  '{$SchoolId}',
								SchoolYearId =  '{$SchoolYearId}',
								StartDayOffDate =  '{$StartDayOffDate}',
								EndDayOffDate =  '{$EndDayOffDate}',
								DayOffDesc =  '{$DayOffDesc}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchSchoolsDaysOff  
								(StatusId, 
								SchoolId,
								SchoolYearId,
								StartDayOffDate,
								EndDayOffDate,
								DayOffDesc,
								UserId,
								TransDate )
				values 	('{$StatusId}',  
						'{$SchoolId}',	
						'{$SchoolYearId}',
						'{$StartDayOffDate}',  
						'{$EndDayOffDate}',  
						'{$DayOffDesc}',  
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
						
			}	

			/* Get School Services Listing
            //======================= */			
			function getSchoolServices($SchoolId) {
                        $query = "SELECT a.Id as id, 
										ServiceTypeId,	
										StudentRequiredFL,
										ServiceTypeDesc
								FROM SchSchoolServicesDetails a,
									SchServiceTypes b
								WHERE a.SchoolId =  '{$SchoolId}'
								AND a.ServiceTypeId = b.Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Get School Assignment Listing
            //======================= */			
			function getSchoolAssignments($SchoolId) {
                        								
						$query = "Call proc_getSchoolAssignments ('{$SchoolId}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			

			/* Set School's Assignment 
            //======================= */			
			function setSchoolAssignments( 	$Id,
										  	$SchoolId,
											$ServiceTypeId,
											$StatusId,
											$AssignmentTypeId,	  
											$ConfirmationNumber,
											$StartDate,
											$EndDate,
											$RegistrantId,
											$MonFL,
											$MonStartTime,
											$MonEndTime,
											$MonHrs,
											$TueFL,
											$TueStartTime,
											$TueEndTime,
											$TueHrs,
											$WedFL,
											$WedStartTime,
											$WedEndTime,
											$WedHrs,
											$ThuFL,
											$ThuStartTime,
											$ThuEndTime,
											$ThuHrs,
											
											$FriFL,
											$FriStartTime,
											$FriEndTime,
											$FriHrs,
											
											$SatFL,
											$SatStartTime,
											$SatEndTime,
											$SatHrs,
											
											$SunFL,
											$SunStartTime,
											$SunEndTime,
											$SunHrs,

											$UserId	)			
				{
                if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchoolAssignments 
								set StatusId =  '{$StatusId}', 
								AssignmentTypeId =  '{$AssignmentTypeId}',
								ConfirmationNumber =  '{$ConfirmationNumber}',
								StartDate =  '{$StartDate}',
								EndDate =  '{$EndDate}',
								RegistrantId =  '{$RegistrantId}',
								MonFL =  '{$MonFL}',
								MonStartTime =  '{$MonStartTime}',
								MonEndTime =  '{$MonEndTime}',
								MonHrs =  '{$MonHrs}',
								TueFL =  '{$TueFL}',
								TueStartTime =  '{$TueStartTime}',
								TueEndTime =  '{$TueEndTime}',
								TueHrs =  '{$TueHrs}',
								WedFL =  '{$WedFL}',
								WedStartTime =  '{$WedStartTime}',
								WedEndTime =  '{$WedEndTime}',
								WedHrs =  '{$WedHrs}',
								ThuFL =  '{$ThuFL}',
								ThuStartTime =  '{$ThuStartTime}',
								ThuEndTime =  '{$ThuEndTime}',
								ThuHrs =  '{$ThuHrs}',
								
								FriFL =  '{$FriFL}',
								FriStartTime =  '{$FriStartTime}',
								FriEndTime =  '{$FriEndTime}',
								FriHrs =  '{$FriHrs}',
								
								SatFL =  '{$SatFL}',
								SatStartTime =  '{$SatStartTime}',
								SatEndTime =  '{$SatEndTime}',
								SatHrs =  '{$SatHrs}',

								SunFL =  '{$SunFL}',
								SunStartTime =  '{$SunStartTime}',
								SunEndTime =  '{$SunEndTime}',
								SunHrs =  '{$SunHrs}',
								
								UserId =  '{$UserId}',
								TransDate = NOW()
							where SchoolId = '{$SchoolId}' ";
                } else {
                       $query ="Insert into SchSchoolAssignments  
								(StatusId,
								SchoolId,
								ServiceTypeId,
								AssignmentTypeId,
								ConfirmationNumber,
								StartDate,
								EndDate,
								RegistrantId,
								MonFL,
								MonStartTime,
								MonEndTime,
								MonHrs,
								TueFL,
								TueStartTime,
								TueEndTime,
								TueHrs,
								WedFL,
								WedStartTime,
								WedEndTime,
								WedHrs,
								ThuFL,
								ThuStartTime,
								ThuEndTime,
								ThuHrs,
								
								FriFL,
								FriStartTime,
								FriEndTime,
								FriHrs,
								
								SatFL,
								SatStartTime,
								SatEndTime,
								SatHrs,
								
								SunFL,
								SunStartTime,
								SunEndTime,
								SunHrs,
								
								UserId,
								TransDate )
				values 	('{$StatusId}',  
						'{$SchoolId}',	
						'{$ServiceTypeId}',	
						'{$AssignmentTypeId}',	
						'{$ConfirmationNumber}',
						'{$StartDate}',  
						'{$EndDate}',  
						'{$RegistrantId}',
						'{$MonFL}',
						'{$MonStartTime}',
						'{$MonEndTime}',
						'{$MonHrs}',
						'{$TueFL}',
						'{$TueStartTime}',
						'{$TueEndTime}',
						'{$TueHrs}',
						'{$WedFL}',
						'{$WedStartTime}',
						'{$WedEndTime}',
						'{$WedHrs}',
						'{$ThuFL}',
						'{$ThuStartTime}',
						'{$ThuEndTime}',
						'{$ThuHrs}',
						
						'{$FriFL}',
						'{$FriStartTime}',
						'{$FriEndTime}',
						'{$FriHrs}',
						
						'{$SatFL}',
						'{$SatStartTime}',
						'{$SatEndTime}',
						'{$SatHrs}',
						
						'{$SunFL}',
						'{$SunStartTime}',
						'{$SunEndTime}',
						'{$SunHrs}',
						
						'{$UserId}',
						NOW()  ) ";    }
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}			

			/* Get Student Site Navigation Data
            //=======================	*/		
			function getStudentSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchStudentSideNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Selected Student's General Info */
            /*======================= */			
			function getSelStudent($StudentId) {
			
                        $query ="SELECT  Id as id, 
					                    Id,
										ExtId,
										SearchId,
										StatusId,
										DateOfBirth,
										FirstName,
										LastName,
										CONCAT( trim( LastName) , ', ', trim(FirstName)) as StudentName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										GuardianName,
										COALESCE(GuardianPhone,'') as GuardianPhone,
										GuardianEmail,
										MedicalNeeds,
										Comments,
										ReportGroupId,
										UserId
										
									FROM SchStudents
								  where Id = '{$StudentId}' "; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Get Students Listing
            //======================= */			
			function getStudents($Statuses) {
                        $query = "SELECT Id as id,  
										CONCAT( trim( LastName) , ', ', trim(FirstName) ) as StudentName,
										SearchId
							FROM SchStudents
							WHERE StatusID in {$Statuses}
							order by LastName, FirstName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	

			/* Update Selected Student's Info 
            //======================= */			
			function setSelStudent( 	$Id,
										$ExtId,
										$SearchId,
										$StatusId,
										$DateOfBirth,
										$FirstName,
										$LastName,
										$MiddleInitial,
										$StreetAddress1,
										$StreetAddress2,
										$City,
										$State,
										$ZipCode,
										$MobilePhone,
										$HomePhone,
										$GuardianName,
										$GuardianPhone,
										$GuardianEmail,
										$MedicalNeeds,
										$Comments,
										$UserId	)			
				{
                if(is_numeric($Id) ) { 	
			
                        $query ="Update SchStudents 
								set ExtId =  '{$ExtId}', 
								SearchId =  '{$SearchId}',
								StatusId =  '{$StatusId}',
								DateOfBirth =  '{$DateOfBirth}',
								FirstName =  '{$FirstName}',
								LastName =  '{$LastName}',
								MiddleInitial =  '{$MiddleInitial}',
								StreetAddress1 =  '{$StreetAddress1}',
								StreetAddress2 =  '{$StreetAddress2}',
								City =  '{$City}',
								State =  '{$State}',
								ZipCode =  '{$ZipCode}',
								MobilePhone =  '{$MobilePhone}',
								HomePhone =  '{$HomePhone}',
								GuardianName =  '{$GuardianName}',
								GuardianPhone =  '{$GuardianPhone}',
								GuardianEmail =  '{$GuardianEmail}',
								MedicalNeeds =  '{$MedicalNeeds}',
								Comments =  '{$Comments}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchStudents  
								(ExtId, 
								SearchId,
								StatusId,
								DateOfBirth,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								GuardianName,
								GuardianPhone,
								GuardianEmail,
								MedicalNeeds,
								Comments,
								UserId,
								TransDate )
				values 	('{$ExtId}',  
						'{$SearchId}',	
						'{$StatusId}',
						'{$DateOfBirth}',  
						'{$FirstName}',  
						'{$LastName}',
						'{$MiddleInitial}',
						'{$StreetAddress1}',
						'{$StreetAddress2}',
						'{$City}',
						'{$State}',
						'{$ZipCode}',
						'{$MobilePhone}',
						'{$HomePhone}',
						'{$GuardianName}',
						'{$GuardianPhone}',
						'{$GuardianEmail}',
						'{$MedicalNeeds}',
						'{$Comments}',
						'{$UserId}',
						NOW()  ) ";    }
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}					

			/* Get Student Messages
            //======================= */			
		 	function getStudentMessages($StudentId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchStudentMessages a, Users b  
								WHERE StudentId = '{$StudentId}'
								AND a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}	

			/* Set Student's Message */
            /*======================= */			
			function setStudentMessages($StudentId,
										$HighPriority,
										$Msg,
										$UserId )  
			{
				
                       $query ="Insert into SchStudentMessages 
								(StudentId, 
								HighPriority,
								Msg,
								UserId,
								TransDate )
				values 	('{$StudentId}',  
						'{$HighPriority}',
						'{$Msg}',
						'{$UserId}',
						NOW()  ) ";
								            

						
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						/*return $result;*/ 
						return $query;
						
			}		

			/* Get Student 1 to 1 Assignment's Info
            //======================= */			
			function getStudent1to1Assignments($StudentId, $ServiceTypeId) {
                        								
						$query = "Call proc_getStudent1to1Assignments ('{$StudentId}','{$ServiceTypeId}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		
			
			/* Set Student 1 to 1 Assignment's Info
            //======================= */			
			function setStudent1to1Assignments( 	$Id,
										  	$StudentId,
											$DistrictId,
											$SchoolId,
											$ServiceTypeId,
											$StatusId,
											$AssignmentTypeId,	  
											$ConfirmationNumber,
											$StartDate,
											$EndDate,
											$RegistrantId,
											$MonFL,
											$Mon1to1StartTime,
											$Mon1to1EndTime,
											$MonHrs,
											$TueFL,
											$Tue1to1StartTime,
											$Tue1to1EndTime,
											$TueHrs,
											$WedFL,
											$Wed1to1StartTime,
											$Wed1to1EndTime,
											$WedHrs,
											$ThuFL,
											$Thu1to1StartTime,
											$Thu1to1EndTime,
											$ThuHrs,
											$FriFL,
											$Fri1to1StartTime,
											$Fri1to1EndTime,
											$FriHrs,
											$UserId	)			
				{
                if(is_numeric($Id) ) { 	
			
                        $query ="Update SchStudent1to1Assignments 
								set StatusId =  '{$StatusId}', 
								DistrictId =  '{$DistrictId}',
								SchoolId =  '{$SchoolId}',
								AssignmentTypeId =  '{$AssignmentTypeId}',
								ConfirmationNumber =  '{$ConfirmationNumber}',
								StartDate =  '{$StartDate}',
								EndDate =  '{$EndDate}',
								RegistrantId =  '{$RegistrantId}',
								MonFL =  '{$MonFL}',
								Mon1to1StartTime =  '{$Mon1to1StartTime}',
								Mon1to1EndTime =  '{$Mon1to1EndTime}',
								MonHrs =  '{$MonHrs}',
								TueFL =  '{$TueFL}',
								Tue1to1StartTime =  '{$Tue1to1StartTime}',
								Tue1to1EndTime =  '{$Tue1to1EndTime}',
								TueHrs =  '{$TueHrs}',
								WedFL =  '{$WedFL}',
								Wed1to1StartTime =  '{$Wed1to1StartTime}',
								Wed1to1EndTime =  '{$Wed1to1EndTime}',
								WedHrs =  '{$WedHrs}',
								ThuFL =  '{$ThuFL}',
								Thu1to1StartTime =  '{$Thu1to1StartTime}',
								Thu1to1EndTime =  '{$Thu1to1EndTime}',
								ThuHrs =  '{$ThuHrs}',
								FriFL =  '{$FriFL}',
								Fri1to1StartTime =  '{$Fri1to1StartTime}',
								Fri1to1EndTime =  '{$Fri1to1EndTime}',
								FriHrs =  '{$FriHrs}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where StudentId = '{$StudentId}' ";
                } else {
                       $query ="Insert into SchStudent1to1Assignments  
								(StudentId,
								DistrictId,
								SchoolId,
								StatusId,
								ServiceTypeId,
								AssignmentTypeId,
								ConfirmationNumber,
								StartDate,
								EndDate,
								RegistrantId,
								MonFL,
								Mon1to1StartTime,
								Mon1to1EndTime,
								MonHrs,
								TueFL,
								Tue1to1StartTime,
								Tue1to1EndTime,
								TueHrs,
								WedFL,
								Wed1to1StartTime,
								Wed1to1EndTime,
								WedHrs,
								ThuFL,
								Thu1to1StartTime,
								Thu1to1EndTime,
								ThuHrs,
								FriFL,
								Fri1to1StartTime,
								Fri1to1EndTime,
								FriHrs,
								UserId,
								TransDate )
				values 	('{$StudentId}',
						'{$DistrictId}',
						'{$SchoolId}',
						'{$StatusId}',  
						'{$ServiceTypeId}',	
						'{$AssignmentTypeId}',	
						'{$ConfirmationNumber}',
						'{$StartDate}',  
						'{$EndDate}',  
						'{$RegistrantId}',
						'{$MonFL}',
						'{$Mon1to1StartTime}',
						'{$Mon1to1EndTime}',
						'{$MonHrs}',
						'{$TueFL}',
						'{$Tue1to1StartTime}',
						'{$Tue1to1EndTime}',
						'{$TueHrs}',
						'{$WedFL}',
						'{$Wed1to1StartTime}',
						'{$Wed1to1EndTime}',
						'{$WedHrs}',
						'{$ThuFL}',
						'{$Thu1to1StartTime}',
						'{$Thu1to1EndTime}',
						'{$ThuHrs}',
						'{$FriFL}',
						'{$Fri1to1StartTime}',
						'{$Fri1to1EndTime}',
						'{$FriHrs}',
						'{$UserId}',
						NOW()  ) ";    }
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}	
			
			
			/* Get Student Transportation Assignment's Info
            //======================= */			
			function getStudentTransportAssignments($StudentId) {
                        								
						$query = "Call proc_getStudentTransportAssignments ('{$StudentId}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		
			
			/* Set Student Transport Assignment's Info
            //======================= */			
			function setStudentTransportAssignments( 	$Id,
										  	$StudentId,
											$DistrictId,
											$SchoolId,
											$ServiceTypeId,
											$StatusId,
											$AssignmentTypeId,	  
											$ConfirmationNumber,
											$StartDate,
											$EndDate,
											$RegistrantId,

											$MonFL,
											$MonTrans1StartTime,
											$MonTrans1EndTime,
											$Mon1Hrs,
											$MonTrans2StartTime,
											$MonTrans2EndTime,
											$Mon2Hrs,
											
											$TueFL,
											$TueTrans1StartTime,
											$TueTrans1EndTime,
											$Tue1Hrs,
											$TueTrans2StartTime,
											$TueTrans2EndTime,
											$Tue2Hrs,
											
											$WedFL,
											$WedTrans1StartTime,
											$WedTrans1EndTime,
											$Wed1Hrs,
											$WedTrans2StartTime,
											$WedTrans2EndTime,
											$Wed2Hrs,
											
											$ThuFL,
											$ThuTrans1StartTime,
											$ThuTrans1EndTime,
											$Thu1Hrs,
											$ThuTrans2StartTime,
											$ThuTrans2EndTime,
											$Thu2Hrs,
											
											$FriFL,
											$FriTrans1StartTime,
											$FriTrans1EndTime,
											$Fri1Hrs,
											$FriTrans2StartTime,
											$FriTrans2EndTime,
											$Fri2Hrs,
											
											$UserId	)			
				{
                if(is_numeric($Id) ) { 	
			
                        $query ="Update SchStudentTransportAssignments 
								set StatusId =  '{$StatusId}', 
								DistrictId =  '{$DistrictId}',
								SchoolId =  '{$SchoolId}',
								AssignmentTypeId =  '{$AssignmentTypeId}',
								ConfirmationNumber =  '{$ConfirmationNumber}',
								StartDate =  '{$StartDate}',
								EndDate =  '{$EndDate}',
								RegistrantId =  '{$RegistrantId}',
								
								MonFL =  '{$MonFL}',
								MonTrans1StartTime =  '{$MonTrans1StartTime}',
								MonTrans1EndTime =  '{$MonTrans1EndTime}',
								Mon1Hrs =  '{$Mon1Hrs}',
								MonTrans2StartTime =  '{$MonTrans2StartTime}',
								MonTrans2EndTime =  '{$MonTrans2EndTime}',
								Mon2Hrs =  '{$Mon2Hrs}',
								
								TueFL =  '{$TueFL}',
								TueTrans1StartTime =  '{$TueTrans1StartTime}',
								TueTrans1EndTime =  '{$TueTrans1EndTime}',
								Tue1Hrs =  '{$Tue1Hrs}',
								TueTrans2StartTime =  '{$TueTrans2StartTime}',
								TueTrans2EndTime =  '{$TueTrans2EndTime}',
								Tue2Hrs =  '{$Tue2Hrs}',
								
								WedFL =  '{$WedFL}',
								WedTrans1StartTime =  '{$WedTrans1StartTime}',
								WedTrans1EndTime =  '{$WedTrans1EndTime}',
								Wed1Hrs =  '{$Wed1Hrs}',
								WedTrans2StartTime =  '{$WedTrans2StartTime}',
								WedTrans2EndTime =  '{$WedTrans2EndTime}',
								Wed2Hrs =  '{$Wed2Hrs}',
								
								ThuFL =  '{$ThuFL}',
								ThuTrans1StartTime =  '{$ThuTrans1StartTime}',
								ThuTrans1EndTime =  '{$ThuTrans1EndTime}',
								Thu1Hrs =  '{$Thu1Hrs}',
								ThuTrans2StartTime =  '{$ThuTrans2StartTime}',
								ThuTrans2EndTime =  '{$ThuTrans2EndTime}',
								Thu2Hrs =  '{$Thu2Hrs}',
								
								FriFL =  '{$FriFL}',
								FriTrans1StartTime =  '{$FriTrans1StartTime}',
								FriTrans1EndTime =  '{$FriTrans1EndTime}',
								Fri1Hrs =  '{$Fri1Hrs}',
								FriTrans2StartTime =  '{$FriTrans2StartTime}',
								FriTrans2EndTime =  '{$FriTrans2EndTime}',
								Fri2Hrs =  '{$Fri2Hrs}',	
							
								UserId =  '{$UserId}',
								TransDate = NOW()
							where StudentId = '{$StudentId}' ";
                } else {
                       $query ="Insert into SchStudentTransportAssignments  
								(StudentId,
								DistrictId,
								SchoolId,
								StatusId,
								ServiceTypeId,
								AssignmentTypeId,
								ConfirmationNumber,
								StartDate,
								EndDate,
								RegistrantId,
								
								MonFL,
								MonTrans1StartTime,
								MonTrans1EndTime,
								Mon1Hrs,
								MonTrans2StartTime,
								MonTrans2EndTime,
								Mon2Hrs,
								
								TueFL,
								TueTrans1StartTime,
								TueTrans1EndTime,
								Tue1Hrs,
								TueTrans2StartTime,
								TueTrans2EndTime,
								Tue2Hrs,
								
								WedFL,
								WedTrans1StartTime,
								WedTrans1EndTime,
								Wed1Hrs,
								WedTrans2StartTime,
								WedTrans2EndTime,
								Wed2Hrs,
								
								ThuFL,
								ThuTrans1StartTime,
								ThuTrans1EndTime,
								Thu1Hrs,
								ThuTrans2StartTime,
								ThuTrans2EndTime,
								Thu2Hrs,
								
								FriFL,
								FriTrans1StartTime,
								FriTrans1EndTime,
								Fri1Hrs,
								FriTrans2StartTime,
								FriTrans2EndTime,
								Fri2Hrs,
								
								UserId,
								TransDate )
				values 	('{$StudentId}',
						'{$DistrictId}',
						'{$SchoolId}',
						'{$StatusId}',  
						'{$ServiceTypeId}',	
						'{$AssignmentTypeId}',	
						'{$ConfirmationNumber}',
						'{$StartDate}',  
						'{$EndDate}',  
						'{$RegistrantId}',
						
						'{$MonFL}',
						'{$MonTrans1StartTime}',
						'{$MonTrans1EndTime}',
						'{$Mon1Hrs}',
						'{$MonTrans2StartTime}',
						'{$MonTrans2EndTime}',
						'{$Mon2Hrs}',
						
						'{$TueFL}',
						'{$TueTrans1StartTime}',
						'{$TueTrans1EndTime}',
						'{$Tue1Hrs}',
						'{$TueTrans2StartTime}',
						'{$TueTrans2EndTime}',
						'{$Tue2Hrs}',
						
						'{$WedFL}',
						'{$WedTrans1StartTime}',
						'{$WedTrans1EndTime}',
						'{$Wed1Hrs}',
						'{$WedTrans2StartTime}',
						'{$WedTrans2EndTime}',
						'{$Wed2Hrs}',
						
						'{$ThuFL}',
						'{$ThuTrans1StartTime}',
						'{$ThuTrans1EndTime}',
						'{$Thu1Hrs}',
						'{$ThuTrans2StartTime}',
						'{$ThuTrans2EndTime}',
						'{$Thu2Hrs}',
						
						'{$FriFL}',
						'{$FriTrans1StartTime}',
						'{$FriTrans1EndTime}',
						'{$Fri1Hrs}',
						'{$FriTrans2StartTime}',
						'{$FriTrans2EndTime}',
						'{$Fri2Hrs}',
						
						'{$UserId}',
						NOW()  ) ";    }
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}	
			
			/* Get School Registrant Weekly Availability Info
            //================================================ */			
			function getSchoolRegistrantWklyAvailability($RegistrantId, $PayrollWeek, $ServiceTypeId) {
                        								
						$query = "Call proc_getSchoolRegistrantWklyAvailability ('{$RegistrantId}' , '{$PayrollWeek}', '{$ServiceTypeId}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			/* Set Selected School Registrants's Availability Info
            //====================================================*/		
			function setSchoolRegistrantWklyAvailability(  	$Id,
															$RegistrantId,
															$PayrollWeek, 
															$ServiceDate,
															$ServiceTypeId,
															$AvailabilityFL,
															$Comments,
															$UserId  )
			
			{

				
				if(is_numeric($Id) ) { 	
			
                        $query ="Update SchSchoolRegistrantWeeklyAvailability 
								set AvailabilityFL =  '{$AvailabilityFL}', 
								Comments =  '{$Comments}',
								UserId =  '{$UserId}',
								TransDate = NOW()
							where Id = '{$Id}' ";
                } else {
                       $query ="Insert into SchSchoolRegistrantWeeklyAvailability  
								(RegistrantId, 
                                PayrollWeek, 								
								ServiceDate,
								ServiceTypeId,
								AvailabilityFL,
								Comments,
								UserId,
								TransDate )
				values 	('{$RegistrantId}',  
						'{$PayrollWeek}',	
						'{$ServiceDate}',
						'{$ServiceTypeId}',
						'{$AvailabilityFL}',
						'{$Comments}',  
						'{$UserId}',
						NOW()  ) ";
								            

				}		
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
						
			}			
			
			/* Get School's Weekly Services Info */
            /*======================= */			
			function getSchoolWklyServices(	$SchoolId,
											$PayrollWeek) {
			
                        $query ="SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										a.ServiceCoverageTypeId,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										AssignmentTypeId,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
										a.SchoolId,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
										Users f			
									WHERE a.SchoolId = '{$SchoolId}' 
									AND PayrollWeek = '{$PayrollWeek}'
									AND a.ServiceTypeId = 1 
									AND a.DistrictId = d.DistrictId
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
									AND a.UserId = f.UserId 
									Order By ServiceDateSort"; 
									
									
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		
			
			/* Get School Weekly Services Summary Info
            //================================================ */			
			function getSchoolWklyServicesSummary($SchoolId, $PayrollWeek) {
                        								
						$query = "Call proc_SchoolWklyServicesSummary ('{$SchoolId}' , '{$PayrollWeek}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		
			
			/* Get School Weekly Services Messages
            //=======================	*/		
		 	function getSchoolWklyServicesMsgs($ServiceId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
								
								WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDate1,
								a.TransDate as TransDate
							FROM SchSchoolWeeklyServicesMessages a, Users b  
								WHERE ServiceId = '{$ServiceId}' 
								AND a.UserId = b.UserId 
								Order By  TransDate1 Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;
			}			
			
			/* Get School Weekly Services Messages
            //=======================	*/		
			function setSchoolWklyServicesNewMsg($ServiceId, 
									$Msg,
								    $HighPriority,	  
									$UserId) 	{ 
				 
                       $query ="Insert into SchSchoolWeeklyServicesMessages( ServiceId,
																			Msg,
																			HighPriority,
																			UserId,		
																			TransDate )
							values  ('{$ServiceId}',
									'{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}		

			
			/* Get School Weekly Services Registrants Selection Info
            //================================================ */			
			function getSchoolWklyServicesRegistrantsSelection($SchoolId, 
															$RegistrantTypeId, 
															$ServiceDate) {
                        								
						$query = "Call proc_SchoolWklyServicesRegistrantsSelection ('{$SchoolId}' , '{$RegistrantTypeId}', '{$ServiceDate}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			
			// Set School Service Status
            //=======================			
			function setSchoolServiceStatus(
								$ServiceId,  
								$ServiceStatusId, 
								$RegistrantConfFL, 
								$SchoolConfFL, 
								$RegistrantId, 
								$PayRate,
								$FilledTransDate,
								$UserId	) {
			
                        $query = "update SchSchoolWeeklyServices
									set ServiceStatusId = '{$ServiceStatusId}',
										RegistrantConfFL = '{$RegistrantConfFL}',
										SchoolConfFL = '{$SchoolConfFL}',
										RegistrantId = '{$RegistrantId}',
										PayRate = '{$PayRate}',
										FilledTransDate = '{$FilledTransDate}',
										UserId = '{$UserId}',
										TransDate = now()
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $query;
						
			}

			// Set Assign Registrant to All Pending School Service for a Given Payroll Week/School 
            //=======================			
			function setSchoolServiceMultDays(
								$PayrollWeek,  
								$SchoolId,
								$ServiceStatusId, 
								$RegistrantConfFL, 
								$SchoolConfFL, 
								$RegistrantId, 
								$PayRate,
								$Msg,
								$UserId	) {
			
  						
						$query = "Call proc_setSchoolServiceMultDays ('{$PayrollWeek}' , 
																	  '{$SchoolId}', 
																	  '{$ServiceStatusId}'  , 
																	  '{$RegistrantId}',
																	  '{$RegistrantConfFL}',
																	  '{$SchoolConfFL}',
																	  '{$PayRate}',
																	  '{$Msg}',
																	  '{$UserId}' )  "; 
 
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $query;
						
			}
			// Set Cancelled By Registrant Info
            //=======================			
			function setCancelledByRegistrant($RegistrantId,
											$ServiceId, 
											$CancelReason,
											$UserId) 	{ 
				 
                       $query ="Insert into SchSchoolRegistrantCancelledServices (  RegistrantId,
																					ServiceId,
																					CancelReason,
																					Userid,
																					TransDate)
							values  ('{$RegistrantId}',
									'{$ServiceId}',  
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}			

			// Set Cancelled By School Info
            //=======================			
			function setCancelledBySchool(	$SchoolId,
											$ServiceId, 
											$CancelReason,
											$UserId) 	{ 
				 
                       $query ="Insert into SchSchoolCancelledServices (  SchoolId,
																		ServiceId,
																		CancelReason,
																		Userid,
																		TransDate)																		)
							values  ('{$SchoolId}',
									'{$ServiceId}',  
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";
				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}
			
			// Set School Service Status
            //=======================			
			function setSchoolService(
								$ServiceId,  
								$StartTime,
								$EndTime,
								$TotalHours,
								$WeekDay, 
								$ConfirmationNumber,
								$PayRate,
								$ReportComments,
	                            $UserId	) 
			{ 
                        $query = "update SchSchoolWeeklyServices 
						            set StartTime = '{$StartTime}', 
                                        EndTime = '{$EndTime}', 
                                        TotalHours = '{$TotalHours}', 
										WeekDay = '{$WeekDay}',
                                        ConfirmationNumber = '{$ConfirmationNumber}', 
										PayRate = '{$PayRate}',
										ReportComments = '{$ReportComments}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			// Set School Weekly Service(s) from Template
            //============================================			
			function setSchoolWklyServicesFromTemplate(	$PayrollWeek,
														$SchoolId,
														$DistrictId,
														$ServiceDate,
														$StartTime,
														$EndTime,
														$TotalHours,
														$WeekDay, 
														$ScheduleOrigBy,
														$PayRate,
														$ServiceCoverageTypeId,
														$UserId ) 
			{
					$query ="insert into SchSchoolWeeklyServices
							                  (	
												PayrollWeek,
												SchoolId,
												DistrictId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												ScheduleOrigBy,
												PayRate,
												ServiceCoverageTypeId,
												UserId,
												RequestTransDate, 
												TransDate )	
												
						       values (
										'{$PayrollWeek}',
										'{$SchoolId}',
										'{$DistrictId}',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
 										'{$ScheduleOrigBy}',
										'{$PayRate}',
										'{$ServiceCoverageTypeId}',
										'{$UserId}',
										now(),	
                                        now()	)";
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;		
			}

			/* Convert School Assignments into Schools Weekly Services 
            //================================================ */			
			function setSchoolAssigmentsToWklyServicesConversion(	$payroll_week, 
																	$mon,
																	$tue,
																	$wed,																		
																	$thu,
																	$fri) {
                        								
						$query = "Call proc_SchoolAssigmentsToWklyServicesConversion ('{$payroll_week}' , '{$mon}', '{$tue}', '{$wed}', '{$thu}', '{$fri}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}		

			// Get School's Restricted Registrants Info
            //=======================			
			function getSchoolRestRegistrants($SchoolId) {
			
                        $query ="SELECT a.RegistrantId as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RestrictType,
										CASE RestrictType
											WHEN '1' THEN 'Clinical'
											WHEN '0' THEN 'Non-Clinical'
										END AS RestrictTypeDesc,
										RestrictReason,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
										
							FROM 	SchSchoolRestrictedRegistrants a,
									SchSchoolRegistrants b, 
									Users c,
									SchSchoolRegistrantTypes f  
					where 	a.RegistrantId = b.Id 
						AND b.TypeId = f.Id
						AND a.UserId = c.UserId
						AND RestrictStatus = 1
						AND a.SchoolId = '{$SchoolId}' 
						 Order By b.LastName, b.FirstName  "; 
										 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			// Get School's nonRestricted Registrants Info
            //=======================			
			function getSchoolNonRestRegistrants($SchoolId) {
			
                        $query ="SELECT b.Id as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
							FROM 
								SchSchoolRegistrants b, SchSchoolRegistrantTypes f  
					where 
						  b.TypeId = f.Id
						and b.StatusId = 1
						and not exists (Select 1 from SchSchoolRestrictedRegistrants d
                                            Where b.Id = d.RegistrantId
                                                AND d.SchoolId = '{$SchoolId}' 
                                       		AND RestrictStatus = 1	)   
						 Order By b.LastName, b.FirstName  "; 
										 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}

			//  Update School's Restricted Registrants     
            //=======================			
			function setSchoolRestRegistrants($SchoolId,
											$RestRegistrants,			
											$UserId) {
					
 
                    // Delete All Existing Restricted Registrant for the Client
					//===============================================					
					$query ="Delete from  SchSchoolRestrictedRegistrants
								Where SchoolId = '{$SchoolId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }

					// Insert Newly Selected Restricted Registrant for the Client
					//===============================================					

					foreach ($RestRegistrants as $RegistrantId) {
								
							$Sel_Registrant = $RegistrantId['id'];
							
												
						$query ="Insert into SchSchoolRestrictedRegistrants
						   (SchoolId, RegistrantId, UserId, TransDate)   
						values ( '{$SchoolId}',  '{$Sel_Registrant}', '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}
			
			
			
			//  Remove  Schools's Restriction     
            //=============================================			
			function setRemoveSchoolRestRegistrant(	$RegistrantId,
													$SchoolId,	  
													$UserId) {
 												
						$query ="Update SchSchoolRestrictedRegistrants
						   Set RestrictStatus = 2,
							   UserId = '{$UserId}',
							   TransDate = now()	
						Where 	RegistrantId = '{$RegistrantId}'
						AND		SchoolId = 	'{$SchoolId}'
						AND		RestrictStatus = 1  ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
						}	
  						
				return $result;
			}	
			
			//  Update  School's Restriction Reason    
            //=============================================			
			function setSchoolRestRegistrantReason(	$RegistrantId,
													$SchoolId,	  
													$RestrictType,
													$RestrictReason,			
													$UserId) {
 												
						$query ="Insert into SchSchoolRestrictedRegistrants
						   (RegistrantId,  	SchoolId,  RestrictType,  RestrictReason, UserId, TransDate)   
						values ( '{$RegistrantId}',  '{$SchoolId}',  '{$RestrictType}', '{$RestrictReason}',   '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
						}	
  						
				return $result;
			}	
			
			/* Get Student's Weekly Services Info */
            /*======================= */			
			function getStudentWklyServices(	$StudentId,
											$PayrollWeek) {
			
                        $query ="SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										a.DistrictId,
										DistrictName,
										a.SchoolId,
										SchoolName,
										a.StudentId,
										a.ServiceTypeId,
										ServiceTypeDesc,
										PayrollWeek,
										a.PayRate,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ReportComments,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										StartTime as StartTimeSort,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
										SchDistricts g,
										SchSchools h,
										Users f			
									WHERE a.StudentId = '{$StudentId}' 
									AND PayrollWeek = '{$PayrollWeek}'
									AND a.DistrictId = d.DistrictId
									AND a.DistrictId = g.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
									
									AND a.SchoolId = h.Id	
									AND a.UserId = f.UserId 
									Order By ServiceDateSort, StartTimeSort "; 
									
									
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get Student Weekly Services Summary Info
            //================================================ */			
			function getStudentWklyServicesSummary($StudentId, $PayrollWeek) {
                        								
						$query = "Call proc_StudentWklyServicesSummary ('{$StudentId}' , '{$PayrollWeek}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			// Set Student (1 to 1) Weekly Service(s) from Template
            //============================================			
			function setStudentWklyServices1to1FromTemplate(	$PayrollWeek,
																$StudentId,
																$SchoolId,
																$DistrictId,
																$ServiceDate,
																$StartTime,
																$EndTime,
																$TotalHours,
																$WeekDay, 
																$ScheduleOrigBy,
																$ServiceTypeId,
																$PayRate,
																$UserId ) 
			{
					$query ="insert into SchSchoolWeeklyServices
							                  (	
												PayrollWeek,
												StudentId,
												SchoolId,
												DistrictId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												ScheduleOrigBy,
												ServiceTypeId,
												PayRate,
												UserId,
												RequestTransDate, 
												TransDate )	
												
						       values (
										'{$PayrollWeek}',
										'{$StudentId}',
										'{$SchoolId}',
										'{$DistrictId}',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
 										'{$ScheduleOrigBy}',
										'{$ServiceTypeId}',
										'{$PayRate}',
										'{$UserId}',
										now(),	
                                        now()	)";
					
					 	

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
				
				//return $result;  
				return $query;		
			}
			
			/* Get Student Weekly Services Messages
            //=======================	*/		
			function setStudentWklyServicesNewMsg($ServiceId, 
									$Msg,
								    $HighPriority,	  
									$UserId) 	{ 
				 
                       $query ="Insert into SchStudentWeeklyServicesMessages( ServiceId,
																			Msg,
																			HighPrsetCancelledBySchooliority,
																			UserId,		
																			TransDate )
							values  ('{$ServiceId}',
									'{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}	
			
			/* Get Student Weekly Services Registrants Selection Info
            //================================================ */			
			function getStudentWklyServicesRegistrantsSelection($StudentId, 
															$RegistrantTypeId, 
															$ServiceTypeId,
															$ServiceDate) {
                        								
						$query = "Call proc_StudentWklyServicesRegistrantsSelection ('{$StudentId}' , '{$RegistrantTypeId}', '{$ServiceTypeId}', '{$ServiceDate}' )  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}		

			// Set Cancelled By Student Info
            //=======================			
			function setCancelledByStudent(	$StudentId,
											$ServiceId, 
											$CancelReason,
											$UserId) 	{ 
				 
                       $query ="Insert into SchStudentCancelledServices (  StudentId,
																		ServiceId,
																		CancelReason,
																		Userid,
																		TransDate)																		)
							values  ('{$StudentId}',
									'{$ServiceId}',  
									'{$CancelReason}',
									'{$UserId}',
									 NOW() )  ";
				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						//return $query;  
	 		}
			
			/* Get Student Service Types Listing
            //======================= */			
			function getStudentServiceTypes() {
                        $query = "SELECT Id as id, 
										Id as ServiceTypeId,	
										StudentRequiredFL,
										RegistrantTypeId, 
										ServiceTypeDesc
								FROM SchServiceTypes  
                                WHERE StudentRequiredFL = 1 
								ORDER BY SortOrder";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}			
			
			/* Get Registrants by Type Listing
            //======================= */			
			function getSchoolRegistrantsByType($RegistrantTypeId) {
                        $query = "SELECT a.Id as id,  
										TypeId,	
										CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
							FROM SchSchoolRegistrants a, SchSchoolRegistrantTypes b
							WHERE Typeid  = b.Id 
							AND StatusID = '1'
                            AND TypeId = '{$RegistrantTypeId}'
							order by LastName, FirstName  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get School Board Site Navigation Data
            //=======================	*/		
			function getSchoolBoardSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolBoardSideNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Board Reports Data
            //===========================*/		
			function getSchoolBoardReports() {
			
                        $query = "SELECT Id as id,
										 ReportDesc,
										 ReportLink			
						FROM SchSchoolBoardReports 
					order by Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Daily Services (ALL) Data
            //===========================*/		
			function getSchoolDailyServicesAll($FromServiceDate, $ToServiceDate) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										COALESCE((SELECT Msg 
										     FROM SchSchoolWeeklyServicesMessages k
											WHERE k.ServiceId = a.id
											ORDER BY Id DESC
											LIMIT 0 , 1),'') as Msg,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                        SchSchoolTypes i, 
										Users f			
									WHERE ServiceDate between '{$FromServiceDate}' and '{$ToServiceDate}'
									 
									AND a.ServiceTypeId = 1 
                                                                        AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                                                        AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
									AND a.UserId = f.UserId 
									Order By DistrictName, ServiceDate, SchoolName";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Student Daily Services (ALL) Data
            //===========================*/		
			function getStudentDailyServicesAll($ServiceDate) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceTypeDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
                                        a.StudentId,
                                        CONCAT( trim( j.LastName) , ', ', trim(j.FirstName)) as StudentName,  
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                       	SchSchoolTypes i,
                                        SchStudents j,  
										Users f			
									WHERE ServiceDate = '{$ServiceDate}'
									AND a.ServiceTypeId != 1 
                                    AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
                                    AND StudentId = j.Id
									AND a.UserId = f.UserId 
									Order By j.LastName, j.FirstName, ServiceDateSort";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Daily Services Requiring Attention Data
            //===========================*/		
			function getSchoolDailyServicesReqAttention($ServiceDate) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                        SchSchoolTypes i, 
										Users f			
									WHERE ServiceDate = '{$ServiceDate}'
									 
									AND a.ServiceTypeId = 1 
                                    AND a.ServiceStatusId in (0,5,6)
									AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
									AND a.UserId = f.UserId 
									Order By DistrictName, SchoolName";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Student Daily Services Requiring Attention Data
            //===========================*/		
			function getStudentDailyServicesReqAttention($ServiceDate) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceTypeDesc,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
                                        a.StudentId,
                                        CONCAT( trim( j.LastName) , ', ', trim(j.FirstName)) as StudentName,  
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                       	SchSchoolTypes i,
                                        SchStudents j,  
										Users f			
									WHERE ServiceDate = '{$ServiceDate}'
									AND a.ServiceTypeId != 1 
									AND a.ServiceStatusId in (0,5,6)
                                    AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
                                    AND StudentId = j.Id
									AND a.UserId = f.UserId 
									Order By j.LastName, j.FirstName, ServiceDateSort";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Registrant Types Listing
            //======================= */			
			function getSchoolRegistrantTypes() {
                        $query = "SELECT Id as id, 
										Id as RegistrantTypeId, 
										RegistrantTypeDesc
							FROM SchSchoolRegistrantTypes 
					order by RegistrantTypeDesc  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get School Registrant Cred. Items Not Selected for a given Registrant Type Listing
            //================================================================================== */			
			function getSchoolRegistrantCredItemsNotSelected($RegistrantTypeId) {
                        $query = "SELECT a.Id AS id, CredItem
							FROM CredentialingItems a
						WHERE 	CredItemCategory = 1 
						AND NOT EXISTS (select * from SchSchoolRegistrantTypeCredItems  b
							where a.Id = b.CredItemId and
							RegistrantTypeId = '{$RegistrantTypeId}') 
						Order By CredItem   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			/* Get School Registrant Cred. Items Selected for a given Registrant Type Listing
            //================================================================================== */			
			function getSchoolRegistrantCredItemsSelected($RegistrantTypeId) {
                        $query = "SELECT CredItemId AS id, RegistrantTypeId, CredItem 
							FROM SchSchoolRegistrantTypeCredItems a, CredentialingItems b
						WHERE CredItemId = b.Id
						AND RegistrantTypeId = '{$RegistrantTypeId}'
						ORDER BY CredItem   ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			function setSchoolRegistrantCredItemsSelected($RegistrantTypeId,
										$CredItems,			
										$UserId) {
					
 
							
					//===============================================================	
                    // Delete All Existing Credentialing Items for a Group
					//===============================================================					
					$query ="Delete from  SchSchoolRegistrantTypeCredItems
								Where  	RegistrantTypeId = '{$RegistrantTypeId}'";
					 

					$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
					//======================================================	
					// Insert newaly Selected Credentialig Items for a Group
					//======================================================					

					foreach ($CredItems as $CredItemId) {
								
							$Sel_CredItem = $CredItemId['id'];
							
												
						$query ="Insert into SchSchoolRegistrantTypeCredItems
						   (RegistrantTypeId,  	CredItemId, UserId, TransDate)   
						values ( '{$RegistrantTypeId}',  '{$Sel_CredItem}', '{$UserId}',  now()  ) ";
		
						$result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
							
                        }
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	
			
			// Get Non-Compliant School Registrants Listing ALL
            //=======================			
			function getSchoolRegistrantsNonComplCredItems() 
			{
                        
						 $query = " SELECT  distinct
									a.RegistrantId,
									CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
									COALESCE((SELECT max(DATE_FORMAT( ServiceDate, '%m-%d-%Y' )) 
											from SchSchoolWeeklyServices g
											where a.RegistrantId = g.RegistrantId
										AND ServiceStatusId = 7 ),'') as LastServiceDate,
									(Select group_concat( CredItem SEPARATOR ', ' ) 
										FROM SchSchoolRegistrantCredItems k,  CredentialingItems h
										WHERE a.RegistrantId = k.RegistrantId
                                        AND  k.CredItemId = h.Id )  as CredItems,  
									CONCAT( trim( d.FirstName ) , ' ', trim( d.LastName ) ) AS UserName,
										a.TransDate
							FROM 	SchSchoolRegistrantCredItems a,
									CredentialingItems b,
									SchSchoolRegistrants c,
									SchSchoolRegistrantTypes f,
                                    Users d	  
								WHERE 	a.CredItemId = b.Id 
								AND a.RegistrantId = c.Id
								AND c.TypeId = f.Id
                                AND a.UserId = d.UserId   
								AND a.CredItemStatus in (1,3)
							Order by RegistrantName, CredItem  ";
								  
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}

			// Get School Registrant's Credentialing Items Expiring within 30 days Listing ALL
            //=======================			
			function getSchoolRegistrant30DaysExpCredItemAll() 
			{
                        
						 $query = " SELECT a.Id as RegistrantCredItemTransId,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,

										a.CredItemId,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										CredItem,
										a.CredItemStatus,
										CredItemStatusDesc,
										CredItemStatusColor,
										CredItemStatusBGColor,
										ComplianceLevelId,
										CredItemType,
									
										COALESCE(a.Results,'') as Results,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	SchSchoolRegistrantCredItems a,
									CredentialingItems b,
									SchSchoolRegistrants c,
									SchSchoolRegistrantTypes f,
									CredentialingItemStatuses g,
									Users e 
						WHERE 	a.CredItemId = b.Id 
								AND a.RegistrantId = c.Id
								AND c.TypeId = f.Id
								AND a.CredItemStatus = g.CredItemStatus
								AND a.UserId = e.UserId
								AND ComplianceLevelId !=1 
								AND DATEDIFF( ExpirationDate, CURDATE( )  ) between 0 and 30 
							Order by RegistrantName, CredItem   ";
								  
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Registrants for Email Blast Listing
            //================================================================================== */			
			function getSchoolRegistrantsSelEmailBlast($RegistrantTypeId, $BoroughId) {
                        $query = "SELECT a.Id as RegistrantId,
										CONCAT( trim( a.LastName) , ', ', trim( a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										COALESCE((SELECT max(ServiceDate) from SchSchoolWeeklyServices g
											where a.Id = g.RegistrantId
											AND ServiceStatusId = 7 ), '') as LastServiceDate,  
										a.City,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										TypeId,
										a.Email,
										(SELECT count(*) from SchSchoolRegistrantCredItems d
											where a.Id = d.RegistrantId
											AND ComplianceLevelId = 1 ) as CredItemsNonCompliant,    
										(SELECT count(*) from SchSchoolRegistrantCredItems d
											where a.Id = d.RegistrantId
											AND ComplianceLevelId != 1 ) as CredItemsCompliant
										
									FROM 	SchSchoolRegistrants a, 
											SchSchoolRegistrantTypes f
									where 	a.TypeId = f.Id
											and a.StatusId = 1
											and a.TypeId = '{$RegistrantTypeId}'
											and a.Email != ''
											and exists (Select 1 from SchBoroughZipcodes b
													 where a.ZipCode =  b.ZipCode 
													 and BoroughId = '{$BoroughId}') 
										Order By a.LastName, a.FirstName  "; 
													 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			// Get School Board Messages
            //=======================			
			function getSchoolBoardMessages() {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ) as TransDate
							FROM SchSchoolBoardMessages a, Users b  
								WHERE a.UserId = b.UserId 
								Order By  TransDate Desc"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			// Set School Board Message Info
            //=======================			
			function setSchoolBoardMessage(  $Msg,
								    $HighPriority,	  
									$UserId)
			
			{ 
				 
                       $query ="Insert into SchSchoolBoardMessages ( Msg,
															HighPriority,
															UserId,		
															TransDate )
							values ('{$Msg}',  
									'{$HighPriority}',  
									'{$UserId}',
									 NOW() )  ";

				 	 
                        $result = $this->connection->query($query);
                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;  
	 		}	
			
			/* Get School Registrant's Services History Data
            //===========================*/		
			function getSchoolRegistrantServicesHistory($RegistrantId) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceTypeDesc,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
																				
										COALESCE((SELECT CONCAT(  trim( g.LastName) , ', ', trim(g.FirstName) )
											FROM SchStudents g
											WHERE a.StudentId = g.Id), '') as StudentName,   
	
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										StartTime as StartTimeSort,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										EndTime as EndTimeSort,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                        SchSchoolTypes i, 
										Users f			
									WHERE a.RegistrantId = '{$RegistrantId}'
                                    AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                                                        AND SchoolTypeId = i.Id 
									AND a.UserId = f.UserId 
									Order By ServiceDateSort DESC,  SchoolName, StartTimeSort, EndTimeSort ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School's Services History Data
            //===========================*/		
			function getSchoolServicesHistory($SchoolId) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceTypeDesc,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                        SchSchoolTypes i, 
										Users f			
									WHERE a.SchoolId = '{$SchoolId}'
									 
									AND a.ServiceTypeId = 1 
                                    AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
									AND a.UserId = f.UserId 
									Order By ServiceDateSort DESC";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Student's Services History Data
            //===========================*/		
			function getStudentServicesHistory($StudentId) {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceTypeDesc,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										COALESCE(ConfirmationNumber,'') as ConfirmationNumber,
										CASE AssignmentTypeId 
											WHEN '1' THEN 'Per Diem'
											ELSE 'Long Term'
											END AS AssignmentTypeDesc,
										a.DistrictId,
                                        DistrictName, 
										a.SchoolId,
                                        SchoolName,
                                        SchoolTypeDesc,
										PayrollWeek,
										PriorityFL,
										SchoolConfFL,
										RegistrantConfFL,
										ServiceComment,
										RegistrantTypeId, 
										a.RegistrantId,
										a.PayRate,
										(SELECT COALESCE(CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ), '')
											FROM SchSchoolRegistrants b, SchSchoolRegistrantTypes f
											WHERE a.Registrantid = b.Id
											AND RegistrantTypeId = f.Id) as RegistrantName,   
										
										ServiceDate as ServiceDateSort,
							 			DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										StartTime as StartTimeSort,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										EndTime as EndTimeSort,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g, 
                                        SchSchools h,
                                        SchSchoolTypes i, 
										Users f			
									WHERE a.StudentId = '{$StudentId}'
                                    AND a.DistrictId = g.Id
									AND a.DistrictId = d.DistrictId
                                    AND a.SchoolId = h.Id
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND SchoolTypeId = i.Id 
									AND a.UserId = f.UserId 
									Order By ServiceDateSort DESC, StartTimeSort, EndTimeSort";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get School Registrant Weekly Availability All Info
            //================================================ */			
			function getSchoolRegistrantWklyAvailabilityAll($PayrollWeek, $ServiceTypeId) {
                        								
						$query = "Call proc_getSchoolRegistrantWklyAvailabilityAll ( '{$PayrollWeek}', '{$ServiceTypeId}')  "; 
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			// Get School Registrant Tiem Card Verificaiton Data
            //=======================			
			function getSchoolRegistrantTimeCardVerificaiton($RegistrantId, $PayrollWeek) {
			
                        $query ="SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceStatusId,
										ServiceStatusDesc,
										ServiceStatusTextColor,
										ServiceStatusBackgroundColor,
										ServiceCoverageTypeId,
										a.ServiceTypeId,
										ServiceTypeDesc as ServiceCoverageTypeDesc,
										/*ServiceCoverageTypeDesc,*/
										/*
										COALESCE((SELECT ServiceCoverageTypeDesc 
											FROM SchServiceCoverageTypes b 
											WHERE a.ServiceTypeId = b.ServiceTypeId ),'') as ServiceCoverageTypeDesc,  
										*/
										COALESCE((SELECT CONCAT(  trim( g.LastName) , ', ', trim(g.FirstName) )
											FROM SchStudents g
											WHERE a.StudentId = g.Id), '') as StudentName,  	
										a.DistrictId,
                                        DistrictName, 
                                        i.BillingCode as BillingClientCode, 
										a.SchoolId,
                                        SchoolName,
                                        SUBSTRING_INDEX(h.ExtId, '#', 1 ) as BillingClientArea,  
										SUBSTRING_INDEX( h.ExtId, '#', -1 ) as BillingSchoolId,
                                        PayrollWeek,
										a.RegistrantId,
										a.PayRate,
										ServiceTypeDesc,   
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g,  
                                        SchSchools h,  
										SchDistrictSchoolContracts i,
										SchServiceCoverageTypes k,
										Users f			
									WHERE a.RegistrantId = '{$RegistrantId}' 
									AND PayrollWeek = '{$PayrollWeek}'									  
                                    AND ServiceStatusId = 7 									
									AND a.DistrictId = d.DistrictId
                                    AND a.DistrictId = g.Id 
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
                                    AND a.SchoolId = h.Id 
									AND a.DistrictId = i.DistrictId 
									AND a.AssignmentTypeId = i.AssignmentTypeId
									AND a.ServiceTypeId = k.Id
									AND a.UserId = f.UserId 
									Order By ServiceDateSort"; 
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
			}				

			/* Get Service Coverage Types By Service Type Listing
            //=======================	*/		
			function getServiceCoverageTypesByServiceType($ServiceTypeId) {
			
                        $query = "SELECT Id as id, 
										Id as ServiceCoverageTypeId,
										ServiceCoverageTypeDesc
									FROM SchServiceCoverageTypes
									Where ServiceTypeId = '{$ServiceTypeId}' 
										Order by Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			// Set Adjust School Service Status
            //=======================			
			function setAdjustSchoolService(
								$ServiceId,
								$StartTime,
								$EndTime,
								$TotalHours,
								$WeekDay, 
								$DistrictId,
								$SchoolId,
								$ServiceCoverageTypeId,
								$UserId) 
			{ 
                        $query = "update SchSchoolWeeklyServices 
						            set StartTime = '{$StartTime}', 
                                        EndTime = '{$EndTime}', 
                                        TotalHours = '{$TotalHours}', 
										WeekDay = '{$WeekDay}',
                                        DistrictId = '{$DistrictId}', 
										SchoolId = '{$SchoolId}',
										ServiceCoverageTypeId = '{$ServiceCoverageTypeId}',
                                        UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			// Set Verify Time Card 
            //=======================			
			function setVerifyTimeCard(
								$ServiceId,
								$ServiceStatusId,
								$UserId) 
			{ 
                        $query = "update SchSchoolWeeklyServices 
						            set ServiceStatusId = '{$ServiceStatusId}', 
                                         UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			/* Get TiemCard Site Navigation Data
            //=======================	*/		
			function getTimeCardSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchTimeCardNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get TimeCards Upload Data Options
            //===========================*/		
			function getSchoolTimeCardsUploadDataOptions() {
			
                        $query = "SELECT Id as id,
										 UploadDesc,
										 UploadLink			
						FROM SchSchoolTimeCardsDataUpload 
					order by Id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Billing Site Navigation Data
            //=======================	*/		
			function getSchoolBillingSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchSchoolBillingNavigation 
					order by id  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			/* Get Schools DOE (Un-Billed) Billing Data
            //==========================*/		
			function getSchoolDOEBillingData() {
			
                        $query = "SELECT a.Id as id, 
										a.Id as ServiceId, 
										ServiceCoverageTypeId,
										a.ServiceTypeId,
										COALESCE((SELECT ServiceCoverageTypeDesc 
											FROM SchServiceCoverageTypes b 
											WHERE ServiceCoverageTypeId = b.Id ),'') as ServiceCoverageTypeDesc,  
										COALESCE((SELECT CONCAT(  trim( g.LastName) , ', ', trim(g.FirstName) )
											FROM SchStudents g
											WHERE a.StudentId = g.Id), '') as StudentName,  	
										a.DistrictId,
                                        DistrictName, 
                                        g.ExtId as BillingClientCode, 
										a.SchoolId,
                                        SchoolName,
                                        SUBSTRING_INDEX(h.ExtId, '#', 1 ) as BillingClientArea,  
										SUBSTRING_INDEX( h.ExtId, '#', -1 ) as BillingSchoolId,
                                        PayrollWeek,
										a.RegistrantId,
										CONCAT( trim( j.LastName) , ', ', trim(j.FirstName)) as RegistrantName,
										a.PayRate,
										/*BillRate,*/
										CASE AssignmentTypeId 
											WHEN '1' THEN BillRate
											ELSE '48.50'
										END AS BillRate,
										/*(BillRate *  TotalHours) as BillAmt,*/
										CASE AssignmentTypeId 
											WHEN '1' THEN (BillRate *  TotalHours)
											ELSE (48.50 *  TotalHours)
										END AS BillAmt,
										ServiceTypeDesc,   
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										WeekDay,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										TotalHours,
										ConfirmationNumber,
										ReportComments,
										InvoiceNumber,
										InvoiceNumberExt,
										CASE BillingExtractDate 
											WHEN '0000-00-00 00:00:00' THEN 'No'
											ELSE 'Yes'
										END AS DataTransferStatus,

										a.UserId,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate
									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g,  
                                        SchSchools h,  
										SchSchoolRegistrants j,
										Users f			
									WHERE ServiceStatusId = 8 
									AND BillingStatusId = 0									  
 									AND a.DistrictId = d.DistrictId
                                    AND a.DistrictId = g.Id 
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceTypeId < 4  
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
									AND a.RegistrantId = j.Id
                                    AND a.SchoolId = h.Id 
									AND a.UserId = f.UserId 
									AND a.DistrictId < '34'
									Order By DistrictName, SchoolName, ServiceDateSort ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
			// Set Adjust School DOE Billing data
            //=======================			
			function setAdjustSchoolDOEBillingData(
								$ServiceId,
								$InvoiceNumber,
								$InvoiceNumberExt,
								$ConfirmationNumber,
								$ReportComments, 
								$StartTime,
								$EndTime,
								$TotalHours,
								$UserId) 
			{ 
                        $query = "update SchSchoolWeeklyServices 
						            set InvoiceNumber = '{$InvoiceNumber}', 
                                        InvoiceNumberExt = '{$InvoiceNumberExt}', 
										ConfirmationNumber = '{$ConfirmationNumber}',
                                        ReportComments = '{$ReportComments}', 
                                        UserId = '{$UserId}',
										StartTime = '{$StartTime}',
										EndTime = '{$EndTime}',
										TotalHours = '{$TotalHours}',
                                        TransDate = now()										
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			// Set Update School DOE Billing Flag   
            //=======================			
			function setUpdateSchoolDOEBillingData(
								$ServiceId,
								$BillingStatusId,
								$UserId) 
			{ 
                        $query = "update SchSchoolWeeklyServices 
						            set BillingStatusId = '{$BillingStatusId}', 
                                         UserId = '{$UserId}',
                                        TransDate = now()										
									Where 	Id = '{$ServiceId}' ";
						
						$result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						//return $result;
						return $query;
			}
			
			/* Get School Billing Extenal Invoice # Listing
            //============================================ */			
			function getSchoolBillingExternalInvoices() {
                        $query = "SELECT DISTINCT a.InvoiceNumberExt AS id,
									a.InvoiceNumberExt,
									SUM((BillRate *  TotalHours)) as TotBillAmt
							FROM SchSchoolWeeklyServices a,
								SchDistrictServiceTypes d 
							WHERE a.InvoiceNumberExt != ''
							AND a.DistrictId = d.DistrictId
							AND a.ServiceTypeId = d.ServiceTypeId
							Group BY a.InvoiceNumberExt 
							ORDER BY a.InvoiceNumberExt ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}
			
			/* Get Schools DOE Billed  Transactions Data
            //==========================*/		
			function getSchoolDOEBilledTransactions($InvoiceNumberExt) {
			
                        $query = "SELECT 
                                        ReportComments,
										TRIM(InvoiceNumber) as InvoiceNumber,
										InvoiceNumberExt,
										SchoolName,
										DistrictName,
										COALESCE((SELECT ServiceCoverageTypeDesc 
											FROM SchServiceCoverageTypes b 
											WHERE ServiceCoverageTypeId = b.Id ),'') as ServiceCoverageTypeDesc,
										TotalHours,
										g.ExtId as BillingClientCode, 
                                        SUBSTRING_INDEX(h.ExtId, '#', 1 ) as BillingClientArea,  
										SUBSTRING_INDEX( h.ExtId, '#', -1 ) as BillingSchoolId,
										CONCAT( trim( j.LastName) , ', ', trim(j.FirstName)) as RegistrantName,
										BillRate,
										(BillRate *  TotalHours) as BillAmt,
										ServiceTypeDesc,   
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										CONCAT( trim( f.FirstName ) , ' ', trim( f.LastName ) ) AS UserName,
										a.TransDate

									FROM SchSchoolWeeklyServices a, 
										SchSchoolServicesDetails b,
										SchSchoolServicesStatuses c, 
										SchDistrictServiceTypes d,
										SchServiceTypes e,
                                        SchDistricts g,  
                                        SchSchools h,  
										SchSchoolRegistrants j,
										Users f			
									WHERE InvoiceNumberExt = '{$InvoiceNumberExt}' 
                                    AND BillingStatusId = 1									  
 									AND a.DistrictId = d.DistrictId
                                    AND a.DistrictId = g.Id 
									AND a.ServiceTypeId = b.ServiceTypeId
									AND a.ServiceTypeId = d.ServiceTypeId
									AND a.ServiceStatusId = c.Id
									AND a.ServiceTypeId = e.Id
									AND a.SchoolId = b.SchoolId	
									AND a.RegistrantId = j.Id
                                    AND a.SchoolId = h.Id 
									AND a.UserId = f.UserId 
									Order By InvoiceNumber  ";
                                                      
                        $result = $this->connection->getAll($query, DB_FETCHMODE_ASSOC);

                        if (DB::isError($result)){
                            die("Could not query the database:<br />$query ".DB::errorMessage($result));
                        }
			
						return $result;
						
			}	
			
		
			function setFormData($formData)  
			{
				$form_data1 = json_decode($formData);
				return $form_data1->{'ConfirmationNumber'};
						
			}				
			
}

            	
			

?>