<?php


	require_once("db_GetSetData.php");
	$conn = getCon();


 
    $MandateId = $_GET['MandateId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
	$SearchId = $_GET['SearchId'];


	$date=date_create($FromDate);
	$from_date_frm =  date_format($date,"m/d/y");

	$date=date_create($ToDate);
	$to_date_frm =  date_format($date,"m/d/y");

	$period = $from_date_frm.'-'.$to_date_frm;

	//$to = '<EMAIL>';
	//$from = '<EMAIL>';
	 
	// $from = '<EMAIL>';
	// $to = '<EMAIL>';


	// To send HTML mail, the Content-type header must be set
	$headers  = 'MIME-Version: 1.0' . "\r\n";
	$headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
	 
	// // Create email headers
	// $headers .= 'From: '.$from."\r\n".
	//     'Reply-To: '.$from."\r\n" .
	//     'X-Mailer: PHP/' . phpversion();
	 
			
	//================			 	
	
	   $query = "SELECT   DISTINCT      DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
                        a.ServiceDate as ServiceDateSort,
                        DATE_FORMAT( a.ServiceDate, '%M') AS ApprovalMonthName, 
                        DATE_FORMAT( a.ServiceDate, '%c') AS ApprovalMonth, 
                        DATE_FORMAT( a.ServiceDate, '%Y') AS ApprovalYear, 
                        
                        a.StartTime as StartTimeSort,
                        DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                        DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                        a.SessionGrpSize,
                        concat(' (#s',a.StudentId,'s$)') as StudentId,
                        concat(' (#p',a.RegistrantId,'p$)') as RegistrantId, 
                        concat(b.FirstName,' ',b.LastName) as RegistrantName,
                        concat(c.FirstName,' ',c.LastName) as StudentName,
                        ServiceTypeDesc,
                        GuardianFirstName,
                        GuardianLastName,
                        concat(GuardianFirstName,' ',GuardianLastName) as GuardianName,
                        GuardianEmail,
                        CompanyName,
                        f.SessionFrequency,
                        f.SessionLength,
                        -- f.SessionGrpSize,
                        CONCAT(f.SessionFrequency, 'X', f.SessionLength, 'X', f.SessionGrpSize) as MandateDesc,
                        a.MandateId,
                        e.ApprovalEmail,
                        b.Email as RegistrantEmail,
                        c.ExtId as OSIS_Number,
                        b.Email as ProviderEmail  

                        

                    FROM     WeeklyServices a,	
                             Registrants b,
                             SchStudents c,
                             SchServiceTypes d,
                             Company e,
                             SchStudentMandates f
                        WHERE a.MandateId = '{$MandateId}' 
                        AND   f.Id = a.MandateId
                        AND   a.ScheduleStatusId >= '7'
                        AND   a.RegistrantId = b.Id
                        AND   a.StudentId = c.Id
                        AND   a.ServiceTypeId = d.Id
                        AND   a.SessionDeliveryModeId != 'I'  
                        AND   GuardianEmail != ''
                        AND   a.ServiceDate between  '{$FromDate}' and '{$ToDate}'   
                ORDER BY ServiceDateSort, StartTimeSort    
            ";

	  $result =  mysqli_query($conn, $query) or die
	  ("Error in Selecting " . mysqli_error($conn));
	 
	  //echo '$query: '.$query.'</br>';

	  $i = 0;


	  while ($row = $result->fetch_assoc()) {
			
		$i++;	



		if ($i == 1) {



			$from = $row['ApprovalEmail'];
			$to = $row['GuardianEmail'];

			if ($row['ProviderEmail']) {

				$bcc = $row['ProviderEmail'];

			} else {

				$bcc = '';

			}

			//$from = '<EMAIL>';
			//$to = '<EMAIL>';

			// Create email headers
			$headers .= 'From: '.$from."\r\n".
			    'Reply-To: '.$from."\r\n" .
			    'Bcc: '.$bcc."\r\n" .
			    'X-Mailer: PHP/' . phpversion();


			// Compose a simple HTML email message
		 	
			// $subject = 'Sessions Approval Request from: '.$row['CompanyName'];
			$subject =  $row['StudentName'].' '.$row['MandateDesc'].' '.$row['CompanyName']. ' Tele-therapy Session Confirmation Required for '.$period;



			$message = '<html><body>';

			$message .= '<p style="font-size:18px;">Dear '.ucwords($row['GuardianName']).',</p>';

			// $message .= '<p style="font-size:18px;">Please respond to this email with the word  <span style="color: #ff0000">CONFIRMED</span>  in order to verify that the following Tele-Therapy Sessions were provided to your child:</p>';


			$message .= '<p style="font-size:18px;">Your child attended teletherapy sessions for the '.$period.' bi-weekly period and you are required to confirm that the following sessions below were serviced.  Please reply to this email with the word <span style="color: #ff0000">CONFIRMED</span> in order to verify that the following Tele-Therapy Sessions were provided to your child:</p>';


			// $message .= '<p style="font-size:18px;">Responda a este correo electronico con la palabra  <span style="color: #ff0000">CONFIRMED</span>  para verificar que se hayan proporcionado las siguientes sesiones de teleterapia a su hijo:</p>';

			$message .= '<p style="font-size:18px;">Su hijo/a asistio a las sesiones de teleterapia durante el periodo quincenal del '.$period.' y requerimos que usted confirme que su hijo/a  asistio a estas sesiones.  Responda a este correo electronico con la palabra <span style="color: #ff0000">CONFIRMED</span> para verificar que se hayan proporcionado las siguientes sesiones de teleterapia a su hijo/a:</p>';

			
			$message .= '<p style="font-size:18px; margin-bottom:0;"><b>Child Name:</b> '.$row['StudentName'].' <b>OSIS#:</b> '.$row['OSIS_Number'].'</p>';
			$message .= '<p  style="font-size:18px; margin-bottom:0;"><b>Provider Name:</b> '.$row['RegistrantName'].'</p>';
			$message .= '<p  style="font-size:18px; margin-bottom:0;"><b>Sessions Month:</b> '.$row['ApprovalMonthName'].'<b> Year:</b> '.$row['ApprovalYear'].'</p>';
			
			$message .= '<table width="600" border="1" cellpadding="0" cellspacing="0" bordercolor="#CCCCCC">
						<tr>
							<th>Session Date</th>
							<th>Start Time</th>
							<th>End Time</th>
							<th>Freq</th>
							<th>Dur Time</th>
							<th>Group Size</th>
							<th>Service Type</th>					
							<th>Provider Name</th>
						</tr>';


			// $ids = '#d'.$row['MandateId'].'d$-#m'.$row['ApprovalMonth'].'m$-#y'.$row['ApprovalYear'].'y$';
			// $ids .='-#n'.$row['GuardianFirstName'].' '.$row['GuardianLastName'].'n$';

			$ids = '#s'.$SearchId.'$s';
			

		}


			$td = '';
			$td .= '<td>'.$row['ServiceDate'].'</td>';
			$td .= '<td>'.$row['StartTime'].'</td>';
			$td .= '<td>'.$row['EndTime'].'</td>';
			$td .= '<td align="center">'.$row['SessionFrequency'].'</td>';
			$td .= '<td align="center">'.$row['SessionLength'].'</td>';
			$td .= '<td align="center">'.$row['SessionGrpSize'].'</td>';
			$td .= '<td>'.$row['ServiceTypeDesc'].'</td>';
			$td .= '<td>'.$row['RegistrantName'].'</td>';

			$message .=	'<tr>'.$td.'</tr>';  
			$message = wordwrap($message);



	  }	


    setDisConn($conn);


    //================

	$message .= '</table></br></br><hr>';
	$message .= '<h6>'.$ids.'!</h6>';

	$message .= '</body></html>';
 

 	//Sending email
	if(mail($to, $subject, $message, $headers)){
	    echo 'Your mail has been sent successfully to: '.$to;
	} else{
	    echo 'Unable to send email. Please try again.';
	}

?>


 