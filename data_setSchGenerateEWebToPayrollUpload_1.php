<?php
	/** Error reporting */
  	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
  
	require_once('DB.php');
	include('db_login.php');

	/** PHPExcel */
	//include 'PHPExcel.php';

	include('../../phpexcel-1-8/Classes/PHPExcel.php');

	/** PHPExcel_Writer_Excel2007 */
	//include 'PHPExcel/Writer/Excel2007.php';
	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


	// $UserId = $_POST['UserId'];
	// if (!$UserId) {
		$UserId = '0';
	// }

	// $Data = $_POST['Data'];
	$Data = ["4822771"];
	// $Data=json_decode($Data,true);


	$HrTypeId = '1'; 
	// $HrTypeId = $_POST['HrTypeId'];
	if ($HrTypeId == '1') {

		$SheetName = 'Employee';

	} else {

		$SheetName = '1099 Subcontractor';

	}

	$BillingContractId = '1'; 

	// $BillingContractId = $_POST['BillingContractId'];
	if (!$BillingContractId) {
		$BillingContractId = '1';
	}

	if ($BillingContractId == '1') {

		$summary_type = 'School Name';
		$school_dbn_title = 'School Name DBN';

	} else {

		$summary_type = 'Student';
		$school_dbn_title = '';

	}

	// $BillingContractName = $_POST['BillingContractName'];

	$BillingContractName = "Contract";

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


	/*=========== Get/update Next Payrol Batch Number ================*/


		$next_payroll_batch_num = 0; 
		

		$query1 = "SELECT (MAX(Id) + 1) as NextBatchNumber 
					FROM SchPayrollBatchHeader";
		
		$result1 = $connection->query ($query1);


	 	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {	

	 		$next_payroll_batch_num = $row1['NextBatchNumber']; 
	 	}	
		


		//================================	
		// Add "PayrollBatchHeader" Record
		//==================================
		 
		 

		$query2 = "INSERT INTO SchPayrollBatchHeader
					(	Id,
						BatchDate,
						BatchCount,
						BatchTypeId,
						UserId,
						TransDate)
					VALUES
					(
						'{$next_payroll_batch_num}',
						curdate(),
						0,
						'2',  
						'{$UserId}',
						NOW()
					);";
		
		echo  "query2: $query2<br>";  

		$result2 = $connection->query ($query2);


	/*=========== Set Selected Transascitons with the Next Payrol Batch Number ================*/

	foreach ($Data as $ScheduleId) {

		echo "ScheduleId: $ScheduleId<br>";


		//================================
		// Update "Billing Extract Date"
		//==================================
		 
		
		$query3 = "UPDATE WeeklyServices a 
					  SET PayrollBatchNumber ='{$next_payroll_batch_num}',
					      PaidFL = '1',
					      UserId = '{UserId}',
					      TransDate = NOW()

				WHERE a.Id =  '$ScheduleId'  
				";
		
 		echo "query3: $query3<br>";
		$result3 = $connection->query ($query3);


	}	



$objPHPExcel = new PHPExcel();

 
$objPHPExcel->setActiveSheetIndex(0);
$objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Applicant Name');
$objPHPExcel->getActiveSheet()->SetCellValue('B1', 'HRID');
$objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Service Date');
$objPHPExcel->getActiveSheet()->SetCellValue('D1', 'Start Time');
$objPHPExcel->getActiveSheet()->SetCellValue('E1', 'End Time');
$objPHPExcel->getActiveSheet()->SetCellValue('F1', 'District');
$objPHPExcel->getActiveSheet()->SetCellValue('G1', 'Service Type');
$objPHPExcel->getActiveSheet()->SetCellValue('H1', 'Billing Contract');
$objPHPExcel->getActiveSheet()->SetCellValue('I1', 'Hours');
$objPHPExcel->getActiveSheet()->SetCellValue('J1', 'School Name DBN');
//$objPHPExcel->getActiveSheet()->SetCellValue('K1', $school_dbn_title);
//$objPHPExcel->getActiveSheet()->SetCellValue('K1', $school_dbn_title);


/*$school_dbn_title*/ 


	$query = "CALL proc_getSchEWebToPayrollUploadData ('$next_payroll_batch_num', '$BillingContractId', '$BillingContractName')"; 



	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 1;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$linecount++;


            $row_num++;

            // Extract Distrcict  
			$filteredNumbers = array_filter(preg_split("/\D+/", $row['DistrictName']));
			$district_number = reset($filteredNumbers);

         
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['RegistrantName']);
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['HrId']);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['ServiceDate']);

		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['StartTime']);
		    $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['EndTime']);

		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $district_number);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['ServiceTypeDesc']);
		    $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['BillingContractName']);


		    $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['TotalHours']);
		   /* $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['SchoolName']); */
		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['SchoolNameDBN']);


 

	}	

	$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection1 = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection1)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection1));
    } 

		$query4 = " 	UPDATE SchPayrollBatchHeader
					SET BatchCount = '{$linecount}'
					WHERE Id = '{$next_payroll_batch_num}' ";

 		
		$result4 = $connection1->query ($query4);


		$connection->disconnect();
		$connection1->disconnect();



 

	/*=========================================================================*/

// // Rename sheet
// //echo date('H:i:s') . " Rename sheet\n";
// $objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// // Save Excel 2007 file
// //echo date('H:i:s') . " Write to Excel2007 format\n";
// $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



// //$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

// $out_File = "../uploads/eweb_to_payroll_upload.xlsx";

// $objWriter->save($out_File);

// // Echo done
// echo  "{ success: true, transactions: '{$linecount}'}";
// //echo  $query4  ;

   

?>