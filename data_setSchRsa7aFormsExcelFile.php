<?php
	/** Error reporting */

  // ini_set("memory_limit","-1");

   	
	// error_reporting(E_ALL);
	// ini_set('display_errors', TRUE);
	// ini_set('display_startup_errors', TRUE);
   
	require_once('DB.php');
	include('db_login.php');
 
	include('../../phpexcel-1-8/Classes/PHPExcel.php');
 	include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');


 

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


 
    $FromDate = $_GET['FromDate'];  
    $PeriodStartDate = $_GET['PeriodStartDate'];  
    $PeriodEndDate = $_GET['PeriodEndDate'];  
    $EndDate = $_GET['EndDate'];  
    $RegistrantIdSel = $_GET['RegistrantIdSel'];  
    $StudentIdSel = $_GET['StudentIdSel'];  
    $SchoolIdSel = $_GET['SchoolIdSel'];  
    $BiMonthyperiodIdSel = $_GET['BiMonthyperiodIdSel'];  
    $FromProviderName = $_GET['FromProviderName'];  
    $ToProviderName = $_GET['ToProviderName'];  


    $selected_year = date("Y",strtotime($PeriodStartDate));
    $selected_month = date("F",strtotime($PeriodStartDate));





	  $objPHPExcel = new PHPExcel();

	 
	 $objPHPExcel->setActiveSheetIndex(0);

  
   $objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Year:');
   $objPHPExcel->getActiveSheet()->getStyle("A1")->getFont()->setBold(true)
                                ->setName('Verdana')
                                ->setSize(10)
                                ;


   $objPHPExcel->getActiveSheet()->SetCellValue('B1', $selected_year);
   $objPHPExcel->getActiveSheet()->SetCellValue('C1', 'Month:');
   $objPHPExcel->getActiveSheet()->getStyle("C1")->getFont()->setBold(true)
                                ->setName('Verdana')
                                ->setSize(10)
                                ;

   $objPHPExcel->getActiveSheet()->SetCellValue('D1', $selected_month);


   $objPHPExcel->getActiveSheet()->SetCellValue('A3', 'Provider Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('B3', 'Student ID');
   $objPHPExcel->getActiveSheet()->SetCellValue('C3', 'Student Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('D3', 'Service Type');
   $objPHPExcel->getActiveSheet()->SetCellValue('E3', 'Freq');
   $objPHPExcel->getActiveSheet()->SetCellValue('F3', 'Dur');
   $objPHPExcel->getActiveSheet()->SetCellValue('G3', 'Grp Size');
   $objPHPExcel->getActiveSheet()->SetCellValue('H3', 'Form Type');
   $objPHPExcel->getActiveSheet()->SetCellValue('I3', 'Bi-Weekly Period');

   $objPHPExcel->getActiveSheet()->SetCellValue('J3', 'School');
   $objPHPExcel->getActiveSheet()->SetCellValue('K3', 'Approver Name');
   $objPHPExcel->getActiveSheet()->SetCellValue('L3', 'Approver Email');
   $objPHPExcel->getActiveSheet()->SetCellValue('M3', 'Approver Phone #');
   $objPHPExcel->getActiveSheet()->SetCellValue('N3', 'Signature Status');
   $objPHPExcel->getActiveSheet()->SetCellValue('O3', 'Billing Contract');
   $objPHPExcel->getActiveSheet()->SetCellValue('P3', 'Comments');

   


    $query = "call proc_getSchRsa7aForms (   '{$FromDate}',
                                        '{$PeriodEndDate}',
                                        '{$PeriodStartDate}',
                                        '{$EndDate}',
                                        '{$RegistrantIdSel}',
                                        '{$StudentIdSel}',
                                        '{$SchoolIdSel}',
                                        '{$BiMonthyperiodIdSel}', 
                                        '{$FromProviderName}' ,
                                        '{$ToProviderName}' 
                                                                )     
 ";


   // echo $query;

	$result = $connection->query ($query);

 	
	$linecount = 0;
	$row_num = 3;

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$linecount++;


            $row_num++;

          $status = $row['Rsa7aFormSignatureStatus'];            

         switch ($status) {
          case '0':
            $signatire_status = 'Awaiting Provider Signature'; 
            break;
          
          case '2':
            $signatire_status = 'Awaiting Parent Signature'; 
            break;
          
          case '3':
            $signatire_status = 'Awaiting Principal Signature'; 
            break;
          
          case '4':
            $signatire_status = 'Fully Signed'; 
            break;

          default:
            $signatire_status = 'Undefined'; 
        } 

          
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('A'.$row_num, $row['RegistrantName']);
		  
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('B'.$row_num, $row['StudentExtId']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('C'.$row_num, $row['StudentName']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('D'.$row_num, $row['ServiceTypeDesc']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('E'.$row_num, $row['SessionFrequency']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('F'.$row_num, $row['SessionLength']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('G'.$row_num, $row['SessionGrpSize']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('H'.$row_num, $row['FormType']);
        
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('I'.$row_num, $row['BiMonthlyPeriodDesc']);

        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('J'.$row_num, $row['SchoolName']);
		 
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('K'.$row_num, $row['ApproverName']);
		 
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setAutoSize(true);
		    $objPHPExcel->getActiveSheet()->setCellValue('L'.$row_num, $row['ApproverEmail']);
		    
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('M'.$row_num, $row['ApproverPhone']);

		    $objPHPExcel->getActiveSheet()->getColumnDimension('N')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('N'.$row_num, $signatire_status);

        $objPHPExcel->getActiveSheet()->getColumnDimension('O')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('O'.$row_num, $row['BillingContractDesc']);
 
        $objPHPExcel->getActiveSheet()->getColumnDimension('P')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('P'.$row_num, $row['Comments']);
 
 	}	

 
		$connection->disconnect();
 


 

	/*=========================================================================*/

// Rename sheet
//echo date('H:i:s') . " Rename sheet\n";

$SheetName = 'RSA 7a Forms';
$objPHPExcel->getActiveSheet()->setTitle($SheetName);

		
// Save Excel 2007 file
//echo date('H:i:s') . " Write to Excel2007 format\n";
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);



//$objWriter->save(str_replace('.php', '.xlsx', __FILE__));

$out_File = "../uploads/Rsa7aForms .xlsx";

$objWriter->save($out_File);

echo  "{ success: true, transactions: '{$linecount}'}";

 

/*==============================*/
   
 
   $DownloadedFileName = 'Rsa7aFormsFile-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;
 
   

?>