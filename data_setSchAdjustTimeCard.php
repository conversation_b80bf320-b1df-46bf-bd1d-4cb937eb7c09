<?php 


require "ewDataHandler.php";
  
$rcr_transaction = new dataHandler(); 

$ScheduleId =  $_POST['ScheduleId'];
$StartTime =  $_POST['StartTime'];
$EndTime =  $_POST['EndTime'];	
$LunchHour =  $_POST['LunchHour'];
$TotalHours =  $_POST['TotalHours'];	
$ApplyAllDays = $_POST['ApplyAllDays'];

$UserId = $_POST['UserId'];

if ($LunchHour == '') {

	$LunchHour = '0';	
}



if ($ApplyAllDays == '1') {


	$result = $rcr_transaction->setSchAdjustTimeCardAllWeek($ScheduleId,
														    $StartTime,
														    $EndTime,
														    $LunchHour,
														    $TotalHours,
														    $UserId); 


} else {

	$result = $rcr_transaction->setSchAdjustTimeCard(	$ScheduleId,
														$StartTime,
														$EndTime,
														$LunchHour,
														$TotalHours,
														$UserId); 


}
												

$rcr_transaction->disconnectDB (); 

echo $result;

?>
