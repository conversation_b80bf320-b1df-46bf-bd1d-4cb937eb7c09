<?php

error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

    $username = '<EMAIL>';
    $password = 'kbltnisqxruugzxu';
$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';

echo "Starting script...\n";
echo "Connecting to IMAP...\n";

// Open IMAP connection
$inbox = imap_open($hostname, $username, $password) or die('Cannot connect to Gmail: ' . imap_last_error());

echo "IMAP connected...\n";

// Search for unread emails
$emails = imap_search($inbox, 'UNSEEN');

if ($emails) {
    rsort($emails); // Sort emails from newest to oldest

    echo "Unread emails found: " . count($emails) . "\n";

    foreach ($emails as $email_number) {
        // Fetch the email's header information
        $header = imap_headerinfo($inbox, $email_number);

        // Check if the subject contains the word 'Docked'
        if (strpos($header->subject, 'Docked') === false) {
            echo "Error: Email subject does not contain 'Docked'.\n";
            continue; // Skip this email and go to the next
        }

        $structure = imap_fetchstructure($inbox, $email_number);

        if (isset($structure->parts) && count($structure->parts)) {
            for ($i = 0; $i < count($structure->parts); $i++) {
                $attachments = $structure->parts[$i];

                if ($attachments->ifdisposition && $attachments->disposition == "ATTACHMENT") {
                    $attachmentName = $attachments->dparameters[1]->value;
                    $fileExtension = strtolower(pathinfo($attachmentName, PATHINFO_EXTENSION));

                    // Check if the attachment is a CSV file
                    if ($fileExtension == 'csv') {
                        $attachment = imap_fetchbody($inbox, $email_number, $i + 1);

                        // Decode the attachment based on its encoding
                        if ($attachments->encoding == 3) { // 3 = BASE64
                            $attachment = base64_decode($attachment);
                        } elseif ($attachments->encoding == 4) { // 4 = QUOTED-PRINTABLE
                            $attachment = quoted_printable_decode($attachment);
                        }

                        sleep(5);
                        $file_name = 'docked_report-' . date("d-m-Y_H-i-s") . '.csv';
                        $targetDirectory = '/var/www/rcm/rn_reports/';

                        // Debugging information
                        echo "Target directory: " . $targetDirectory . "<br>";
                        echo "File name: " . $file_name . "<br>";

                        // Check if file already exists
                        if (file_exists($targetDirectory . $file_name)) {
                            echo "Error: File already exists: " . $file_name . "\n";
                        } else {
                            // Attempt to save the attachment to a file
                            $result = file_put_contents($targetDirectory . $file_name, $attachment);

                            if ($result === false) {
                                echo "Error: Failed to save file to " . $targetDirectory . $file_name . "<br>";
                            } else {
                                echo "Downloaded to rn_reports: " . $targetDirectory . $file_name . "\n";

                                // Process Docked hours file
                                $url = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/data_setSchProcessDockedReportFile.php?Filename=' . urlencode($file_name);

                                echo "url: $url<br>";

                                $curl = curl_init();
                                curl_setopt($curl, CURLOPT_URL, $url);
                                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                                $ret = curl_exec($curl);

                                if ($ret === false) {
                                    echo "cURL Error: " . curl_error($curl);
                                } else {
                                    echo "ret: $ret<br>";
                                }

                                curl_close($curl);
                            }
                        }
                    }
                }
            }
        }
    }
} else {
    echo "No unread emails found.\n";
}

// Close the IMAP connection
imap_close($inbox);
echo "IMAP connection closed.\n";
?>
