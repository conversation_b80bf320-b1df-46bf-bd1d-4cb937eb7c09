<?php 
	
  require_once("db_GetSetData.php");

  $conn = getCon();

  $RegistrantId = $_GET['RegistrantId'];
  $ServiceDate = $_GET['ServiceDate'];

  if (!$RegistrantId) {

      $RegistrantId = '';    
  }

  $query = "call proc_getSchRNDailyCallInTime (  '{$RegistrantId}', 
                                                 '{$ServiceDate}' ) "; 


  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;



  
  
?>

