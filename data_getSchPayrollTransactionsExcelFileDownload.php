<?php

    $FromDate = $_GET['FromDate'];
    $ToDate = $_GET['ToDate'];

    $url = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/data_getSchPayrollTransactionsExcelFile.php?FromDate=' . $FromDate . '&ToDate=' . $ToDate;

    // $curl = curl_init();
    // curl_setopt($curl, CURLOPT_URL, $url);
    // curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    // $ret = curl_exec($curl);

    // if ($ret === false) {
    //     echo "cURL Error: " . curl_error($curl);
    //     curl_close($curl);
    //     exit();
    // }

    // curl_close($curl);

$curl = curl_init();
$out_File = "../pr/payroll_transactions.xlsx";

$fp = fopen($out_File, 'wb'); // Open a file for writing
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_FILE, $fp); // Save directly to file
curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
$ret = curl_exec($curl);

if ($ret === false) {
    echo "cURL Error: " . curl_error($curl);
    fclose($fp);
    curl_close($curl);
    exit();
}

fclose($fp);
curl_close($curl);

    // Save the fetched content to a file
    $out_File = "../pr/payroll_transactions.xlsx";
    // file_put_contents($out_File, $ret);

    // Ensure the file exists and has content
    if (!file_exists($out_File) || filesize($out_File) === 0) {
        die("Error: File was not saved or is empty.");
    }

    // Send the file to the client
    header('Content-Description: File Transfer');
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="payroll_transactions.xlsx"');
    header('Content-Length: ' . filesize($out_File));
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    header('Pragma: public');

    // Clear output buffers and send the file
    ob_clean();
    flush();
    readfile($out_File);

    // Optionally delete the file after download
    // unlink($out_File);
    exit();
?>
