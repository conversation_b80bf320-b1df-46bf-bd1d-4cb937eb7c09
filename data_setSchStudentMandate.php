<?php 


	require_once("db_GetSetData.php");
	$conn = getCon();

	$StudentId = $_POST['StudentId'];
	
	$MandateId = $_POST['MandateId'];
	$StatusId = $_POST['StatusId'];
	$OrigStatusId = $_POST['OrigStatusId'];
	
	$PlaceOfService = $_POST['PlaceOfService'];
	$StartDate = $_POST['StartDate'];	
	$EndDate = $_POST['EndDate'];
	
	$SchoolId = $_POST['SchoolId'];
	$OrigSchoolId = $_POST['OrigSchoolId'];

	$RegistrantId = $_POST['RegistrantId'];
	$OrigRegistrantId = $_POST['OrigRegistrantId'];


	$ParaStartTimeMon1 = $_POST['ParaStartTimeMon1'];
	$ParaEndTimeMon1 = $_POST['ParaEndTimeMon1'];
    $ParaTotalHoursMon1 = $_POST['ParaTotalHoursMon1'];
    $ParaStartTimeMon2 = $_POST['ParaStartTimeMon2'];
    $ParaEndTimeMon2 = $_POST['ParaEndTimeMon2'];
    $ParaTotalHoursMon2 = $_POST['ParaTotalHoursMon2'];
	    
	$ParaStartTimeTue1 = $_POST['ParaStartTimeTue1'];
    $ParaEndTimeTue1 = $_POST['ParaEndTimeTue1'];
    $ParaTotalHoursTue1 = $_POST['ParaTotalHoursTue1'];
    $ParaStartTimeTue2 = $_POST['ParaStartTimeTue2'];
    $ParaEndTimeTue2 = $_POST['ParaEndTimeTue2'];
    $ParaTotalHoursTue2 = $_POST['ParaTotalHoursTue2'];

    $ParaStartTimeWed1 = $_POST['ParaStartTimeWed1'];
    $ParaEndTimeWed1 = $_POST['ParaEndTimeWed1'];
    $ParaTotalHoursWed1 = $_POST['ParaTotalHoursWed1'];
    $ParaStartTimeWed2 = $_POST['ParaStartTimeWed2'];
    $ParaEndTimeWed2 = $_POST['ParaEndTimeWed2'];
    $ParaTotalHoursWed2 = $_POST['ParaTotalHoursWed2'];

    $ParaStartTimeThu1 = $_POST['ParaStartTimeThu1'];
    $ParaEndTimeThu1 = $_POST['ParaEndTimeThu1'];
    $ParaTotalHoursThu1 = $_POST['ParaTotalHoursThu1'];
    $ParaStartTimeThu2 = $_POST['ParaStartTimeThu2'];
    $ParaEndTimeThu2 = $_POST['ParaEndTimeThu2'];
    $ParaTotalHoursThu2 = $_POST['ParaTotalHoursThu2'];

    $ParaStartTimeFri1 = $_POST['ParaStartTimeFri1'];
    $ParaEndTimeFri1 = $_POST['ParaEndTimeFri1'];
    $ParaTotalHoursFri1 = $_POST['ParaTotalHoursFri1'];
    $ParaStartTimeFri2 = $_POST['ParaStartTimeFri2'];
    $ParaEndTimeFri2 = $_POST['ParaEndTimeFri2'];
    $ParaTotalHoursFri2 = $_POST['ParaTotalHoursFri2'];
    $ParaExtdHoursAuthFL = $_POST['ParaExtdHoursAuthFL'];


	$BillingContractId = $_POST['BillingContractId'];
	
	$SESISParaServiceTypeId = $_POST['SESISParaServiceTypeId'];


	$UserId = $_POST['UserId'];


    /* Update Mandate Info
	 =======================*/


         $query = "UPDATE SchStudentMandates 
			SET StatusId = '{$StatusId}',
			    PlaceOfService = '{$PlaceOfService}', 
				StartDate = '{$StartDate}',
				EndDate = '{$EndDate}',
				SchoolId = '{$SchoolId}',
				RegistrantId = '{$RegistrantId}',
				BillingContractId  = '{$BillingContractId }',


				ParaStartTimeMon1	  =	'{$ParaStartTimeMon1}',
				ParaEndTimeMon1	    =	'{$ParaEndTimeMon1}' ,
				ParaTotalHoursMon1	    =	'{$ParaTotalHoursMon1}' ,
				ParaStartTimeMon2	    =	'{$ParaStartTimeMon2}' ,
				ParaEndTimeMon2	    =	'{$ParaEndTimeMon2}' ,
				ParaTotalHoursMon2	    =	'{$ParaTotalHoursMon2}' ,
				


				ParaStartTimeTue1	  =	'{$ParaStartTimeTue1}',
				ParaEndTimeTue1	    =	'{$ParaEndTimeTue1}' ,
				ParaTotalHoursTue1	    =	'{$ParaTotalHoursTue1}' ,
				ParaStartTimeTue2	    =	'{$ParaStartTimeTue2}' ,
				ParaEndTimeTue2	    =	'{$ParaEndTimeTue2}' ,
				ParaTotalHoursTue2	    =	'{$ParaTotalHoursTue2}' ,

				ParaStartTimeWed1	    =	'{$ParaStartTimeWed1}' ,
				ParaEndTimeWed1	    =	'{$ParaEndTimeWed1}' ,
				ParaTotalHoursWed1	    =	'{$ParaTotalHoursWed1}' ,
				ParaStartTimeWed2	    =	'{$ParaStartTimeWed2}' ,
				ParaEndTimeWed2	    =	'{$ParaEndTimeWed2}' ,
				ParaTotalHoursWed2	    =	'{$ParaTotalHoursWed2}' ,

				ParaStartTimeThu1	    =	'{$ParaStartTimeThu1}' ,
				ParaEndTimeThu1	    =	'{$ParaEndTimeThu1}' ,
				ParaTotalHoursThu1	    =	'{$ParaTotalHoursThu1}' ,
				ParaStartTimeThu2	    =	'{$ParaStartTimeThu2}' ,
				ParaEndTimeThu2	    =	'{$ParaEndTimeThu2}' ,
				ParaTotalHoursThu2	    =	'{$ParaTotalHoursThu2}' ,

				ParaStartTimeFri1	    =	'{$ParaStartTimeFri1}' ,
				ParaEndTimeFri1	    =	'{$ParaEndTimeFri1}' ,
				ParaTotalHoursFri1	    =	'{$ParaTotalHoursFri1}' ,
				ParaStartTimeFri2	    =	'{$ParaStartTimeFri2}' ,
				ParaEndTimeFri2	    =	'{$ParaEndTimeFri2}' ,
				ParaTotalHoursFri2	    =	'{$ParaTotalHoursFri2}' ,

				ParaExtdHoursAuthFL	    =	'{$ParaExtdHoursAuthFL}' ,


			    UserId = '{$UserId}',
				TransDate = now()
		WHERE 	Id = '{$MandateId}' 
				";

		$ret =  setData ($conn, $query);  
		setDisConn($conn);

    //=========================================

	if ($RegistrantId != $OrigRegistrantId) {  // Start


		/* Update Student Assignment Registrant from Assigned Mandate 
		 ===================================================*/

		
		$conn = getCon();

        $query1 = "UPDATE   SchStudentAssignmentDetails b,  SchStudentAssignmentHeader d   
				SET   
				     b.RegistrantId = '{$RegistrantId}'
			WHERE 	 d.MandateId = '{$MandateId}'
			AND      d.Id = b.AssignmentId
			AND      b.RegistrantId != '{$RegistrantId}'
			
					";

		
 		$ret =  setData ($conn, $query1);  
		setDisConn($conn);
																     
		/* Update Student Schedule from Assigned Mandate 
		 ===================================================*/

		
		$conn = getCon();

        $query2 = "UPDATE   WeeklyServices a,   SchStudentAssignmentHeader d   
				   SET  a.RegistrantId = '{$RegistrantId}'  
				      
			WHERE 	 d.MandateId = '{$MandateId}'
			AND      d.Id = a.AssignmentId
			AND      a.RegistrantId != '{$RegistrantId}'
					";

		
 		$ret =  setData ($conn, $query2);  
		setDisConn($conn);



	} // End


	/* Update Student's School from Assigned Mandate 
	 ===================================================*/

	if ($SchoolId != $OrigSchoolId) { 


        // // Set School in Student Profile
        // // =============================		

		// $conn = getCon();


        //     $query3 = "UPDATE SchStudents  
		// 			SET SchoolId = '{$SchoolId}' 
 		// 		WHERE 	Id = '{$StudentId}'
		// 		AND     SchoolId != '{$SchoolId}' 
 		// 				";


 		// $ret =  setData ($conn, $query3);  
		// setDisConn($conn);


        // Set School in Student Schedule related to Mandate 
        // =============================		

		$conn = getCon();

		if ($SESISParaServiceTypeId != '0') { // Para


            $query4 = "UPDATE WeeklyServices a, SchStudentAssignmentHeader b 
					SET a.SchoolId = '{$SchoolId}' 
 				WHERE 	b.MandateId = '{$MandateId}'
				AND     b.Id = a.AssignmentId
				AND     a.SchoolId != '{$SchoolId}' 
				AND     a.ServiceTypeId < 39

						";
	 
 


		} else { // Therapy


            $query4 = "UPDATE WeeklyServices    
					SET SchoolId = '{$SchoolId}', 	
					    UserId = '{$UserId}',
						TransDate = now()
				
				WHERE   MandateId = '{$MandateId}'
				AND     SchoolId != '{$SchoolId}'
				AND     ServiceTypeId < 39
						";


		}

 		$ret =  setData ($conn, $query4);  
		setDisConn($conn);
    }

	// /* Inactive Para Assignments for Inactivated Mandates 
	//  ===================================================*/


	if ($SESISParaServiceTypeId != '0') { // Para

		if (($OrigStatusId == '1') && ($StatusId == '2')) {

				$conn = getCon();

                $query5 = "UPDATE SchStudentAssignmentHeader
						   SET StatusId = '2',
						       EndDate = StartDate,
						       UserId = '{$UserId}',
						       TransDate = NOW()  
						  WHERE MandateId =  '{$MandateId}'     
							";
																			    
 		$ret =  setData ($conn, $query5);  
		setDisConn($conn);


		}
	

	 }	


	 	

   // echo $query4;


	// /* Sync Para Assignment  
	//  ===================================================*/


	if ($SESISParaServiceTypeId != '0') { // Para

		include 'db_login.php';

		try {
		    // Enable buffered queries by adding PDO::MYSQL_ATTR_USE_BUFFERED_QUERY
		    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password, [
		        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
		        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
		    ]);

		    // Define the days of the week with their corresponding names and weekday IDs
		    $days = [
		        1 => ['name' => 'Mon', 'WeekDayId' => 1],
		        2 => ['name' => 'Tue', 'WeekDayId' => 2],
		        3 => ['name' => 'Wed', 'WeekDayId' => 3],
		        4 => ['name' => 'Thu', 'WeekDayId' => 4],
		        5 => ['name' => 'Fri', 'WeekDayId' => 5]
		    ];

		    // Iterate over each day to perform the updates
		    foreach ($days as $day) {
		        try {
		            // Prepare the SQL statement with placeholders for dynamic parts
		            

		            // SNync AM Hours
		            //==================
		            $updateStmtAM = $conn->prepare("UPDATE SchStudentMandates a
		                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
		                                            JOIN SchStudents d ON a.StudentId = d.Id
		                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1AMId
		                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
		                                            AND c.WeekDayId = :WeekDayId
		                                            SET 
		                                                c.StartTime = ParaStartTime" . $day['name'] . "1,
		                                                c.EndTime = ParaEndTime" . $day['name'] . "1,
		                                                c.TotalHours = ParaTotalHours" . $day['name'] . "1,
		                                                c.UserId = a.UserId,
		                                                c.TransDate = now()  
		                                            WHERE
		                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
		                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
		                                                AND a.Id = :MandateId
		                                                AND a.EndDate >= '2025-01-01'
		                                                AND ((c.StartTime != ParaStartTime" . $day['name'] . "1 ) || (c.EndTime = !ParaEndTime" . $day['name'] . "1))
		                                                
		                                                ");

		            // Bind parameters
			        $updateStmtAM->bindParam(':MandateId', $MandateId, PDO::PARAM_INT);
		            $updateStmtAM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);
		            
		            
		  
		            // Execute the update statement
		            $updateStmtAM->execute();
		            

		            // SNync PM Hours
		            //==================
		            $updateStmtPM = $conn->prepare("UPDATE SchStudentMandates a
		                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
		                                            JOIN SchStudents d ON a.StudentId = d.Id
		                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1PMId
		                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
		                                            AND c.WeekDayId = :WeekDayId
		                                            SET 
		                                                c.StartTime = ParaStartTime" . $day['name'] . "2,
		                                                c.EndTime = ParaEndTime" . $day['name'] . "2,
		                                                c.TotalHours = ParaTotalHours" . $day['name'] . "2,
		                                                c.UserId = a.UserId,
		                                                c.TransDate = now()    
		                                            WHERE
		                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
		                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
		                                                AND a.Id = :MandateId
		                                                AND a.EndDate >= '2025-01-01'
		                                                AND ((c.StartTime != ParaStartTime" . $day['name'] . "2 ) || (c.EndTime != ParaEndTime" . $day['name'] . "2))

		                                                ");

		            // Bind parameters
		            
			        $updateStmtPM->bindParam(':MandateId', $MandateId, PDO::PARAM_INT);
		            $updateStmtPM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);

		   
		            
		            // Execute the update statement
		            $updateStmtPM->execute();

		            // echo "Processed " . $day['name'] . "<br>";
		        
		        } catch (PDOException $e) {
		            // Display error message for each day if it occurs
		            echo "Error processing " . $day['name'] . ": " . $e->getMessage() . "<br>";
		        }
		    }

		    // echo "Processing completed for all days.";

		} catch (PDOException $e) {
		    echo "Connection error: " . $e->getMessage();
		}

		// Close connection
		$conn = null;


	}	


		
?>


