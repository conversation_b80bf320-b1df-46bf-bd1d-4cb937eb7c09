<?php
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

require_once("db_GetSetData.php");

$username = '<EMAIL>';
$password = 'ovxbgkjvpqbvhcav';

$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
$inbox = imap_open($hostname, $username, $password) or die('Cannot connect to Gmail: ' . imap_last_error());

$emails = imap_search($inbox, 'ALL');

if ($emails) {
    rsort($emails);
    foreach ($emails as $email_number) {
        $overview = imap_fetch_overview($inbox, $email_number, 0);
        $subject = $overview[0]->subject ?? 'No Subject';
        $message_plain = imap_fetchbody($inbox, $email_number, 1);

        // Decode the email based on encoding
        if (isset($overview[0]->encoding)) {
            switch ($overview[0]->encoding) {
                case 3: // base64
                    $message_plain = imap_base64($message_plain);
                    break;
                case 4: // quoted-printable
                    $message_plain = quoted_printable_decode($message_plain);
                    break;
            }
        }

        $headers = imap_fetchheader($inbox, $email_number);
        $email_file_path = '../em/rn_confirmaiton_email.eml';
        file_put_contents($email_file_path, $headers . "\n" . $message_plain);

        // Process email content with Python script
        $command = escapeshellcmd("python3 ../em/import_confirmation_email.py " . $email_file_path);
        $output = shell_exec($command);
        $result = json_decode($output, true);

        print_r($result);
        echo"<br>";

        if (!$result) {
            echo "Error processing email: Unable to parse data.\n";
            continue;
        }

        $conf_number = $result["Confirmation #:"] ?? '';
        $rn_school_number = $result["School ID:"] ?? '';
        $rn_name = $result["Agency Nurse:"] ?? '';
        $coverage_type = $result["Coverage Type:"] ?? '';
        $osis_number = $result["Student ID:"] ?? '';
        $school_name = $result["School Name:"] ?? '';
        $start_date = date('Y-m-d', strtotime($result["Start Date:"] ?? ''));
        // $end_date = date('Y-m-d', strtotime($result["End Date:"] ?? ''));


        $parsed_end_date = strtotime($result["End Date:"] ?? '');
        $end_date = ($parsed_end_date && $parsed_end_date !== false && date('Y-m-d', $parsed_end_date) !== '1970-01-01')
            ? date('Y-m-d', $parsed_end_date)
            : '2025-06-26';

        // if (!$conf_number || !$rn_school_number || !$start_date || !$end_date) {
        if (!$conf_number || !$rn_school_number || !$start_date) {
 
            echo "Skipping email: Missing essential data.\n";
            continue;
        }


        // if (!$end_date) {

        //     $end_date = '2025-06-26';
        // }

        $period = ($start_date != '1969-12-31') ?
            (new DateTime($end_date))->diff(new DateTime($start_date))->format('%a') : -1;

        if ($period == -1) {
            echo "Skipping email: Invalid date range.\n";
            continue;
        }

 
        $service_category = ($osis_number !== '') ? 'Student' : 'School';
        $service_type_id = get_service_type($coverage_type, $service_category);

        $rn_name = removeCoverageFromRNName($rn_name);
        $name_parts = explode(",", $rn_name);
        $rn_first_name = isset($name_parts[1]) ? trim($name_parts[1]) : '';
        $rn_last_name = isset($name_parts[0]) ? trim($name_parts[0]) : '';
        $registrant_id = get_rn_id($rn_first_name, $rn_last_name);

        // Begin database transaction
        $conn = getCon();
        mysqli_autocommit($conn, false);

        try {
            if ($service_category == 'School') {
                set_conf_num_for_school_posted_sessions($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn);
                if ($period > 10) {
                    set_conf_num_for_school_assignments($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn);
                }
            } else {
                set_conf_num_for_student_assignments($conf_number, $rn_school_number, $service_type_id, $registrant_id, $osis_number, $start_date, $end_date, $conn);
                set_conf_num_for_student_posted_sessions($conf_number, $rn_school_number, $service_type_id, $osis_number, $start_date, $end_date, $conn);
            }

            mysqli_commit($conn);
        } catch (Exception $e) {
            mysqli_rollback($conn);
            echo "Transaction failed: " . $e->getMessage() . "\n";
        } finally {
            mysqli_autocommit($conn, true);
            setDisConn($conn);
        }
    }
}
imap_close($inbox);

echo "OK";

// Function Definitions
function get_service_type($coverage_type, $service_category) {
    if ($service_category == 'Student') {
        return ($coverage_type == '1:1') ? '43' : '44,45';
    } else {
        switch ($coverage_type) {
            case 'School Coverage': return '39';
            case 'After School': return '40';
            case 'Trip': return '41';
            case 'Special Events': return '42';
            default: return '';
        }
    }
}

function get_rn_id($rn_first_name, $rn_last_name) {
    $conn = getCon();
    $query = "
        SELECT Id AS RegistrantId
        FROM Registrants
        WHERE TypeId = 12 AND LastName = '{$rn_last_name}' AND FirstName = '{$rn_first_name}'
    ";
    $result = mysqli_query($conn, $query);
    $registrant_id = ($result && $row = mysqli_fetch_assoc($result)) ? $row['RegistrantId'] : '';
    setDisConn($conn);
    return $registrant_id;
}

function removeCoverageFromRNName($name) {
    return strpos($name, "Coverage") !== false ? substr($name, 0, strpos($name, "Coverage")) : $name;
}

function set_conf_num_for_school_posted_sessions($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn) {
    retry_query(function() use ($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn) {
        $query = "
            UPDATE WeeklyServices a
            JOIN SchSchools b ON a.SchoolId = b.Id AND b.SesisSchoolId = '{$rn_school_number}'
            SET ConfirmationNumber = '{$conf_number}'
            WHERE a.ServiceTypeId IN ({$service_type_id}) AND a.ServiceDate BETWEEN '{$start_date}' AND '{$end_date}'
        ";
        mysqli_query($conn, $query);
    });
}

function set_conf_num_for_student_posted_sessions($conf_number, $rn_school_number, $service_type_id, $osis_number, $start_date, $end_date, $conn) {
    retry_query(function() use ($conf_number, $rn_school_number, $service_type_id, $osis_number, $start_date, $end_date, $conn) {
        $query = "
            UPDATE WeeklyServices a
              JOIN SchSchools b ON a.SchoolId = b.Id AND b.SesisSchoolId = '{$rn_school_number}'
              JOIN SchStudents c ON a.StudentId = c.Id AND c.ExtId = '{$osis_number}'

            SET ConfirmationNumber = '{$conf_number}'
            WHERE a.ServiceTypeId IN ({$service_type_id}) AND a.ServiceDate BETWEEN '{$start_date}' AND '{$end_date}'
        ";
        mysqli_query($conn, $query);
    });
}

function set_conf_num_for_school_assignments($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn) {
    retry_query(function() use ($conf_number, $rn_school_number, $service_type_id, $start_date, $end_date, $conn) {
        $query = "
            UPDATE SchSchoolAssignmentHeader a
            JOIN SchSchools b ON a.SchoolId = b.Id AND b.SesisSchoolId = '{$rn_school_number}'
            SET ConfirmationNumber = '{$conf_number}'
            WHERE a.ServiceTypeId IN ({$service_type_id}) AND a.StartDate = '{$start_date}' AND a.EndDate = '{$end_date}'
        ";
        mysqli_query($conn, $query);
    });
}



function set_conf_num_for_student_assignments($conf_number, $rn_school_number, $service_type_id, $registrant_id, $osis_number, $start_date, $end_date, $conn) {
    retry_query(function() use ($conf_number, $rn_school_number, $service_type_id, $registrant_id, $osis_number, $start_date, $end_date, $conn) {
        $query = "
            UPDATE SchStudentAssignmentHeader a
            JOIN SchStudents c ON a.StudentId = c.Id AND c.ExtId = '{$osis_number}'
            JOIN SchSchools b ON c.SchoolId = b.Id AND b.SesisSchoolId = '{$rn_school_number}'
            SET a.ConfirmationNumber = '{$conf_number}'
            WHERE a.ServiceTypeId IN ({$service_type_id}) AND a.StartDate = '{$start_date}' AND a.EndDate = '{$end_date}'
        ";
        mysqli_query($conn, $query);
    });
}

function retry_query($query_function, $retry_count = 3) {
    $attempt = 0;
    while ($attempt < $retry_count) {
        try {
            $query_function();
            return;
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Deadlock') === false || ++$attempt >= $retry_count) {
                throw $e;
            }
        }
    }
}
?>
