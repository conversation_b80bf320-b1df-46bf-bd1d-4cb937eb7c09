<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$SchoolId = $_GET['SchoolId'];  
  $SubSchoolTypeId = $_GET['SubSchoolTypeId'];  

      $query = "	SELECT 	a.Id as SchoolHoursId,
  	                    a.Id AS id,
  	                    b.WeekDayId,
  	                    b.WeekDay,
  	                    '{$SchoolId}' as SchoolId,
                       CASE   
                        WHEN a.StartTime THEN a.StartTime
                        ELSE '07:00:00'
                      END AS StartTime,

                      CASE  
                        WHEN a.EndTime THEN a.EndTime 
                        ELSE '15:00:00'
                      END AS EndTime,


                      CASE   
                        WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' ) 
                        ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )  
                      END AS StartTimeFrm,
                      
                      CASE   
                        WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' ) 
                        ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
                      END AS EndTimeFrm,



                      CASE   
                        WHEN a.TotalHours THEN a.TotalHours 
                        ELSE '8.00'
                      END AS TotalHours,
                  COALESCE(CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ),'') AS UserName,
                  COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                          FROM  SchSchoolHours a

              RIGHT JOIN DaysOfWeek5 b
              ON a.WeekDayId = b.WeekDayId
              AND a.SchoolId = '{$SchoolId}'
              AND a.SubSchoolTypeId = '{$SubSchoolTypeId}' 
              AND b.WeekDayId NOT IN (0,6)


              LEFT JOIN Users c 
              ON a.Userid = c.Userid   	  ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

  
?>

 