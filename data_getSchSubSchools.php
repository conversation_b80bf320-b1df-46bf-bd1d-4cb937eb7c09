<?php 


	require_once("db_GetSetData.php");

	$conn = getCon();

    $SubSchoolTypeId = $_GET['SubSchoolTypeId'];  
    if (!$SubSchoolTypeId) {

    	$SubSchoolTypeId = 0;
    }

	    $query = "SELECT 	a.Id as id, 
											a.Id, 
											CONCAT(TRIM(c.SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
											c.SchoolName as SchoolNameDBN,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId
											 
								FROM        SchSchools a,
											SchDistricts b,
											SchSubSchools c
								WHERE       SubSchoolTypeId = '{$SubSchoolTypeId}'
							    AND			a.DistrictId = b.Id	
							    AND         a.Id = c.SchoolId
							    
							    ORDER BY    TRIM(c.SchoolName) ";	 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

 

?>



 