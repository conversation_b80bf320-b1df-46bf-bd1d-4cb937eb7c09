/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchRegistrantRsa7aFormDetails$$

CREATE PROCEDURE proc_getSchRegistrantRsa7aFormDetails (IN p_registrant_id INT,
                                                           p_mandate_id INT,
                                                           p_form_year varchar(4),
                                                           p_form_month varchar(2),
                                                           p_form_type_id INT,
                                                           p_bi_monthly_period INT 
                                                      )  

BEGIN

      DECLARE v_Session_Delivery_Sel VARCHAR(96); 
      DECLARE v_Bi_Monthly_Sel VARCHAR(96); 


	IF p_form_type_id = '1' THEN
	   set v_Session_Delivery_Sel = "SessionDeliveryModeId = 'I' ";
	ELSE
	   set v_Session_Delivery_Sel = "SessionDeliveryModeId != 'I' ";
	END IF;
 

	IF p_bi_monthly_period = '1' THEN
	   set v_Bi_Monthly_Sel = "DAY(ServiceDate) < '16'";
	ELSE
	   set v_Bi_Monthly_Sel = "DAY(ServiceDate) > '15'";
	END IF;

    /* Therapy Session Notes  */ 
    /*=========================*/
   SET @Query = CONCAT(
    "SELECT   DISTINCT DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
                            a.ServiceDate as ServiceDateSort,
                            DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate,
                            a.StartTime as StartTimeSort,
                            DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                            DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                            a.SessionGrpSize
                         
                        FROM    WeeklyServices a                   
                            Where RegistrantId = ",p_registrant_id, " 
                            AND   MandateId = ", p_mandate_id, "  
                            AND   ScheduleStatusId >= '7'
                            AND   YEAR(ServiceDate) = ", p_form_year, " 
                            AND   MONTH(ServiceDate) = ", p_form_month, "    
                            AND ",   v_Bi_Monthly_Sel  ," 
                            AND ",   v_Session_Delivery_Sel, "                     
                    ORDER BY ServiceDateSort, StartTimeSort" );  

   
 
 
   PREPARE queryStmt FROM @Query;  

 
   EXECUTE queryStmt;  
 DEALLOCATE PREPARE queryStmt;    

 

  
END $$

DELIMITER ;  
