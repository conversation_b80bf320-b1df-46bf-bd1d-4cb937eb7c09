<?php 


	require_once("db_GetSetData.php"); 	

	$conn = getCon();

	$ScheduleId = $_POST['ScheduleId'];
	$BillingComments = $_POST['BillingComments'];
	$UserId = $_POST['UserId'];

    $query = "	
    UPDATE WeeklyServices
        SET BillingComments = '{$BillingComments}',
            UserId = '{$UserId}',
            TransDate = NOW()
    WHERE Id = '{$ScheduleId}'        
     ";

	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	echo $ret;

?>

