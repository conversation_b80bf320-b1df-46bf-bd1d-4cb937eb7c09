<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 



	$ServiceDate = $_POST['ServiceDate'];
	$PayrollWeek = $_POST['PayrollWeek'];
	$WeekDay = $_POST['WeekDay'];
	$StartTime = $_POST['StartTime'];
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$GroupSize = $_POST['GroupSize'];
	$SessionDeliveryModeId = $_POST['SessionDeliveryModeId'];

	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);

	$UserId = $_POST['UserId'];
	
	
	//===============================

	$Check_Dup = $Data;

	
	foreach ($Check_Dup as $MandateId) {
			

		/*========= Check Duplicate Sessions =========*/
		$result_error = $rcr_transaction->getSchStudentCheckDupSessions( 	$MandateId,
																				$ServiceDate,
																				$StartTime,
																				$EndTime);


		if ($result_error[0]["Dup_Count"] != 0) {

			echo  'error_dup';
			return; 
		}

		/*===========================================*/



		/*========= Check Mandate riched Max Freq. Per Week =========*/
		$result_error = $rcr_transaction->getSchMandateRemainingFreq( 	$MandateId,
																		$PayrollWeek
																		);


		if ($result_error[0]["RemaininqFreq"] < 1) {

			echo  'error_freq';
			return; 
		}

		/*===========================================*/

	}
 

	//===============================

	 

	foreach ($Data as $MandateId) {
			

	 
		$result = $rcr_transaction->setSchRegistrantVerifiedMandatedSessions( 	$ServiceDate,
																				$PayrollWeek,
																				$WeekDay,
																				$MandateId,
																				$StartTime,
																				$EndTime,
																				$TotalHours,
																				$GroupSize,	
																				$SessionDeliveryModeId,	
																				$UserId); 
																	

	 
		} 
	
	$rcr_transaction->disconnectDB (); 

	echo 'OK';

?>
