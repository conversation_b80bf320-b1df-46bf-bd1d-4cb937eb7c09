
<?php 
	
	require_once("db_GetSetData.php");

	$conn = getCon();

	$StudentId = $_GET['StudentId'];
	$SchoolSeasonId = $_GET['SchoolSeasonId'];

	if ($StudentId) {
  						$query = "SELECT 	a.Id as id,
						a.Id as MandateId,
						StudentId, 
						b.SearchId as StudentSearchId,
						StudentExtId,
					    b.<PERSON>,
                        b.FirstName,
					    CONCAT( trim( b.LastName) , ', ', trim(b.FirstName),' Guardian Info : ', GuardianName, ' ',GuardianPhone, ' ',GuardianEmail ) as StudentName, 						
						COALESCE(CONCAT(trim(b.StreetAddress1), ' ',trim(b.StreetAddress2),' ', b.City, ', NY ',b.<PERSON>, ' ',b.MobilePhone, ' ',b.HomePhone  ),'') as StudentInfo,
                        DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                        b.<PERSON>,
                        b.<PERSON>,
 						CASE a.SchoolId 
							WHEN '0' THEN ''
						ELSE a.SchoolId
						END AS SchoolId,

					   COALESCE((SELECT CONCAT(trim(e.SchoolName),
		               ' (District: ',DistrictName,') ',
		               trim(e.StreetAddress1),' ', e.City,' ', e.State,' ', e.ZipCode,' ',
		               e.LiaisonFirstName, ' ',  e.LiaisonLastName, ' ',
		               e.OfficePhone,' ', e.MobilePhone,' ',e.Email, ' ', 
    		           e.LiaisonFirstName2, ' ',  e.LiaisonLastName2,  ' ',
            		   e.OfficePhone2,' ', e.MobilePhone2,' ',e.Email2  


		               ) 
					      from SchSchools c, SchDistricts d, SchSubSchools e
					   WHERE b.SchoolId = c.Id 
					   AND c.DistrictId = d.Id
					   AND b.SchoolId = e.SchoolId
					   AND e.SubSchoolTypeId = b.SubSchoolTypeId  
					   ),'') as SchoolName,   
					/*   
						CONCAT(trim(OverrideSchoolName), ' ', trim(OverrideDistrict),' ',trim(OverrideSchoolAddress1),' ', OverrideSchoolCity,' ', OverrideSchoolState,' ', OverrideSchoolZipCode,' ', 
						OverrideSchoolContactName,' ', OverrideSchoolContactPhone) 
						      as OverrideSchoolInfo,  

						OverrideSchoolName,
					*/	
						ServiceTypeDesc,  
 						ServiceTypeId,
						a.StatusId , 
						SECMandateStatus,
						DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
						DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,

 						SECMandateStatus,
						DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
						
						CASE a.ParaTransportPerc
						WHEN '0' THEN CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize )
						ELSE CONCAT( SessionFrequency , ' X ', ParaTransportPerc , ' %')
						END AS MandateDesc,
						
                        OverrideSchoolContactName,
                        OverrideSchoolContactPhone,
						
						Language,
                        CONCAT( trim( GuardianFirstName) , ' ', trim(GuardianLastName)) as GuardianName, 
                        GuardianPhone,  
                        OverrideSchoolContactName,
                        OverrideSchoolContactPhone,
						
						/*CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName, */
						
						COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
						      from Registrants c, RegistrantTypes f 
						   WHERE a.RegistrantId = c.Id 
						   AND c.TypeId = f.Id),'') as RegistrantName,  


              			a.RegistrantId,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						DOESchoolName,

						SESISParaServiceTypeId,

						
						DATE_FORMAT( ParaStartTimeMon1, '%l:%i %p' ) as ParaStartTimeMon1,
						DATE_FORMAT( ParaEndTimeMon1, '%l:%i %p' ) as ParaEndTimeMon1,
						ParaTotalHoursMon1,
						DATE_FORMAT( ParaStartTimeMon2, '%l:%i %p' ) as ParaStartTimeMon2,
						DATE_FORMAT( ParaEndTimeMon2, '%l:%i %p' ) as ParaEndTimeMon2,
						ParaTotalHoursMon2,

			            DATE_FORMAT( ParaStartTimeTue1, '%l:%i %p' ) as ParaStartTimeTue1,
			            DATE_FORMAT( ParaEndTimeTue1, '%l:%i %p' ) as ParaEndTimeTue1,
			            ParaTotalHoursTue1,
			            DATE_FORMAT( ParaStartTimeTue2, '%l:%i %p' ) as ParaStartTimeTue2,
			            DATE_FORMAT( ParaEndTimeTue2, '%l:%i %p' ) as ParaEndTimeTue2,
			            ParaTotalHoursTue2,

			            DATE_FORMAT( ParaStartTimeWed1, '%l:%i %p' ) as ParaStartTimeWed1,
			            DATE_FORMAT( ParaEndTimeWed1, '%l:%i %p' ) as ParaEndTimeWed1,
			            ParaTotalHoursWed1,
			            DATE_FORMAT( ParaStartTimeWed2, '%l:%i %p' ) as ParaStartTimeWed2,
			            DATE_FORMAT( ParaEndTimeWed2, '%l:%i %p' ) as ParaEndTimeWed2,
			            ParaTotalHoursWed2,

		                DATE_FORMAT( ParaStartTimeThu1, '%l:%i %p' ) as ParaStartTimeThu1,
		                DATE_FORMAT( ParaEndTimeThu1, '%l:%i %p' ) as ParaEndTimeThu1,
   		                ParaTotalHoursThu1,
	                    DATE_FORMAT( ParaStartTimeThu2, '%l:%i %p' ) as ParaStartTimeThu2,
	                    DATE_FORMAT( ParaEndTimeThu2, '%l:%i %p' ) as ParaEndTimeThu2,
	                    ParaTotalHoursThu2,

	                    DATE_FORMAT( ParaStartTimeFri1, '%l:%i %p' ) as ParaStartTimeFri1,
	                    DATE_FORMAT( ParaEndTimeFri1, '%l:%i %p' ) as ParaEndTimeFri1,
	                    ParaTotalHoursFri1,
	                    DATE_FORMAT( ParaStartTimeFri2, '%l:%i %p' ) as ParaStartTimeFri2,
	                    DATE_FORMAT( ParaEndTimeFri2, '%l:%i %p' ) as ParaEndTimeFri2,
	                    ParaTotalHoursFri2,
						
						a.UserId , 
						DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as TransDate 

				FROM 	SchStudentMandates a, 
						SchStudents b,
						SchSchoolYear c,
						SchServiceTypes f,
					    Users e
					WHERE a.StudentId = '{$StudentId}'    
					AND a.StudentId = b.Id	
					AND a.StatusId = '1' 
					AND a.ServiceTypeId = f.Id
					AND a.UserId = e.UserId
					AND a.Statusid = '1'
					-- AND a.AssignmentGeneratedFL != 1
					AND SESISParaServiceTypeId != 0 
					AND c.Id = '{$SchoolSeasonId}'
					AND a.RegistrantId = 0
					AND StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate

				ORDER BY ServiceTypeId, b.LastName, b.FirstName	  "; 


					} else {

  						$query = "SELECT 	a.Id as id,
						a.Id as MandateId,
						StudentId, 
						b.SearchId as StudentSearchId,
						StudentExtId,
						b.LastName,
                        b.FirstName,
						CONCAT( trim( b.LastName) , ', ', trim(b.FirstName),' Guardian Info : ', CONCAT( trim( GuardianFirstName) , ' ', trim(GuardianLastName)), ' ',GuardianPhone, ' ',GuardianEmail ) as StudentName, 						
						COALESCE(CONCAT(trim(b.StreetAddress1), ' ',trim(b.StreetAddress2),' ', b.City, ', NY ',b.ZipCode, ' ',b.MobilePhone, ' ',b.HomePhone  ),'') as StudentInfo,
                        DATE_FORMAT( b.DateOfBirth, '%m-%d-%Y' ) as DateOfBirth,
                        b.MedicalNeeds,
                        b.Comments,
 						CASE a.SchoolId 
							WHEN '0' THEN ''
						ELSE a.SchoolId
						END AS SchoolId,

					   COALESCE((SELECT CONCAT(trim(e.SchoolName),
		               ' (District: ',DistrictName,') ',
		               trim(e.StreetAddress1),' ', e.City,' ', e.State,' ', e.ZipCode,' ',
		               e.LiaisonFirstName, ' ',  e.LiaisonLastName, ' ',
		               e.OfficePhone,' ', e.MobilePhone,' ',e.Email,  ' ',
    		           e.LiaisonFirstName2, ' ',  e.LiaisonLastName2,  ' ',
            		   e.OfficePhone2,' ', e.MobilePhone2,' ',e.Email2  


		                ) 
					      from SchSchools c, SchDistricts d, SchSubSchools e
					   WHERE b.SchoolId = c.Id 
					   AND c.DistrictId = d.Id
					   AND b.SchoolId = e.SchoolId
					   AND e.SubSchoolTypeId = b.SubSchoolTypeId  
					   ),'') as SchoolName,   
					/*   
						CONCAT(trim(OverrideSchoolName), ' ', trim(OverrideDistrict),' ',trim(OverrideSchoolAddress1),' ', OverrideSchoolCity,' ', OverrideSchoolState,' ', OverrideSchoolZipCode,' ', 
						OverrideSchoolContactName,' ', OverrideSchoolContactPhone) 
						      as OverrideSchoolInfo,  

						OverrideSchoolName,
					*/	
						ServiceTypeDesc,  
 						ServiceTypeId,
						a.StatusId , 
						SECMandateStatus,
						DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
						DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,

 						SECMandateStatus,
						DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
						
						CASE a.ParaTransportPerc
						WHEN '0' THEN CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize )
						ELSE CONCAT( SessionFrequency , ' X ', ParaTransportPerc , ' %')
						END AS MandateDesc,
						
						
						Language,
                        CONCAT( trim( GuardianFirstName) , ' ', trim(GuardianLastName)) as GuardianName, 
                        GuardianPhone,  
                        OverrideSchoolContactName,
                        OverrideSchoolContactPhone,
						
						/*CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName, */
						
						COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
						      from Registrants c, RegistrantTypes f 
						   WHERE a.RegistrantId = c.Id 
						   AND c.TypeId = f.Id),'') as RegistrantName,  


              			a.RegistrantId,
						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						DOESchoolName,

						SESISParaServiceTypeId,

						
						DATE_FORMAT( ParaStartTimeMon1, '%l:%i %p' ) as ParaStartTimeMon1,
						DATE_FORMAT( ParaEndTimeMon1, '%l:%i %p' ) as ParaEndTimeMon1,
						ParaTotalHoursMon1,
						DATE_FORMAT( ParaStartTimeMon2, '%l:%i %p' ) as ParaStartTimeMon2,
						DATE_FORMAT( ParaEndTimeMon2, '%l:%i %p' ) as ParaEndTimeMon2,
						ParaTotalHoursMon2,

			            DATE_FORMAT( ParaStartTimeTue1, '%l:%i %p' ) as ParaStartTimeTue1,
			            DATE_FORMAT( ParaEndTimeTue1, '%l:%i %p' ) as ParaEndTimeTue1,
			            ParaTotalHoursTue1,
			            DATE_FORMAT( ParaStartTimeTue2, '%l:%i %p' ) as ParaStartTimeTue2,
			            DATE_FORMAT( ParaEndTimeTue2, '%l:%i %p' ) as ParaEndTimeTue2,
			            ParaTotalHoursTue2,

			            DATE_FORMAT( ParaStartTimeWed1, '%l:%i %p' ) as ParaStartTimeWed1,
			            DATE_FORMAT( ParaEndTimeWed1, '%l:%i %p' ) as ParaEndTimeWed1,
			            ParaTotalHoursWed1,
			            DATE_FORMAT( ParaStartTimeWed2, '%l:%i %p' ) as ParaStartTimeWed2,
			            DATE_FORMAT( ParaEndTimeWed2, '%l:%i %p' ) as ParaEndTimeWed2,
			            ParaTotalHoursWed2,

		                DATE_FORMAT( ParaStartTimeThu1, '%l:%i %p' ) as ParaStartTimeThu1,
		                DATE_FORMAT( ParaEndTimeThu1, '%l:%i %p' ) as ParaEndTimeThu1,
   		                ParaTotalHoursThu1,
	                    DATE_FORMAT( ParaStartTimeThu2, '%l:%i %p' ) as ParaStartTimeThu2,
	                    DATE_FORMAT( ParaEndTimeThu2, '%l:%i %p' ) as ParaEndTimeThu2,
	                    ParaTotalHoursThu2,

	                    DATE_FORMAT( ParaStartTimeFri1, '%l:%i %p' ) as ParaStartTimeFri1,
	                    DATE_FORMAT( ParaEndTimeFri1, '%l:%i %p' ) as ParaEndTimeFri1,
	                    ParaTotalHoursFri1,
	                    DATE_FORMAT( ParaStartTimeFri2, '%l:%i %p' ) as ParaStartTimeFri2,
	                    DATE_FORMAT( ParaEndTimeFri2, '%l:%i %p' ) as ParaEndTimeFri2,
	                    ParaTotalHoursFri2,
						
						a.UserId , 
						DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as TransDate 

				FROM 	SchStudentMandates a, 
						SchStudents b,
						SchSchoolYear c,
						SchServiceTypes f,
					    Users e
					WHERE a.StudentId = b.Id	
					AND a.ServiceTypeId = f.Id
					AND a.UserId = e.UserId
					AND a.Statusid = '1'
					AND SESISParaServiceTypeId != 0 
					-- AND a.AssignmentGeneratedFL != 1
					AND c.Id = '{$SchoolSeasonId}'
					AND a.RegistrantId = 0
					AND StartDate between SchoolSeasonStartDate and SchoolSeasonEndDate

				ORDER BY ServiceTypeId, b.LastName, b.FirstName	  "; 
    }

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
 


/*
	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$StudentId = $_GET['StudentId'];
	$SchoolSeasonId = $_GET['SchoolSeasonId'];
 
 

	$result = $rcr_transaction->getSchStudentOpenParaMandates($StudentId, $SchoolSeasonId);


	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/
?>

 