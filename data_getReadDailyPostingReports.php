<?php

    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);

    // require_once("db_GetSetData.php");
    // $conn = getCon();

    // $query1 ="SELECT UserId, AppPassword FROM SchGmaiAccounts where Id = '1'";
    // $result1 =  mysqli_query($conn, $query1) or die("Error in Selecting " . mysqli_error($conn));

    // while ($row1 = $result1->fetch_assoc()) {
    //     $username = $row1['UserId'];    
    //     $password = $row1['AppPassword'];
    // }
    // setDisConn($conn);


    $username = '<EMAIL>';
    $password = 'dbmnvusghphictlf ';


    $hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
    
    $inbox = imap_open($hostname, $username, $password) or die('Cannot connect to Gmail: ' . imap_last_error());

    // Search for unread emails
    $emails = imap_search($inbox, 'UNSEEN');



    if ($emails) {
        rsort($emails); // Sort emails from newest to oldest
        
        foreach ($emails as $email_number) {
            $structure = imap_fetchstructure($inbox, $email_number);
            


            if (isset($structure->parts) && count($structure->parts)) {
                for ($i = 0; $i < count($structure->parts); $i++) {
                    $attachments = $structure->parts[$i];
                    
                     // print_r($structure->parts[$i]);
                    


                    if ($attachments->ifdisposition && $attachments->disposition == "ATTACHMENT") {
                        $attachmentName = $attachments->dparameters[1]->value;
                        $fileExtension = strtolower(pathinfo($attachmentName, PATHINFO_EXTENSION));
                        


                        // Check if the attachment is a CSV file
                        if ($fileExtension == 'csv') {
                            $attachment = imap_fetchbody($inbox, $email_number, $i + 1);
                            
                            // Decode the attachment based on its encoding
                            if ($attachments->encoding == 3) { // 3 = BASE64
                                $attachment = base64_decode($attachment);
                            } elseif ($attachments->encoding == 4) { // 4 = QUOTED-PRINTABLE
                                $attachment = quoted_printable_decode($attachment);
                            }
                            
                            // Save the attachment to a file


                            $targetDirectory = __DIR__ . '/../rn_reports/';


                            file_put_contents($targetDirectory . $attachmentName, $attachment);
                            echo "Downloaded to rn_reports: " . $attachmentName . "\n";
                        }
                    }
                }
            }
        }
    }

    // Close the IMAP connection
    imap_close($inbox);
?>

 