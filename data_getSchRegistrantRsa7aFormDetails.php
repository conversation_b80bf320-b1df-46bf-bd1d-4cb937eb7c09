<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
    $MandateId = $_GET['MandateId'];  
	$Year = $_GET['Year'];
	$Month = $_GET['Month'];
    $FormTypeId = $_GET['FormTypeId'];
    $BiMonthlyFL = $_GET['BiMonthlyFL'];
  

     $query = "call  proc_getSchRegistrantRsa7aFormDetails (' {$RegistrantId}',
                                                             '{$MandateId}',
                                                             '{$Year}',
                                                             '{$Month}',
                                                             '{$FormTypeId}' ,
                                                             '{$BiMonthlyFL}'  
                                                                      )  
                " ;              
    


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

