<?php 
	

require_once("db_GetSetData.php");

	$conn = getCon();

	$SchoolId = $_GET['SchoolId'];  
    $SubSchoolTypeId = $_GET['SubSchoolTypeId'];  

    if (!$SubSchoolTypeId) {


    	$SubSchoolTypeId = 0;
    }


      $query = "	SELECT 	a.Id as id, 
							a.Id, 
							a.<PERSON>t<PERSON>d,
							a.<PERSON>d,
							a.<PERSON>d,
							a.SchoolName as SchoolNameDBN,
							d.SchoolName,
							CONCAT(TRIM(d.SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
							SchoolTypeId,
							SchoolTypeDesc,
							DistrictId,
							d.<PERSON>ddress1,
							d.<PERSON>ddress2,
							d.City,
							d.State,
							d.<PERSON>,

							d.<PERSON>isonF<PERSON>tName,
							d.LiaisonLastName,
							d.OfficePhone,
							d.MobilePhone,
							d.Fax,
							d.<PERSON>,

							d.<PERSON>F<PERSON>tName2,
							d.<PERSON>astName2,
							d.<PERSON>,
							d.<PERSON>,
							d.Fax2,
							d<PERSON>,


							d<PERSON>,
							a<PERSON>,
							a.<PERSON>d,
							a.<PERSON>,
							a.<PERSON>ate

						FROM 	SchSchools a, 
								<PERSON>hSchoolTypes b,
								SchDistricts c,
								SchSubSchools d
							where a.Id = '{$SchoolId}' 
							and   a.Id = d.SchoolId
                            and  d.SubSchoolTypeId = '{$SubSchoolTypeId}'
							AND a.SchoolTypeId = b.Id
							AND a.DistrictId = c.Id	  ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;


 
  
?>
