	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchSchoolboardSchoolsDateRangeSchedules$$

	CREATE PROCEDURE proc_getSchSchoolboardSchoolsDateRangeSchedules (IN p_from_date DATE,
																		 p_to_date DATE,
																		 p_district_id INT
														         	)  



	BEGIN

		create temporary table tmp
		(
			 
		 		ScheduleId BIGINT, 
		 		ScheduleStatusId INT, 
		 		ScheduleStatusDesc VARCHAR(32),
		 		TextColor VARCHAR(32),
		  		BackgroundColor VARCHAR(32),
		 		ConfirmationNumber VARCHAR(32),
		 		ServiceDate VARCHAR(32), 
		 		StartTimeNum TIME,
		 		EndTimeNum TIME, 
		  		StartTime VARCHAR(12),
		 		EndTime VARCHAR(12),
				TotalHours DECIMAL(5,2), 
				WeekDay VARCHAR(5), 
				PayrollWeek DATE,
				SchoolId INT, 
				SchoolName VARCHAR(96),   	
				DistrictName VARCHAR(48),
				SchoolSearchId VARCHAR(48),  
				RegistrantId INT, 
				RegistrantName VARCHAR(48),   					
				PrimaryVendor<PERSON> CHAR(1),   					
				DistrictId INT,
				ServiceTypeId INT,
				ServiceTypeDesc VARCHAR(32),
				RegistrantTypeId INT,
				AssignmentTypeId INT,
				LastMessage VARCHAR(128), 
				MessagesCount INT,
				UserName VARCHAR(96),  
				TransDate VARCHAR(16)	


		);



		INSERT INTO tmp

    SELECT      a.Id AS ScheduleId, 
            ScheduleStatusId, 
            ScheduleStatusDesc,
            TextColor,
            BackgroundColor,
        
            a.ConfirmationNumber,
            DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
            StartTime as StartTimeNum,
              EndTime as EndTimeNum,
            DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
            DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
            a.TotalHours, 
            a.WeekDay, 
            a.PayrollWeek,
            a.SchoolId,
            SchoolName,
            DistrictName,
        /*    
            COALESCE((SELECT SchoolName
              FROM SchSchools f
              WHERE a.SchoolId = f.Id
              ),''),   


            COALESCE((SELECT DistrictName
              FROM SchSchools f, SchDistricts h
              WHERE a.SchoolId = f.Id
              AND   f.DistrictId = g.Id),''),   
		*/
            d.SearchId,
            a.RegistrantId, 
            COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', f.RegistrantTypeDesc,')' )
              FROM Registrants c, RegistrantTypes f
              WHERE a.Registrantid = c.Id
              AND b.RegistrantTypeId = f.Id),'') as RegistrantName,   
            
            COALESCE((SELECT h.PrimaryVendorFL
              FROM SchDistrictServiceDetails h
              WHERE d.DistrictId = h.DistrictId
              AND a.ServiceTypeId = h.ServiceTypeId),'0') as PrimaryVendorFL,   
            
            d.DistrictId,
            a.ServiceTypeId,
            b.ServiceTypeDesc,
            b.RegistrantTypeId,
            a.AssignmentTypeId,
            '',
            0,
            CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
            a.TransDate


    FROM  WeeklyServices a, 
        SchServiceTypes b,
        SchSchools d,
        Users e,
        ScheduleStatuses g,
        SchDistricts h
    WHERE   a.ServiceDate between  p_from_date and p_to_date 
      AND a.UserId = e.UserId
      AND ScheduleStatusId = g.Id 
      AND a.ServiceTypeId = b.Id  
      AND b.ServiceCategoryId = 0
      AND a.SchoolId = d.Id 
      AND d.DistrictId = h.Id
      AND d.DistrictId = p_district_id
	;

								 

		/* Set Last Message*/
		/*================================*/
 	 
		Update  tmp a
		  Set LastMessage =  COALESCE(( SELECT Msg
				FROM WeeklyServicesMessages b
				WHERE b.Id = ( SELECT max( c.Id )
					FROM WeeklyServicesMessages c
					WHERE c.ScheduleId = a.ScheduleId )),'') ;
	 	
		/* Set Messages Count*/
		/*================================*/
 	 
		Update  tmp a
		  Set MessagesCount =  ( SELECT COUNT(*)
				FROM WeeklyServicesMessages b			
					WHERE a.ScheduleId = b.ScheduleId ) ;
 	
	 
	



		SELECT * FROM tmp
		ORDER BY DistrictName, ScheduleStatusDesc
		;	


		drop temporary table if exists tmp;
		 
		
	END $$

	DELIMITER ;		
	 