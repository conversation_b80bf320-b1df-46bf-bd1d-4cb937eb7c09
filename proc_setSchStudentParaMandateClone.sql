  /*=========================================*/

  DELIMITER $$

  DROP PROCEDURE IF EXISTS proc_setSchStudentParaMandateClone$$

  CREATE PROCEDURE proc_setSchStudentParaMandateClone (IN p_mandate_id INT, 
                                                          p_start_date DATE, 
                                                          p_end_date DATE, 
                                                          p_registrant_id INT,   
                                                          p_user_id INT ) 
                                     
  

BEGIN


  DECLARE v_Mandate_Id INT; 
  DECLARE v_Student_Id INT;   
  DECLARE v_Search_Id VARCHAR(45);
 

  SELECT rand()  INTO v_Search_Id ;

  /*===== Cancel Fully Confirmed Transactions For the Priginal Mandate    ======*/

      UPDATE WeeklyServices a, SchStudentAssignmentHeader b
        SET   a.ScheduleStatusId = '4'
      WHERE   b.MandateId = p_mandate_id
      AND     a.AssignmentId = b.Id
      AND     a.ScheduleStatusId = '7'
      AND     a.ServiceDate >= p_start_date
       ;  


  /*===== Select New End Date for selected Mandate  ======*/

      UPDATE SchStudentMandates
        SET EndDate  = DATE_ADD(p_start_date, INTERVAL -1 DAY)
      WHERE   Id = p_mandate_id;  


  /*===== Select New End Date for related Assignment  ======*/

      UPDATE SchStudentAssignmentHeader
        SET EndDate  = DATE_ADD(p_start_date, INTERVAL -1 DAY)
      WHERE   MandateId = p_mandate_id;  


  /*===== Clone New Mandate based on selected Mandate  ======*/

      INSERT INTO SchStudentMandates
      (
        StudentExtId,
        StudentId,
        SchoolId,
        SearchId,
        StatusId,
        SECMandateStatus,
        ServiceTypeId,
        ConfirmationNumber,
        StartDate,
        EndDate,
        RegistrantId,
        RegistrantExtId,
        RegistrantExtLastName,
        RegistrantExtFirstName,
        PendingProviderId,
        SessionFrequency,
        SessionLength,
        SessionLengthMin,
        SessionGrpSize,
        Language,
        CallInDate,
        DOEAssignmentDate,
        DOEComplianceDate,
        DOESchoolName,
        PlaceOfService,
        DOEProvider,
        DOEServiceTypeId,
        DOEServiceTypeDesc,
        DOEFirstAttendDate,
        DOEStartDate,
        DOESchoolId, 
        BillingContractId,
        ParaTransportPerc,
        AssignmentGeneratedFL,
        ParaStartTimeMon1,
        ParaEndTimeMon1,
        ParaTotalHoursMon1,
        ParaStartTimeMon2,
        ParaEndTimeMon2,
        ParaTotalHoursMon2,
        UserId,
        TransDate,
        ParaStartTimeFri1,
        ParaStartTimeFri2,
        ParaStartTimeThu2,
        ParaStartTimeThu1,
        ParaStartTimeWed1,
        ParaStartTimeWed2,
        ParaStartTimeTue2,
        ParaStartTimeTue1,
        ParaEndTimeFri1,
        ParaEndTimeFri2,
        ParaEndTimeThu2,
        ParaEndTimeThu1,
        ParaEndTimeWed1,
        ParaEndTimeWed2,
        ParaEndTimeTue2,
        ParaEndTimeTue1,
        ParaTotalHoursFri1,
        ParaTotalHoursFri2,
        ParaTotalHoursThu2,
        ParaTotalHoursThu1,
        ParaTotalHoursWed1,
        ParaTotalHoursWed2,
        ParaTotalHoursTue2,
        ParaTotalHoursTue1,
        ParaExtdHoursAuthFL,
        ClonedMandateFL
      )

  SELECT    
        a.StudentExtId,
        a.StudentId,
        a.SchoolId,
        v_Search_Id,
        a.StatusId,
        a.SECMandateStatus,
        a.ServiceTypeId,
        a.ConfirmationNumber,
        p_start_date,
        p_end_date,
        p_registrant_id,
        b.ExtId,
        b.LastName,
        b.FirstName,
        a.PendingProviderId,
        a.SessionFrequency,
        a.SessionLength,
        a.SessionLengthMin,
        a.SessionGrpSize,
        a.Language,
        a.CallInDate,
        a.DOEAssignmentDate,
        a.DOEComplianceDate,
        a.DOESchoolName,
        a.PlaceOfService,
        '',
        a.DOEServiceTypeId,
        a.DOEServiceTypeDesc,
        a.DOEFirstAttendDate,
        p_start_date,
        a.DOESchoolId, 
        a.BillingContractId,
        a.ParaTransportPerc,
        a.AssignmentGeneratedFL,
        a.ParaStartTimeMon1,
        a.ParaEndTimeMon1,
        a.ParaTotalHoursMon1,
        a.ParaStartTimeMon2,
        a.ParaEndTimeMon2,
        ParaTotalHoursMon2,
        p_user_id,
        now(),
        ParaStartTimeFri1,
        ParaStartTimeFri2,
        ParaStartTimeThu2,
        ParaStartTimeThu1,
        ParaStartTimeWed1,
        ParaStartTimeWed2,
        ParaStartTimeTue2,
        ParaStartTimeTue1,
        ParaEndTimeFri1,
        ParaEndTimeFri2,
        ParaEndTimeThu2,
        ParaEndTimeThu1,
        ParaEndTimeWed1,
        ParaEndTimeWed2,
        ParaEndTimeTue2,
        ParaEndTimeTue1,
        ParaTotalHoursFri1,
        ParaTotalHoursFri2,
        ParaTotalHoursThu2,
        ParaTotalHoursThu1,
        ParaTotalHoursWed1,
        ParaTotalHoursWed2,
        ParaTotalHoursTue2,
        ParaTotalHoursTue1,
        '0',
        '1' 
   FROM SchStudentMandates a, Registrants b
   WHERE a.Id = p_mandate_id
   AND   b.Id = p_registrant_id
   ;     

   SELECT Id, StudentId  into v_Mandate_Id, v_Student_Id from SchStudentMandates
   WHERE SearchId = v_Search_Id;
   

   CALL proc_setSchGenerateStudentAssignmentsFromMandate (v_Mandate_Id, v_Student_Id,  p_user_id); 

   select v_Mandate_Id as MandateId, p_start_date as StartDate;

END $$

  DELIMITER ;   

 