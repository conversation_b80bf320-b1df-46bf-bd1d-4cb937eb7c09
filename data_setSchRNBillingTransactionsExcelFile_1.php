<?php 
	
    error_reporting(E_ALL);
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
    
    $charset = 'utf8mb4';

 
  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];
  
  $BillingIssueCategoryId = $_GET['BillingIssueCategoryId'];
  $SchoolId = $_GET['SchoolId'];
  $ServiceTypeId = $_GET['ServiceTypeId'];
  $RnId = $_GET['RnId'];
  $RNLiaisonId = $_GET['RNLiaisonId'];
  $PostingStatusId = $_GET['PostingStatusId'];
  $ConfirmationNumber = $_GET['ConfirmationNumber'];
  $StudentName = $_GET['StudentName'];
 
  if (!$ConfirmationNumber) {

    $ConfirmationNumber = '';

  }


   
  if (!$BillingIssueCategoryId) {

    $BillingIssueCategoryId = '0';

  }

 
  if (!$BillingIssueCategoryId) {

    $BillingIssueCategoryId = '0';

  }
 
  if (!$SchoolId) {

    $SchoolId = '0';

  }
 
  if (!$ServiceTypeId) {

    $ServiceTypeId = '0';

  }
 
  if (!$RnId) {

    $RnId = '0';

  }

 
  if ($PostingStatusId == '0') {

    $PostingStatusId = '';

  }
 
   // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNBillingTransactions(?,?,?,?,?,?,?,?,?,?)");
    $stmt->execute([ $FromDate, 
                     $ToDate,
                     $BillingIssueCategoryId, 
                     $SchoolId,
                     $ServiceTypeId,
                     $RnId,
                     $RNLiaisonId,
                     $PostingStatusId,
                     $ConfirmationNumber,
                     $StudentName


                  ]);

  // Construct the SQL statement for display
$sql_display = "CALL proc_getSchRNBillingTransactions('$FromDate', '$ToDate', '$BillingIssueCategoryId', '$SchoolId', '$ServiceTypeId', '$RnId', '$RNLiaisonId', '$PostingStatusId', '$ConfirmationNumber', '$StudentName')";

// Display the constructed SQL call
echo "Executing SQL: " . $sql_display . "<br>";

  $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

  $pdo=null;


    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


 
    // Set header - assuming $results is not empty
    $column = 0;
    $objPHPExcel->getActiveSheet()->getStyle("A1:V1")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValue('A1', 'Status');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', 'Date');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', 'District');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', "School");
    $objPHPExcel->getActiveSheet()->setCellValue('E1', 'School ID');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', "Paid Hours");
    $objPHPExcel->getActiveSheet()->setCellValue('G1', "Un-Billed Hours");
    $objPHPExcel->getActiveSheet()->setCellValue('H1', 'Bill Rate');
    $objPHPExcel->getActiveSheet()->setCellValue('I1', 'Time of Arrival/Call in time');
    $objPHPExcel->getActiveSheet()->setCellValue('J1', "Today's Nurse");
    $objPHPExcel->getActiveSheet()->setCellValue('K1', 'Long Term Nurse');
    $objPHPExcel->getActiveSheet()->setCellValue('L1', 'Student');
    $objPHPExcel->getActiveSheet()->setCellValue('M1', 'OSIS');
    $objPHPExcel->getActiveSheet()->setCellValue('N1', 'Trip Location & Time');
    $objPHPExcel->getActiveSheet()->setCellValue('O1', 'Confirmation #');
    $objPHPExcel->getActiveSheet()->setCellValue('P1', 'Placement');
    $objPHPExcel->getActiveSheet()->setCellValue('Q1', 'Term');
    $objPHPExcel->getActiveSheet()->setCellValue('R1', 'Comment');
    $objPHPExcel->getActiveSheet()->setCellValue('S1', 'Liaison');
    $objPHPExcel->getActiveSheet()->setCellValue('T1', 'Billing Issue Category');
    $objPHPExcel->getActiveSheet()->setCellValue('U1', 'Billing Comment');
    $objPHPExcel->getActiveSheet()->setCellValue('V1', 'Issue Amount');
    $objPHPExcel->getActiveSheet()->setCellValue('W1', 'Write-Off Amount');
    $objPHPExcel->getActiveSheet()->setCellValue('X1', 'Additional Comment');
    $objPHPExcel->getActiveSheet()->setCellValue('Y1', 'Posting Status Comment');
    $objPHPExcel->getActiveSheet()->setCellValue('Z1', 'Docked Hrs');
    $objPHPExcel->getActiveSheet()->setCellValue('AA1', 'Setup Hrs');
    $objPHPExcel->getActiveSheet()->setCellValue('AB1', 'Docked Hrs Comment');


  
   

     $row = 2;
    foreach ($results as $result) {


       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $result['PostingStatusDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $result['ServiceDateFrm']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, $row, $result['DistrictName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(3, $row, $result['SchoolName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(4, $row, $result['SesisSchoolId']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, $row, $result['PaidHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(6, $row, $result['BilledHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, $row, $result['BillRate']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(8, $row, $result['CallInTime']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(9, $row, $result['ScheduledRNName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(10, $row, $result['LongTermRnName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(11, $row, $result['StudentName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(12, $row, $result['StudentOsisNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(13, $row, $result['ScheduleDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(14, $row, $result['ConfirmationNumber']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(15, $row, $result['Placement']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(16, $row, $result['AssignmentType']); 
   
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(17, $row, $result['CallInComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(18, $row, $result['LiaisonName']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(19, $row, $result['BillingIssueCategoryDesc']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(20, $row, $result['BillingComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(21, $row, $result['IssueAmount']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(22, $row, $result['WriteOffAmount']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(23, $row, $result['AdditionalComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(24, $row, $result['PostingStatusComments']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(25, $row, $result['DockedHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(26, $row, $result['SetupHours']); 
       $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(26, $row, $result['DockedHoursComments']); 


        $row++;
    }  

   $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

     $out_File = "../rn_reports/rn_billing_transactions_report.xlsx";

     $objWriter->save($out_File);

   $DownloadedFileName = 'rn_billing_transactions_report -'.Date('m-d-Y').'.xlsx';
 
    // header('Content-Description: File Transfer');
    // header('Content-Type: application/octet-stream');
    // //header('Content-Disposition: attachment; filename='.basename($out_File));
    // header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    // header('Content-Transfer-Encoding: binary');
    // header('Expires: 0');
    // header('Cache-Control: must-revalidate');
    // header('Pragma: public');
    // header('Content-Length: ' . filesize($out_File));
    // ob_clean();
    // flush();
    // readfile($out_File);

    unlink($out_File);    

    exit;

 


  
?>

