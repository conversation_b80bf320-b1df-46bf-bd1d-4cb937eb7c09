<?php 


	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 

	$StudentId = $_POST['StudentId'];
	$StatusId = $_POST['StatusId'];	
	$AssignmentId = $_POST['AssignmentId'];
	$AssignmentTypeId = $_POST['AssignmentTypeId'];
	$ConfirmationNumber = $_POST['ConfirmationNumber'];
	$StartDate = $_POST['StartDate'];
	$EndDate = $_POST['EndDate'];
	$ServiceTypeId = $_POST['ServiceTypeId'];
	$IncludeSundayFL = $_POST['IncludeSundayFL'];

	$UserId = $_POST['UserId'];


	$result = $rcr_transaction->setSchStudentAssignmentHeader(	$StudentId,
																$StatusId,
																$AssignmentId,
																$AssignmentTypeId,
																$ConfirmationNumber,
																$StartDate,
																$EndDate,
																$ServiceTypeId,
																$IncludeSundayFL,
																$UserId ); 

	$rcr_transaction->disconnectDB (); 

	//echo  '{ success: true };
	echo $result;

?>
