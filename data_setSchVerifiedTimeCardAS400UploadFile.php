<?PHP

/*
	$str = 'District 75';
	preg_match_all('!\d+!', $str, $matches);
	//print_r($matches);

	echo $matches[0][0];
*/


	require_once('DB.php');
	include('db_login.php');
	

	//AS400 Upload File  
	//==========================================================

	$out_File = "../uploads/Schools_to_400.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");


	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
	


	$query = "SELECT 									

            SUBSTRING_INDEX(d.ExtId, '#', 1 ) as BillingClientArea,  
			g.ExtId as EmplId,	
			a.ServiceTypeId as ServiceTypeId,
			DATE_FORMAT( a.ServiceDate, '%m%d%Y' ) as ServiceDate,
			DATE_FORMAT( a.StartTime, '%h%i%p' ) as StartTime, 
			DATE_FORMAT( a.EndTime, '%h%i%p' ) as EndTime,
			ABS(a.TotalHours) as TotalHours,
			a.AssignmentTypeId,
			c.DistrictName,
            (SELECT SUBSTRING( ExtId, -6 )  
				FROM SchStudents k
				WHERE a.StudentId = k.Id) AS PatientId	


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchSchools d,
				SchDistricts c,
				SchDistrictServiceDetails f,
				Users e,
			    Registrants g
		WHERE   a.UserId = e.UserId
			AND a.RegistrantId = g.Id	
			AND a.ServiceTypeId = b.Id	
			AND a.SchoolId = d.Id
			AND d.DistrictId = c.Id
			AND f.DistrictId = c.Id
			AND f.ServiceTypeId = a.ServiceTypeId
			AND a.ScheduleStatusId = '8'";
	
	$result = $connection->query ($query);

	$linecount = 0;	


	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$count++;

			//====================================
			// Billing Client Code
			//====================================

			$AssignmentTypeId = $row['AssignmentTypeId'];
			$DistrictName = $row['DistrictName'];

			if ($AssignmentTypeId == '1') { // Per Diem

				preg_match_all('!\d+!', $DistrictName, $matches);
				$client = 'DH'.$matches[0][0];

			

			} else { // Long Term

				$client = 'DELT';

			}


			//====================================
			// Department Code
			//====================================
			
			$dept_long = '';		
			$dept_long = $row['BillingClientArea']; 	
			$dept = substr($dept_long,0,6);
			//====================================
			// Service Date
			//====================================
			
			$serv_date = $row['ServiceDate'];
		 
			//====================================
			// Shift Code
			//====================================
			
			$start_time = $row['StartTime'];
			
			$date_arr = date_parse_from_format("g:i A", $start_time); 

			   switch ($date_arr[hour] ) {  		  
	  			 
					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "23") and ($date_arr[hour] <= "24")):  
					$shift_code = "1";   
					break;

					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "0") and ($date_arr[hour] < "7")):  
					$shift_code = "1";   
					break;
					
					// Employee Shift - 2 (Day)
					case (($date_arr[hour] >= "7") and ($date_arr[hour] < "15")):  
					$shift_code = "2";   
					break;

		        
					// Employee Shift - 3 (Evening)
					case (($date_arr[hour] >= "15") and ($date_arr[hour] < "23")):  
					$shift_code = "3";   
					break; 
					
					
				} 

			//====================================
			// Start Time 
			//====================================
			
			$StartTime = $row['StartTime']; 
			$StartTime = substr($StartTime,0, 5); 
			//====================================
			// End Time 
			//====================================
			
			$EndTime = $row['EndTime']; 
			$EndTime = substr($EndTime,0, 5);
			//====================================
			// Total Hours 
			//====================================
			
			$hours_worked = $row['TotalHours']; 
				
			//====================================
			// Employee ID 
			//====================================
			
			$emplid = $row['EmplId']; 
			
			//====================================
			// Patient ID 
			//====================================
			
			$patientid = $row['PatientId']; 

			//====================================
			// Service Type ID 
			//====================================
			
			$service_type = $row['ServiceTypeId'];

			//=====================================
			// For Service Type = "Student 1to1" (2)
			// BLANK OUT Patient ID  	
				
			if ($service_type == '2') {
				$patientid = '';
			}
		
			//=====================================
			// For Service Type = "Student Transportation" (3)
			// BLANK OUT Deparment Code  	
				
			if (($service_type == '1') || ($service_type == '12')) {
				$dept = '';
			}
		


			//=====================================
			// For Service Type = "Orientation" (12)
			// Patient ID to "27766" 	
				
			if ($service_type == '12') {
	 			$patientid = '27766';
			}
			//==============================================				
			//=====================================

			$client = str_pad($client,6, ' ', STR_PAD_RIGHT);
			$dept = str_pad($dept,6, ' ', STR_PAD_RIGHT);
			$name = str_pad($line_of_text[1],20, ' ', STR_PAD_RIGHT);
			//$serv_date = date("mdY",strtotime($serv_date));
			$serv_date = str_pad($serv_date,10, ' ', STR_PAD_RIGHT);
			$emplid = str_pad($emplid,9, '0', STR_PAD_LEFT);
			$patientid = str_pad($patientid,6, '0', STR_PAD_LEFT);
			$StartTime = str_replace(":", "", $StartTime);
			$StartTime = str_pad($StartTime,4, '0', STR_PAD_LEFT);
			$EndTime = str_replace(":", "", $EndTime);
			$EndTime = str_pad($EndTime,4, '0', STR_PAD_LEFT);
			$shift_code = str_pad($shift_code,2, ' ', STR_PAD_RIGHT);
			$hours_worked = sprintf("%01.2f", $hours_worked);
			$hours_worked = str_replace(".", "", $hours_worked);
			$hours_worked = str_pad($hours_worked,4, '0', STR_PAD_LEFT);

	/*			
			echo (' Client: '.$client);
			echo (' Dept: '.$dept);
			echo (' Id: '.$emplid);
			echo (' Date Worked: '.$serv_date);
			echo (' Start Time: '.$start_time);
			echo (' Start Hour: '.$date_arr[hour]);
			echo (' Shift Code: '.$shift_code);
			echo (' Hours Worked: '.$hours_worked.'</br>');
	*/		 

				$out_Line = $client." ".$dept." ".$emplid." ".$serv_date." ".$shift_code." ".$StartTime." ".$EndTime." ".$hours_worked." ".$patientid." EOL"."\n";
				fwrite($fh, $out_Line);
				//echo $out_Line.'</br>';	

				$linecount++;


	}
	
	$connection->disconnect();
	fclose($fh);


	//Download TiemCards File  
	//==========================================================

	if ($linecount > 0 ) {

	/*	
		header('Content-Type: application/octet-stream');
	    header('Content-Disposition: attachment; filename='.basename($out_File));
	    header('Expires: 0');
	    header('Cache-Control: must-revalidate');
	    header('Pragma: public');
	    header('Content-Length: ' . filesize($out_File));
	    readfile($out_File);
	    
	    unlink($out_File);    

	    exit;
	*/

	} 
	echo  "{ success: true, transactions: '{$linecount}'}";

?>