            DELIMITER $$

            DROP PROCEDURE IF EXISTS proc_getSchRsa7aForms$$

            CREATE PROCEDURE proc_getSchRsa7aForms (IN                          p_from_date DATE,
                                                                                p_first_period_date DATE,
                                                                                p_second_period_date DATE,
                                                                                p_to_date DATE,
                                                                                p_registrant_id_sel VARCHAR(18),
                                                                                p_student_id_sel VARCHAR(18),
                                                                                p_school_id_sel VARCHAR(18),
                                                                                p_bi_monthly_period_sel CHAR(1),
                                                                                p_fr_provider_name CHAR(1),
                                                                                p_to_provider_name CHAR(1) 
                                                                        )     

                                                

            BEGIN

              
              /* In_Person Forms - Period: 1 - 15*/  
              /*======================*/
              create temporary table tmp

         
              SELECT  a.Id as MandateId,
                                    a.ServiceTypeId, 
                                    c.ServiceTypeDesc,  
                                    a.SessionLength,
                                    a.SessionGrpSize,
                                    a.SessionFrequency,
                                    a.PlaceOfService,
                                    CONCAT(TRIM(f.SchoolName),' (',DistrictName,')') as SchoolName,
                                    a.SchoolId,
                                    a.StudentExtId, 
                                    a.StudentId, 
                                    a.RegistrantId,
                                    'In-Person      ' as FormType,
                                    '1 - 15    ' as BiMonthlyPeriodDesc,
                                    '1' as BiMonthlyPeriodId,
                                   
                                    COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '1'
                                            LIMIT 1 
                                    ),'0') as Rsa7aFormSignatureStatus,

                                    
                                    COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '1'
                                              LIMIT 1 
                                    ),'0') as Rsa7aFormId,

                                    COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '1'
                                            LIMIT 1 
                                    ),'') as ParentApprovalEmailFileName,
                                    
                                    COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '1'
                                             LIMIT 1 
                                    ),'') as Comments,


                                    COALESCE((SELECT f.SearchId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '1'
                                             LIMIT 1 
                                    ),'') as SearchId,

                                    CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                                    CONCAT(e.LastName, ', ', e.FirstName) as RegistrantName,



                                    (SELECT COUNT(*) FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId = 'I' 
                                        AND d.ServiceDate between p_from_date and p_first_period_date 

                                    ) as NumbeOfSessions,

                                    COALESCE(d.Email,'') as ApproverEmail,
                                    COALESCE(d.OfficePhone,'') as ApproverPhone,
                                    COALESCE((CONCAT(d.LiaisonFirstName, ' ', d.LiaisonLastName)),'') as ApproverName 

                                    
                            FROM SchStudentMandates a, 
                                 SchStudents b, 
                                 SchServiceTypes c, 
                                 SchSubSchools d,
                                 Registrants e,
                                 SchSchools f,
                                 SchDistricts g
                             WHERE a.ParaTransportPerc = 0
                            AND b.StatusId = '1'
                            AND a.StudentId = b.Id
                            AND a.ServiceTypeId = c.Id
                            AND b.Schoolid = d.SchoolId
                            AND b.Schoolid = f.Id  
                            AND f.DistrictId = g.Id 
                            AND b.SubSchoolTypeId = d.SubSchoolTypeId
                            AND a.RegistrantId = e.Id
                             
                            AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId = 'I' 
                                        AND d.ServiceDate between p_from_date and p_first_period_date

                            )
                            

                               ;


              /* In_Person Forms - Period: 15 - EOM*/  
              /*======================*/
              insert into tmp

         
              SELECT  a.Id as MandateId,
                                    a.ServiceTypeId, 
                                    c.ServiceTypeDesc,  
                                    a.SessionLength,
                                    a.SessionGrpSize,
                                    a.SessionFrequency,
                                    a.PlaceOfService,
                                    CONCAT(TRIM(f.SchoolName),' (',DistrictName,')') as SchoolName,
                                    a.SchoolId,
                                    a.StudentExtId,
                                    a.StudentId, 
                                    a.RegistrantId,
                                    'In-Person      ' as FormType,
                                    '16 - E.O.M    ' as BiMonthlyPeriodDesc,
                                    '2' as BiMonthlyPeriodId,
                                   
                                    COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '2'
                                            LIMIT 1 
                                    ),'0') as Rsa7aFormSignatureStatus,

                                    
                                    COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '2'
                                              LIMIT 1 
                                    ),'0') as Rsa7aFormId,

                                    COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '2'
                                            LIMIT 1 
                                    ),'') as ParentApprovalEmailFileName,
                                    
                                    COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '1'
                                            AND   f.BiMonthlyPeriodId = '2'
                                             LIMIT 1 
                                    ),'') as Comments,


                                    COALESCE((SELECT f.SearchId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '1'
                                             LIMIT 1 
                                    ),'') as SearchId,

                                    CONCAT(b.LastName, ', ', b.FirstName) as StudentName, 
                                    CONCAT(e.LastName, ', ', e.FirstName) as RegistrantName,

                                    (SELECT COUNT(*) FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId = 'I' 
                                        AND d.ServiceDate between p_second_period_date and p_to_date 

                                    ) as NumbeOfSessions,

                                    COALESCE(d.Email,'') as ApproverEmail,
                                    COALESCE(d.OfficePhone,'') as ApproverPhone,
                                    COALESCE((CONCAT(d.LiaisonFirstName, ' ', d.LiaisonLastName)),'') as ApproverName 
                                    
                            FROM SchStudentMandates a, 
                                 SchStudents b, 
                                 SchServiceTypes c, 
                                 SchSubSchools d,
                                 Registrants e,
                                 SchSchools f,
                                 SchDistricts g
                              WHERE   a.ParaTransportPerc = 0
                            AND b.StatusId = '1'
                            AND a.StudentId = b.Id
                            AND a.ServiceTypeId = c.Id
                            AND b.Schoolid = d.SchoolId
                            AND b.Schoolid = f.Id  
                            AND f.DistrictId = g.Id
                            AND b.SubSchoolTypeId = d.SubSchoolTypeId
                            AND a.RegistrantId = e.Id
                             
                            AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId = 'I' 
                                        AND d.ServiceDate between p_second_period_date and p_to_date

                            )
                            

                             ;                  


              


               /* Tele-Therapy Forms - Period: 1 - 15*/  
              /*======================*/
             insert into tmp

         
              SELECT  a.Id as MandateId,
                                    a.ServiceTypeId, 
                                    c.ServiceTypeDesc,  
                                    a.SessionLength,
                                    a.SessionGrpSize,
                                    a.SessionFrequency,
                                    a.PlaceOfService,
                                    CONCAT(TRIM(f.SchoolName),' (',DistrictName,')') as SchoolName, 
                                    a.SchoolId,
                                    a.StudentExtId, 
                                    a.StudentId, 
                                    a.RegistrantId,

                                    'Tele-Therapy      ' as FormType,
                                    '1 - 15    ' as BiMonthlyPeriodDesc,
                                    '1' as BiMonthlyPeriodId,
                                   
                                    COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '1'
                                            LIMIT 1 
                                    ),'0') as Rsa7aFormSignatureStatus,

                                    
                                    COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '1'
                                              LIMIT 1 
                                    ),'0') as Rsa7aFormId,

                                    COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '1'
                                            LIMIT 1 
                                    ),'') as ParentApprovalEmailFileName,
                                    
                                    COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '1'
                                             LIMIT 1 
                                    ),'') as Comments,


                                    COALESCE((SELECT f.SearchId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '1'
                                             LIMIT 1 
                                    ),'') as SearchId,

                                    CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                                    CONCAT(e.LastName, ', ', e.FirstName) as RegistrantName,

                                    (SELECT COUNT(*) FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId != 'I' 
                                        AND d.ServiceDate between p_from_date and p_first_period_date 

                                    ) as NumbeOfSessions,

                                    COALESCE(b.GuardianEmail,'') as ApproverEmail,
                                    COALESCE(b.GuardianPhone,'') as ApproverPhone,
                                    COALESCE((CONCAT(b.GuardianFirstName, ' ', b.GuardianLastName)),'') as ApproverName 

                                     


                            FROM SchStudentMandates a, 
                                 SchStudents b, 
                                 SchServiceTypes c, 
                                 SchSubSchools d,
                                 Registrants e,
                                 SchSchools f,
                                 SchDistricts g
                              WHERE   a.ParaTransportPerc = 0
                            AND b.StatusId = '1'
                            AND a.StudentId = b.Id
                            AND a.ServiceTypeId = c.Id
                            AND b.Schoolid = d.SchoolId
                            AND b.Schoolid = f.Id 
                            AND f.DistrictId = g.Id 
                            AND b.SubSchoolTypeId = d.SubSchoolTypeId
                            AND a.RegistrantId = e.Id
                             
                            AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId != 'I' 
                                        AND d.ServiceDate between p_from_date and p_first_period_date

                            )
                            

                               ;


              /* Tele-Therapy Forms - Period: 15 - EOM*/  
              /*======================*/
              insert into tmp

         
              SELECT  a.Id as MandateId,
                                    a.ServiceTypeId, 
                                    c.ServiceTypeDesc,  
                                    a.SessionLength,
                                    a.SessionGrpSize,
                                    a.SessionFrequency,
                                    a.PlaceOfService,
                                    CONCAT(TRIM(f.SchoolName),' (',DistrictName,')') as SchoolName,
                                    a.SchoolId,
                                    a.StudentExtId, 
                                    a.StudentId, 
                                    a.RegistrantId,
                                    'Tele-Therapy      ' as FormType,
                                    '16 - E.O.M    ' as BiMonthlyPeriodDesc,
                                    '2' as BiMonthlyPeriodId,
                                   
                                    COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '2'
                                            LIMIT 1 
                                    ),'0') as Rsa7aFormSignatureStatus,

                                    
                                    COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '2'
                                              LIMIT 1 
                                    ),'0') as Rsa7aFormId,

                                    COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date  
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '2'
                                            LIMIT 1 
                                    ),'') as ParentApprovalEmailFileName,
                                    
                                    COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '2'
                                             LIMIT 1 
                                    ),'') as Comments,


                                    COALESCE((SELECT f.SearchId FROM SchRsa7aFormSignatures f
                                            WHERE a.Id = f.MandateId
                                            AND   f.FromDate =  p_from_date and f.ToDate = p_to_date 
                                            AND   f.FormTypeId = '2'
                                            AND   f.BiMonthlyPeriodId = '2'
                                             LIMIT 1 
                                    ),'') as SearchId,

                                    CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                                    CONCAT(e.LastName, ', ', e.FirstName) as RegistrantName,

                                    (SELECT COUNT(*) FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId != 'I' 
                                        AND d.ServiceDate between p_second_period_date and p_to_date 

                                    ) as NumbeOfSessions,

                                    COALESCE(b.GuardianEmail,'') as ApproverEmail,
                                    COALESCE(b.GuardianPhone,'') as ApproverPhone,
                                    COALESCE((CONCAT(b.GuardianFirstName, ' ', b.GuardianLastName)),'') as ApproverName 
                                    
                            FROM SchStudentMandates a, 
                                 SchStudents b, 
                                 SchServiceTypes c, 
                                 SchSubSchools d,
                                 Registrants e,
                                 SchSchools f,
                                 SchDistricts g
                             WHERE   a.ParaTransportPerc = 0
                            AND b.StatusId = '1'
                            AND a.StudentId = b.Id
                            AND a.ServiceTypeId = c.Id
                            AND b.Schoolid = d.SchoolId
                            AND b.Schoolid = f.Id  
                            AND f.DistrictId = g.Id
                            AND b.SubSchoolTypeId = d.SubSchoolTypeId
                            AND a.RegistrantId = e.Id
                             
                            AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                                        WHERE d.MandateId = a.Id 
                                        AND d.ScheduleStatusId >= 7
                                        AND  d.SessionDeliveryModeId != 'I' 
                                        AND d.ServiceDate between p_second_period_date and p_to_date

                            )
                            

                             ;      

              
                IF p_registrant_id_sel != '' THEN
                   SET @reg_s = CONCAT(' AND RegistrantId = ',p_registrant_id_sel);  
                   SET p_fr_provider_name = 'a';  
                   SET p_to_provider_name = 'z';

                ELSE
                   set @reg_s = "";
                END IF;

         
                IF p_student_id_sel != '' THEN
                   SET @stu_s = CONCAT(' AND StudentId = ',p_student_id_sel);       
                ELSE
                   set @stu_s = "";
                END IF;

                IF p_school_id_sel != '' THEN
                   SET @sch_s = CONCAT(' AND SchoolId = ',p_school_id_sel);       
                ELSE
                   set @sch_s = "";
                END IF;

                IF p_bi_monthly_period_sel != '' THEN
                   SET @prd_s = CONCAT(' AND BiMonthlyPeriodId = ',p_bi_monthly_period_sel);       
                ELSE
                   set @prd_s = "";
                END IF;


                IF ((p_fr_provider_name != 'a') || (p_to_provider_name != 'z')) THEN
                   SET @nam_s = CONCAT('AND SUBSTR(UPPER(TRIM(RegistrantName)),1,1) between '"'", p_fr_provider_name, "'"' and '"'",p_to_provider_name,"'");   
                ELSE                    
                   set @nam_s = "";
                END IF;
                
                SET @s = CONCAT('SELECT * FROM tmp WHERE RegistrantId != 0 ',@reg_s, @stu_s, @sch_s, @prd_s , @nam_s,' order by RegistrantName');
                 PREPARE stmt FROM @s;
                 EXECUTE stmt;  
                DEALLOCATE PREPARE stmt;  

              

              drop temporary table if exists tmp;
               
              
            END $$

            DELIMITER ;   
             