<?php 

	 require_once("db_GetSetData.php");

	$conn = getCon();
 

$query = "  SELECT  a.Id as id,  
					a.TypeId,
					a.ExtId,
					a.SearchId,			
					CONCAT( trim( a.LastName) , ', ', trim(a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
				FROM Registrants a, RegistrantTypes b
				WHERE TypeId  = b.Id 
				AND TypeId != '23'
 				ORDER BY LastName, FirstName  ";


    


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	  

?>
