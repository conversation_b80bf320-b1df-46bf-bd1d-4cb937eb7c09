<?php 



    require_once("db_GetSetData.php");

     $conn = getCon();

    $ProviderTypeId = $_GET['ProviderTypeId'];  
    $FilterTypeId = $_GET['FilterTypeId'];  
    $FilterValue = $_GET['FilterValue'];  


        
    $query = "   SELECT 
                 a.Id, 
                 a.StatusId,
                 case a.StatusId
                   when '1' then 'Active'
                   else 'Inactive'
                 end as StatusDesc,   
                 a.TypeId,
                 concat(a.LastName, ', ',a.FirstName) as CandidateName,
                 a.LastName,
                 a.FirstName,
                 RegistrantTypeDesc as CandidateTypeDesc,
                 a.StreetAddress1,
                 a.StreetAddress2,
                 a.City,
                 a.Zipcode,
                 a.MobilePhone,
                 a.HomePhone, 
                 a.State,
                 a.ZipCode,
                 a.Email,
                 a.RecruitorId,
                 case a.RecruitorId 
                  when 0 then 'Undefined'
                  else concat(d.LastName, ', ',d.FirstName)  
                 end as RecruitorName,
                 concat(c.LastName, ', ',c.FirstName) as UserName,
                 
                 DATE_FORMAT( a.TransDate, '%m-%d-%Y' ) as TransDate    
       from  Candidates a 
               LEFT JOIN 
             RegistrantTypes b ON a.TypeId = b.Id
               LEFT JOIN 
             Users c on a.UserId = c.UserId 
               LEFT JOIN 
             Users d on a.RecruitorId = d.UserId 
              
       Where a.TypeId = '{$ProviderTypeId}'
       
                         ";


 
     $sql_filter = '';


            switch ($FilterTypeId) {
             
              case '2':
                $sql_filter = " AND a.LastName like '%".$FilterValue."%'"  ;
                break;

              case '3':
                $sql_filter = " AND a.FirstName like '%".$FilterValue."%'"  ;
                break;

              case '4':
                $sql_filter = " AND a.Zipcode like '%".$FilterValue."%'"  ;
                break;

            } 


    $sql_roder_by = '  ORDER BY  CandidateName ';     
   

    $query = $query.$sql_filter.$sql_roder_by;          




    $ret = getData ($conn, $query);
    setDisConn($conn);

    echo $ret;
    // echo $query;
 

?>
