
<?php 
	
 
 	require_once("db_login.php");
  
 
	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }
 	
 	//echo 'Step 01</br>'; 
 

 	//$MandateId = $_POST['MandateId'];
	if (isset($_POST['MandateId'])) { 
	    $MandateId = $_POST['MandateId']; } 
	    else { 
	    $MandateId = $_GET['MandateId']; 
	}


	//$StartDate = $_POST['StartDate'];	
	if (isset($_POST['StartDate'])) { 
	    $StartDate = $_POST['StartDate']; } 
	    else { 
	    $StartDate = $_GET['StartDate']; 
	}
	
	//$EndDate = $_POST['EndDate'];
	if (isset($_POST['EndDate'])) { 
	    $EndDate = $_POST['EndDate']; } 
	    else { 
	    $EndDate = $_GET['EndDate']; 
	}
	
	//$RegistrantId = $_POST['RegistrantId'];
	if (isset($_POST['RegistrantId'])) { 
	    $RegistrantId = $_POST['RegistrantId']; } 
	    else { 
	    $RegistrantId = $_GET['RegistrantId']; 
	}
	
	//$UserId = $_POST['UserId'];
	if (isset($_POST['UserId'])) { 
	    $UserId = $_POST['UserId']; } 
	    else { 
	    $UserId = $_GET['UserId']; 
	}


    $query = "	call   proc_setSchStudentParaMandateClone ( '{$MandateId}', 
    														'{$StartDate}',
    														'{$EndDate}',
    														'{$RegistrantId}',
    														'{$UserId}' 
    													  )   ";
	
	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));
	


 

	while ($row = $result->fetch_assoc()) {

	 	$MandateId =  ($row["MandateId"])  ;
	 	$StartDate =  ($row["StartDate"])  ;



	}

    mysqli_free_result($result);
	mysqli_close($conn);
  	 

 	//==========================
	
 	$date = strtotime('next Friday');

 	$EndDate  = date('Y-m-d', $date);


 
	$datetime1 = date_create($StartDate);
	$datetime2 = date_create($EndDate);
	$interval = date_diff($datetime1, $datetime2);
	$days = $interval->format('%a');


	$ServiceDate = $StartDate;
	$ServiceDate  = date('Y-m-d', strtotime( $ServiceDate));	 
	$dw = date( "w", strtotime( $ServiceDate));
	if ($dw == 6) {
		$we_date = strtotime('Saturday', strtotime($ServiceDate));

	}   else { 
		
		$we_date = strtotime('next Saturday', strtotime($ServiceDate));

	}
	$PayrollWeek  = date('Y-m-d', $we_date);


	$conn1=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }



	for ($x = 0; $x <= abs($days); $x++) {

		if (($dw != 0) && ($dw != 6)) { 
	


			$query1 = " INSERT INTO WeeklyServices
                    ( 
			              AssignmentId,
			              CLientId,
			              PayrollWeek,
			              StudentId,
			              ScheduleStatusId,
			              ServiceTypeId,
			              AssignmentTypeId, 
			              RegistrantId,
			              ServiceDate,   
			              StartTime, 
			              EndTime,    
			              TotalHours , 
			              WeekDay,
			              SchoolId,
			              UserId,
			              TransDate 
			            ) 


			SELECT 	a.Id as AssignmentId,
								f.Id as ClientId,
								'{$PayrollWeek}' as PayrollWeek,
								a.StudentId,
								'7',
								a.ServiceTypeId,
								a.AssignmentTypeId,
								b.RegistrantId,
								'{$ServiceDate}' as ServiceDate, 
								b.StartTime, 
								b.EndTime, 		
								b.TotalHours, 
								d.WeekDay,
								e.SchoolId, 
								'0',
								NOW()
			FROM     SchStudentAssignmentHeader a,
		             SchStudentAssignmentDetails b,
		             Registrants c,
		             DaysOfWeek5 d,
		             SchStudents e,
		             Clients f
      WHERE a.StatusId = '1'
      AND  b.WeekDayId = '{$dw}'
      AND  a.Id = b.AssignmentId 
      AND  b.RegistrantId = c.Id 
      AND  b.WeekDayId = d.WeekDayId
      AND  a.StudentId = e.Id
      AND  f.SchoolFL = '1'
      AND  a.MandateId = '{$MandateId}'
      AND NOT EXISTS(SELECT 1 FROM WeeklyServices h
          WHERE a.StudentId = h.StudentId 
          AND   h.ServiceDate =  '{$ServiceDate}'
          AND   a.ServiceTypeId = h.ServiceTypeId 
          AND   h.ScheduleStatusId > 6 


      ) 

      ";

	
			$result1 =  mysqli_query($conn1, $query1) or die
			("Error in Selecting " . mysqli_error($conn1));


 
			echo 'Sevice Date: '.$ServiceDate.' Payroll Week: '.$PayrollWeek.' Day of week:'.$WeekDay.' Service Type: '.$ServiceTypeId.'</br>';

 
		}
		
		$date  = strtotime("+1 day", strtotime( $ServiceDate));
		$ServiceDate  = date('Y-m-d', $date);	
		$dw = date( "w", strtotime( $ServiceDate));	
		if ($dw == 6) {
			$we_date = strtotime('Saturday', strtotime($ServiceDate));

		}   else { 
			
			$we_date = strtotime('next Saturday', strtotime($ServiceDate));

		}
		$PayrollWeek  = date('Y-m-d', $we_date);


	}	

 	//===========================

    mysqli_free_result($result1);
	mysqli_close($conn1);

?>

 