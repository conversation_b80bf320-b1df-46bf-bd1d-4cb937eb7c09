<?php
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

include 'db_login.php';

try {
    // Enable buffered queries by adding PDO::MYSQL_ATTR_USE_BUFFERED_QUERY
    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
    ]);

    // Define the days of the week with their corresponding names and weekday IDs
    $days = [
        1 => ['name' => 'Mon', 'WeekDayId' => 1],
        2 => ['name' => 'Tue', 'WeekDayId' => 2],
        3 => ['name' => 'Wed', 'WeekDayId' => 3],
        4 => ['name' => 'Thu', 'WeekDayId' => 4],
        5 => ['name' => 'Fri', 'WeekDayId' => 5]
    ];

    // Iterate over each day to perform the updates
    foreach ($days as $day) {
        try {
            // Prepare the SQL statement with placeholders for dynamic parts
            

            // SNync AM Hours
            //==================
            $updateStmtAM = $conn->prepare("UPDATE SchStudentMandates a
                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
                                            JOIN SchStudents d ON a.StudentId = d.Id
                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1AMId
                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
                                            AND c.WeekDayId = :WeekDayId
                                            SET 
                                                c.StartTime = ParaStartTime" . $day['name'] . "1,
                                                c.EndTime = ParaEndTime" . $day['name'] . "1,
                                                c.TotalHours = ParaTotalHours" . $day['name'] . "1,
                                                c.UserId = '1',
                                                c.TransDate = now()  
                                            WHERE
                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
                                                AND a.EndDate >= '2025-01-01'
                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
                                                
                                                ");

            // Bind parameters
            $updateStmtAM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);
            
            
  
            // Execute the update statement
            $updateStmtAM->execute();
            

            // SNync PM Hours
            //==================
            $updateStmtPM = $conn->prepare("UPDATE SchStudentMandates a
                                            JOIN SchStudentAssignmentHeader b ON a.Id = b.MandateId
                                            JOIN SchStudents d ON a.StudentId = d.Id
                                            JOIN SchServiceTypes e ON b.ServiceTypeId = e.Para1to1PMId
                                            JOIN SchStudentAssignmentDetails c ON b.Id = c.AssignmentId
                                            AND c.WeekDayId = :WeekDayId
                                            SET 
                                                c.StartTime = ParaStartTime" . $day['name'] . "2,
                                                c.EndTime = ParaEndTime" . $day['name'] . "2,
                                                c.TotalHours = ParaTotalHours" . $day['name'] . "2,
                                                c.UserId = '1',
                                                c.TransDate = now()    
                                            WHERE
                                                CURDATE() BETWEEN a.StartDate AND a.EndDate
                                                AND a.EndDate >= '2025-01-01'
                                                AND a.ServiceTypeId BETWEEN 17 AND 21 
                                                 
                                                ");

            // Bind parameters
            $updateStmtPM->bindParam(':WeekDayId', $day['WeekDayId'], PDO::PARAM_INT);

   
            
            // Execute the update statement
            $updateStmtPM->execute();

            echo "Processed " . $day['name'] . "<br>";
        
        } catch (PDOException $e) {
            // Display error message for each day if it occurs
            echo "Error processing " . $day['name'] . ": " . $e->getMessage() . "<br>";
        }
    }

    echo "Processing completed for all days.";

} catch (PDOException $e) {
    echo "Connection error: " . $e->getMessage();
}

// Close connection
$conn = null;
?>
