	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_getSchSchoolboardSchoolsDateRangeSchedulesSum$$

	CREATE PROCEDURE proc_getSchSchoolboardSchoolsDateRangeSchedulesSum (IN p_from_date DATE,
																		 p_to_date DATE
														         	)  




	BEGIN

		create temporary table tmp
		(
			 
				DistrictId INT, 
				DistrictName VARCHAR(48),
				TotalFilled INT,   					
				TotalUnFilled INT,
				TotalCnlByRegistrants INT,
				TotalCnlBySchools INT


		);



	INSERT INTO tmp (DistrictId, DistrictName)

    SELECT distinct d.DistrictId, DistrictName    

    FROM  WeeklyServices a, 
        SchServiceTypes b,
        SchSchools d,
        SchDistricts c 
    WHERE   a.ServiceDate between  p_from_date and p_to_date 
      AND a.ServiceTypeId = b.Id  
      AND b.ServiceCategoryId = 0
      AND a.SchoolId = d.Id 
      AND d.DistrictId = c.Id 
		;	


	create temporary table tmp1 engine=memory

    SELECT  
            ScheduleStatusId, 
            
            d.DistrictId


    FROM  WeeklyServices a, 
        SchServiceTypes b,
        SchSchools d,
           ScheduleStatuses g
    WHERE   a.ServiceDate between  p_from_date and p_to_date 
      AND ScheduleStatusId = g.Id 
      AND a.ServiceTypeId = b.Id  
      AND b.ServiceCategoryId = 0
      AND a.SchoolId = d.Id 
	;

	/* Update Total Filled */
		UPDATE tmp a
		SET a.TotalFilled = (SELECT count(*) FROM tmp1 b 
			                   where a.DistrictId = b.DistrictId
			                   and ScheduleStatusId > 6 
							);


	/* Update Total UnFilled */
		UPDATE tmp a
		SET a.TotalUnFilled = (SELECT count(*) FROM tmp1 b 
			                   where a.DistrictId = b.DistrictId
			                   and ScheduleStatusId =  0 
							);

	/* Update Cancelled By Registrants */
		UPDATE tmp a
		SET a.TotalCnlByRegistrants = (SELECT count(*) FROM tmp1 b 
			                   where a.DistrictId = b.DistrictId
			                   and ScheduleStatusId = 2 
							);

	/* Update Cancelled By School */
		UPDATE tmp a
		SET a.TotalCnlBySchools = (SELECT count(*) FROM tmp1 b 
			                   where a.DistrictId = b.DistrictId
			                   and ScheduleStatusId in (5,3,4) 
							);


		SELECT * from tmp;

		drop temporary table if exists tmp;
		drop temporary table if exists tmp1;
		 
		
	END $$

	DELIMITER ;		
	 