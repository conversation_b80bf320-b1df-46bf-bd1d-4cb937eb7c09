

/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getSchOpenCaseloadCandidates$$

CREATE PROCEDURE proc_getSchOpenCaseloadCandidates (IN   p_school_zip_code varchar(10), 
													     p_service_type_id int,
													     p_last_text_msg_id int
													     p_user_id int,
													     p_user_group int
												  )  

BEGIN

 
 
	
	DECLARE v_School_Zipcode VARCHAR(5);
	DECLARE v_School_Latitude VARCHAR(8);
	DECLARE v_School_Longitude VARCHAR(9);
	DECLARE v_Registrant_Latitude VARCHAR(8);
	DECLARE v_Registrant_Longitude VARCHAR(9);
	DECLARE v_Registrant_Types VARCHAR(96);

	
	DECLARE done INT DEFAULT 0;	
	
	/* Get Registrant Type for given Service Type */
	/*=================================*/
	Select RegistrantTypeIdMult into v_Registrant_Types  
		from SchServiceTypes
	Where Id = p_service_type_id;	
	
	/* Get School Latitude/Longitude  */
	/*=================================*/
	Select Latitude, Longitude
		into v_School_Latitude, v_School_Longitude
		from Zipcode_New
	Where ZipCode = p_school_zip_code;	

	
	create temporary table tmp engine=memory

	SELECT b.Id as RegistrantId,
      CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)  ) as RegistrantName,
      b.Id as EmailContactId,  
      b.City,
      COALESCE(MobilePhone,'') as MobilePhone,
      COALESCE(HomePhone,'') as HomePhone,
      Email,
      Latitude,	
      Longitude,	
      b.ZipCode,
      0000.00 as ProximityToSchool,
      p_last_text_msg_id as LastEmailId, 
 	  d.EmailRespFL as InterestedFl,
 	  d.Id as EmailSendFL,
 	  RecruitorId,
 	  'EMP' as CandidateType

      
   FROM  
       Registrants b  
          LEFT JOIN 
       Zipcode_New c ON  SUBSTRING(b.ZipCode,1,5)  = c.ZipCode  
                     
           
          LEFT JOIN 
      SchOpenCaseloadsEmailDetails  d ON d.EmailHdrId = p_last_text_msg_id
                                          and  b.Id = d.EmailContactId                               
          
   where     
           b.StatusId = 1
         and b.TypeId in (v_Registrant_Types) 
         -- AND b.EmailContactId != ''
         AND LENGTH(b.ZipCode) >= 5 
 		
   UNION
SELECT b.Id as RegistrantId,
      CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)  ) as RegistrantName,
      b.Id as EmailContactId,  
      b.City,
      COALESCE(MobilePhone,'') as MobilePhone,
      COALESCE(HomePhone,'') as HomePhone,
      Email,
      Latitude,	
      Longitude,	
      b.ZipCode,
      0000.00 as ProximityToSchool,
      p_last_text_msg_id as LastEmailId, 
 	  d.EmailRespFL as InterestedFl,
 	  d.Id as EmailSendFL,
 	  RecruitorId, 
 	  'CAN' as CandidateType

      
   FROM  
       Candidates b  
          LEFT JOIN 
       Zipcode_New c ON  SUBSTRING(b.ZipCode,1,5)  = c.ZipCode  
                       
          LEFT JOIN 
      SchOpenCaseloadsEmailDetails  d ON d.EmailHdrId = p_last_text_msg_id
                                          and  b.Id = d.EmailContactId                               
          
   where     
           b.StatusId = 1
         and b.TypeId in (v_Registrant_Types) 
         AND b.EmailContactId != ''
         AND LENGTH(b.ZipCode) >= 5 
 			;
			
	
/* Set Candidate's Proximity to School   */
/* =======================================*/

UPDATE tmp a, Zipcode_New c   
	Set ProximityToSchool = (
	3959 *
	acos(
		cos(radians(v_School_Latitude)) *
		cos(radians(c.Latitude)) *
		cos(radians(c.Longitude) - radians(v_School_Longitude)) +
		sin(radians(v_School_Latitude)) *
		sin(radians(c.Latitude))
		) 
	) 
Where a.ZipCode = c.ZipCode;
	

 
/* =======================================*/

	select *    
	From tmp   
	WHERE  ProximityToSchool <= 25
	AND Latitude != ''
	AND Email != ''
 	Order By InterestedFl DESC, ProximityToSchool, RegistrantName;

	drop temporary table if exists tmp;	
	
	
END $$

DELIMITER ;	