<?php 
	require_once("db_GetSetData.php");

	$conn = getCon();

/*

    $query = "	SELECT   ContractTypeId AS id,
    					 ContractTypeId, 	
    					 ContractTypeDesc 
				FROM   SchSchoolContracts  ";
*/


    $query = "	SELECT 	a.Id as id,
	   					a.Id as BillingContractId,
				       concat(BillingContractDesc, ' (', BillingEntityDesc,')' ) as BillingContractDesc
				       FROM SchBillingContracts a,
				                SchBillingEntities b
				    WHERE a.BillingEntityId = b.BillingEntityId            
  ";


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
  
?>
