<?php 

 require_once("db_GetSetData.php");

    ini_set("memory_limit","-1");


    $conn = getCon();

    $FromDate = $_GET['FromDate'];  
    $PeriodStartDate = $_GET['PeriodStartDate'];  
    $PeriodEndDate = $_GET['PeriodEndDate'];  
    $ToDate = $_GET['ToDate'];  
    $RegistrantIdSel = $_GET['RegistrantIdSel'];  
    $StudentIdSel = $_GET['StudentIdSel'];  
    $SchoolIdSel = $_GET['SchoolIdSel'];  
    $BiMonthyperiodIdSel = $_GET['BiMonthyperiodIdSel'];  
    $FromProviderName = $_GET['FromProviderName'];  
    $ToProviderName = $_GET['ToProviderName'];  



    $query = "call proc_getSchRsa7aForms (   '{$FromDate}',
                                        '{$PeriodEndDate}',
                                        '{$PeriodStartDate}',
                                        '{$ToDate}',
                                        '{$RegistrantIdSel}',
                                        '{$StudentIdSel}',
                                        '{$SchoolIdSel}',
                                        '{$BiMonthyperiodIdSel}', 
                                        '{$FromProviderName}' ,
                                        '{$ToProviderName}' 
                                                                )     
 ";

 
    $ret = getData ($conn, $query);
    setDisConn($conn);

    echo $ret;
    // echo $query;
    


 