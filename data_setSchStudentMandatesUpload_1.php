  
<?php
    
/*
 
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/
 

	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	//==============
	// Billing Contract Id

	$billing_contract_id = $_POST['BillingContractId'];
	if (!$billing_contract_id) {
		$billing_contract_id = '1';
	}	


	//===============

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
	$query = "SELECT SchoolSeasonEndDate
		FROM SchSchoolYear			 
		WHERE CurrentYearFL = '1'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$EndDate = $row['SchoolSeasonEndDate'];
	}	
	 
	/* Upload New File 
	 =============================================*/
		

	$inputFileName = '../uploads/mandates.csv';

 
 //   if($ufile != none){ 
      
	// 	//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
	// 	$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	// } else {
	// 	print "1:Error uploading extracted file. Please try again!!! "; 
	  
	// 	echo  "{ success: error,  data: ".json_encode($file)."}";
	//     Return ; 

	// }
   

	$file_handle = fopen("../uploads/Mandates - Mejia.csv", "r");
	
	ini_set("auto_detect_line_endings", true);

	$cnt = 0;	
	$linecount = 0;	

   while (($row = fgetcsv($file_handle)) !== FALSE) { // while - start

   			$cnt++;


			$m_rec = substr(($row[0]),0,1);

   			var_dump($m_rec).'<br>';


			// if ($m_rec == '2') { // m_rec - Start
			if (($m_rec == '1') || ($m_rec == '2')) { // m_rec - Start

				/* Student External ID
				 ===================*/

				$student_ext_id = $row[0];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row[1];

				echo 'last name: '.$student_last_name.'<br>';

				/* Student First Name
				 ===================*/

				$student_first_name = $row[2];


				/* Student DOB
				============================= */
				
			/*	
				$dob = $row[7];
				$date_unix_dob = date_create('30-12-1899');
    			date_add($date_unix_dob, date_interval_create_from_date_string("{$dob} days"));
    			$dob_str = date_format($date_unix_dob, 'Y-m-d');
			*/
    			$dob_str = date("Y-m-d", strtotime($row[7]));


				/* Student School ID
				 ===================*/

				$school_ext_id = '%'.$row[8].'%';
				$mandate_school_id = $row[8];

 				$school_id = 0;
				
				$query_school_id = " SELECT Id as SchoolId 
							from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1	 
				";
				
				$result_school_id = $connection->query ($query_school_id);
				if (DB::isError($result_school_id)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_id));
			    }

				while ($row_school_id =& $result_school_id->fetchRow (DB_FETCHMODE_ASSOC)) {
					$school_id = $row_school_id['SchoolId'];
 

				}	



				/* Student Service Type Desc
				 ================================*/

				$mandate_serv_type_desc = $row[14];

				//==================================
				// Get eWeb Service Type 
				//==================================
			 	
				$service_type_id = ''; 

				$query_ser_id = " SELECT  	Id as ServiceTypeId, 
									        SESISServiceDurType,
									        DOEServiceTypeIndId,
              								DOEServiceTypeGrpId,
              								SESISParaServiceTypeId  
						   FROM SchServiceTypes	
   						where SESISServiceTypeDesc  like '%{$mandate_serv_type_desc}%' LIMIT 1 	 
				";
				
				$result_ser_id = $connection->query ($query_ser_id);
				if (DB::isError($result)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
			    }

				while ($row_ser_id =& $result_ser_id->fetchRow (DB_FETCHMODE_ASSOC)) {
					$service_type_id = $row_ser_id['ServiceTypeId'];
					$service_dur_type = $row_ser_id['SESISServiceDurType'];
					$service_type_ind = $row_ser_id['DOEServiceTypeIndId'];
					$service_type_grp = $row_ser_id['DOEServiceTypeGrpId'];
					$sesis_para_service_type_id = $row_ser_id['SESISParaServiceTypeId'];


				}	



				/*================================*/

				/* Student Language
				 ================================*/

				$student_language = $row[16];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row[17];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row[18];


				/* Mandate Service Freq
				 ================================*/

				$mandate_serv_freq = $row[19];
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Duration
				 ================================*/

				$mandate_serv_dur = $row[20];
				$mandate_serv_dur_num = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);

				/* Service Duration for PARAs
                 ============================*/

				$para_transport_perc =  0;

				 
				if (stristr($mandate_serv_type_desc, 'Transportation'))  { 

					if ($mandate_serv_dur_num == 100) { // Full Day

						$para_transport_perc =  100; // Save in Student Mandate
						$mandate_serv_dur_num = 8;
						$mandate_serv_dur_min = 8 * 60;
						$para_fl = '1';

					}


					if ($mandate_serv_dur_num == 20) { // Not Full Day


						$para_transport_perc =  20; // Save in Student Mandate
						$mandate_serv_dur_num = 1.5;
						$mandate_serv_dur_min = 1.5 * 60;
						$para_fl = '1';

					}



				}
				 


				/*===============================*/

				/* Mandate Service Start Date
				============================= */
							
    			$mandate_start_date = date("Y-m-d", strtotime($row[29]));


				/* Mandate First Attend Date
				============================= */
							
    			$mandate_first_attend_date = date("Y-m-d", strtotime($row[30]));

    			if ($sesis_para_service_type_id > 0) {

    				$mandate_first_attend_date = $mandate_start_date;
    			}

				/* Provider Name
				 ================================*/

				
				$mandate_provider_name = $row[23];

				//==================================
				// Get Provider (Registrant)  Id
				//==================================
			 	
				$registrant_id = '0'; 

				
				if ($mandate_provider_name) {


					$mandate_provider_name = $connection->escapeSimple($mandate_provider_name); 

					$query_reg_id = "SELECT COALESCE((SELECT Id from Registrants 
								  WHERE  UPPER(Concat(TRIM(FirstName), ' ', TRIM(LastName)))  LIKE UPPER('%{$mandate_provider_name}%') LIMIT 1),0)   as RegistrantId 
					";
					
					$result_reg_id = $connection->query ($query_reg_id);
					if (DB::isError($result_reg_id)){
				                die("Could not query the database:<br />$query ".DB::errorMessage($result_ser_id));
				    }

					while ($row_reg_id =& $result_reg_id->fetchRow (DB_FETCHMODE_ASSOC)) {
						$registrant_id = $row_reg_id['RegistrantId'];


					}	


				}



				/*
				$provider_name = explode(" ", $row[23]);
				$max = sizeof($provider_name);
				$provider_first_name = strtoupper($provider_name[0]);
				$provider_last_name = strtoupper($provider_name[$max-1]);
				*/


				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = trim($row["25"]);



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query5 = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result5 = $connection->query ($query5);

				if (DB::isError($result5)){
					die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result5->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT INTO SchStudents
						(ExtId, 
						 SearchId, 
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 	
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'1',
						/*	 
							 COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),
						*/
							'{$school_id}',
							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}
				} // End 1	


			//================================ 
			//  Check if Mandate Already Exists  
			//================================ 
		 	

			
			if (strlen($mandate_first_attend_date) > 10) {

				$mandate_first_attend_date = substr($mandate_first_attend_date,0,10);	

			}

			
			if ($sesis_para_service_type_id == 0) { //Non-Para mandates   
 
				$query4 = "SELECT 1 
							FROM SchStudentMandates a, SchServiceTypes b
						WHERE a.StudentExtId = '{$student_ext_id}'
						AND a.StartDate = '{$mandate_first_attend_date}' 
	 				/*	AND b.ServiceTypeDesc like '{$mandate_serv_type_desc}'	*/
	 					AND b.SESISServiceTypeDesc like '{$mandate_serv_type_desc}'	
	 					
	 					AND a.SessionGrpSize = '{$mandate_grp_size}'
	 					AND a.SessionFrequency = '{$mandate_serv_freq_num}'
	                    AND a.ServiceTypeId = b.Id 
	                /*    AND a.DOEFirstAttendDate = '{$mandate_first_attend_date}' */
	                    AND a.DOEProvider = '{$mandate_provider_name}'  
	                    AND a.StatusId = '1' 

	                    ";


			} else {

				$query4 = "SELECT 1 
							FROM SchStudentMandates  
						WHERE  StudentExtId = '{$student_ext_id}'
					 	AND  DOEStartDate = '{$mandate_start_date}'  
	 				/*	AND b.ServiceTypeDesc like '{$mandate_serv_type_desc}'	*/
	 					AND  DOEServiceTypeDesc like '{$mandate_serv_type_desc}'	
	 					
	 				/*	AND a.SessionGrpSize = '{$mandate_grp_size}'
	 					AND a.SessionFrequency = '{$mandate_serv_freq_num}'
	                     AND a.DOEFirstAttendDate = '{$mandate_first_attend_date}'  
	                     AND a.DOEProvider = '{$mandate_provider_name}' */ 
	                    AND StatusId = '1' 

	                    ";


			}	


 			$result4 = $connection->query ($query4);

		 	
			if (DB::isError($result4)){
						die("Could not query the database:<br />$query4 ".DB::errorMessage($result));
			}			
		 	
			$mandate_exists =& $result4->numRows();			
		 
			
			//=============================
			// Terminate Existing Mandate
			//============================



		/*	
			if (($mandate_exists == 1) && ($mandate_status_desc == 'Terminated'))  { // Inactivate Terminated Mandate  - Start

			$query2 = "UPDATE  SchStudentMandates a, SchServiceTypes b
						SET a.StatusId = '2'
					WHERE a.StudentExtId = '{$student_ext_id}'
					AND a.StartDate = '{$mandate_start_date}' 
 					AND b.ServiceTypeDesc like '{$mandate_serv_type_desc}'	
 					AND a.SessionGrpSize = '{$mandate_grp_size}'
                    AND a.ServiceTypeId = b.Id 
                     AND a.StatusId = '1' 
						 ";

						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			


			}	// Inactivate Terminated Mandate  - End

			*/

			/*==========  Get School Hours for for Para (Non-Transportation) type Services  ===========*/		

		  	

			if ($sesis_para_service_type_id == 1) { //Get Default Para Hours from School - Start  


				/*=== Mon Hours - Start ===*/
				$query_school_hrs = "SELECT   	 
	                     CASE   
	                      WHEN a.StartTime THEN a.StartTime
	                      ELSE '07:00:00'
	                    END AS SchoolStartTime,

	                    CASE  
	                      WHEN a.EndTime THEN a.EndTime 
	                      ELSE '15:00:00'
	                    END AS SchoolEndTime,

	                    CASE   
	                      WHEN a.TotalHours THEN a.TotalHours 
	                      ELSE '8.00'
	                    END AS SchoolTotalHours 
			           FROM  SchSchoolHours a
                       

			            RIGHT JOIN DaysOfWeek5 b
			            ON a.WeekDayId = b.WeekDayId
			            AND a.SchoolId = '{$SchoolId}'  
                        
                         WHERE b.WeekDayId = '1' 
				";
				
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_mon = $row_school_hrs['SchoolStartTime'];	
					$para_end_time_mon = $row_school_hrs['SchoolEndTime'];	
					$para_total_hours_mon = $row_school_hrs['SchoolTotalHours'];	


				}	

				/*=== Mon Hours - End ===*/

				/*=== Tue Hours - Start ===*/
				$query_school_hrs = "SELECT   	 
	                     CASE   
	                      WHEN a.StartTime THEN a.StartTime
	                      ELSE '07:00:00'
	                    END AS SchoolStartTime,

	                    CASE  
	                      WHEN a.EndTime THEN a.EndTime 
	                      ELSE '15:00:00'
	                    END AS SchoolEndTime,

	                    CASE   
	                      WHEN a.TotalHours THEN a.TotalHours 
	                      ELSE '8.00'
	                    END AS SchoolTotalHours 
			           FROM  SchSchoolHours a
                       

			            RIGHT JOIN DaysOfWeek5 b
			            ON a.WeekDayId = b.WeekDayId
			            AND a.SchoolId = '{$SchoolId}'  
                        
                         WHERE b.WeekDayId = '2' 
				";
				
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_tue = $row_school_hrs['SchoolStartTime'];	
					$para_end_time_tue = $row_school_hrs['SchoolEndTime'];	
					$para_total_hours_tue = $row_school_hrs['SchoolTotalHours'];	


				}	

				/*=== Tue Hours - End ===*/

				/*=== Wed Hours - Start ===*/
				$query_school_hrs = "SELECT   	 
	                     CASE   
	                      WHEN a.StartTime THEN a.StartTime
	                      ELSE '07:00:00'
	                    END AS SchoolStartTime,

	                    CASE  
	                      WHEN a.EndTime THEN a.EndTime 
	                      ELSE '15:00:00'
	                    END AS SchoolEndTime,

	                    CASE   
	                      WHEN a.TotalHours THEN a.TotalHours 
	                      ELSE '8.00'
	                    END AS SchoolTotalHours 
			           FROM  SchSchoolHours a
                       

			            RIGHT JOIN DaysOfWeek5 b
			            ON a.WeekDayId = b.WeekDayId
			            AND a.SchoolId = '{$SchoolId}'  
                        
                         WHERE b.WeekDayId = '3' 
				";
				
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_wed = $row_school_hrs['SchoolStartTime'];	
					$para_end_time_wed = $row_school_hrs['SchoolEndTime'];	
					$para_total_hours_wed = $row_school_hrs['SchoolTotalHours'];	


				}	

				/*=== Wed Hours - End ===*/

				/*=== Thu Hours - Start ===*/
				$query_school_hrs = "SELECT   	 
	                     CASE   
	                      WHEN a.StartTime THEN a.StartTime
	                      ELSE '07:00:00'
	                    END AS SchoolStartTime,

	                    CASE  
	                      WHEN a.EndTime THEN a.EndTime 
	                      ELSE '15:00:00'
	                    END AS SchoolEndTime,

	                    CASE   
	                      WHEN a.TotalHours THEN a.TotalHours 
	                      ELSE '8.00'
	                    END AS SchoolTotalHours 
			           FROM  SchSchoolHours a
                       

			            RIGHT JOIN DaysOfWeek5 b
			            ON a.WeekDayId = b.WeekDayId
			            AND a.SchoolId = '{$SchoolId}'  
                        
                         WHERE b.WeekDayId = '4' 
				";
				
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_thu = $row_school_hrs['SchoolStartTime'];	
					$para_end_time_thu = $row_school_hrs['SchoolEndTime'];	
					$para_total_hours_thu = $row_school_hrs['SchoolTotalHours'];	


				}	

 
				/*=== Thu Hours - End ===*/

				/*=== Fri Hours - Start ===*/
				$query_school_hrs = "SELECT   	 
	                     CASE   
	                      WHEN a.StartTime THEN a.StartTime
	                      ELSE '07:00:00'
	                    END AS SchoolStartTime,

	                    CASE  
	                      WHEN a.EndTime THEN a.EndTime 
	                      ELSE '15:00:00'
	                    END AS SchoolEndTime,

	                    CASE   
	                      WHEN a.TotalHours THEN a.TotalHours 
	                      ELSE '8.00'
	                    END AS SchoolTotalHours 
			           FROM  SchSchoolHours a
                       

			            RIGHT JOIN DaysOfWeek5 b
			            ON a.WeekDayId = b.WeekDayId
			            AND a.SchoolId = '{$SchoolId}'  
                        
                         WHERE b.WeekDayId = '5' 
				";
				
				$result_school_hrs = $connection->query ($query_school_hrs);
				if (DB::isError($result_school_hrs)){
			                die("Could not query the database:<br />$query ".DB::errorMessage($result_school_hrs));
			    }

				while ($row_school_hrs =& $result_school_hrs->fetchRow (DB_FETCHMODE_ASSOC)) {

					$para_start_time_fri = $row_school_hrs['SchoolStartTime'];	
					$para_end_time_fri = $row_school_hrs['SchoolEndTime'];	
					$para_total_hours_fri = $row_school_hrs['SchoolTotalHours'];	


				}	

 
				/*=== Fri Hours - End ===*/

 
			} //Get Default Para Hours from School - End	
		 
			//=======================
			// Add New Mandate
			//=======================
			

			echo ' mand. Exists: '.$mandate_exists.' mand. status: '.$mandate_status_desc. ' service type id:  '.$service_type_id; 

			if (($mandate_exists == 0) && ($mandate_status_desc != 'Terminated') && ($service_type_id))  { // Mandate not exists - Start
			 


			$query2 = "INSERT INTO SchStudentMandates
						(	
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId, 
							DOEServiceTypeDesc,
							DOEFirstAttendDate,
							DOEStartDate,
							DOEProvider,
							DOESchoolId,
							BillingContractId,
							ParaTransportPerc,

					       ParaStartTimeMon1,
					       ParaEndTimeMon1,
					       ParaTotalHoursMon1,
					       ParaStartTimeTue1,
					       ParaEndTimeTue1,
					       ParaTotalHoursTue1,
			           	   ParaStartTimeWed1,
			               ParaEndTimeWed1,
			               ParaTotalHoursWed1,
			               ParaStartTimeThu1,
			               ParaEndTimeThu1,
			               ParaTotalHoursThu1,
			               ParaStartTimeFri1,
			               ParaEndTimeFri1,
			               ParaTotalHoursFri1,

							UserId,
							TransDate
						) 

						VALUES (

						 	'0',
							'{$student_ext_id}',
						/*	 
							 COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),
						*/
							'{$school_id}',
							'1',
							'{$service_type_id}',

							'{$registrant_id}',
							
							'{$mandate_first_attend_date}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN '{$service_type_ind}'
								ELSE '{$service_type_grp}'
							END,
							'{$mandate_serv_type_desc}',
							'{$mandate_first_attend_date}',
							'{$mandate_start_date}',
							
							'{$mandate_provider_name}',
							'{$mandate_school_id}',
							'{$billing_contract_id}',
							'{$para_transport_perc}',

							'{$para_start_time_mon}',
							'{$para_end_time_mon}',
							'{$para_total_hours_mon}',


							'{$para_start_time_tue}',
							'{$para_end_time_tue}',
							'{$para_total_hours_tue}',

							'{$para_start_time_wed}',
							'{$para_end_time_wed}',
							'{$para_total_hours_wed}',

							'{$para_start_time_thu}',
							'{$para_end_time_thu}',
							'{$para_total_hours_thu}',

							'{$para_start_time_fri}',
							'{$para_end_time_fri}',
							'{$para_total_hours_fri}',

							'{$user_id}',
							now()  

							)";
						
						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			



						/*==========  Add New Assignments for Para type Services  ===========*/		

				/*		

						if (($sesis_para_service_type_id != 0) && ($registrant_id != 0)) { //Add New Para Assignments - Start  


						$query_assign_id = "  SELECT Id as AssignServiceTypeId

										   FROM SchServiceTypes	
				   						where SESISServiceTypeDesc  like '{$mandate_serv_type_desc}' LIMIT 1 	 
						";
						
						$result_assign_id = $connection->query ($query_assign_id);
						if (DB::isError($result)){
					                die("Could not query the database:<br />$query ".DB::errorMessage($result_assign_id));
					    }

						while ($row_assign_id =& $result_assign_id->fetchRow (DB_FETCHMODE_ASSOC)) {


							$assign_service_type_id = $row_assign_id['AssignServiceTypeId'];	

							$query_crt_assign = "  call proc_setSchStudentAssignmentFromMandate ( 	'{$student_ext_id}', 
													                                                '{$mandate_first_attend_date}',
																									'{$EndDate}',
													                                                '{$assign_service_type_id}',
													                                                '{$registrant_id}',
													                                                '{$user_id}')  	 
												 ";

 							
							$result_crt_assign = $connection->query ($query_crt_assign);
							if (DB::isError($result)){
						                die("Could not query the database:<br />$query ".DB::errorMessage($result_crt_assign));
						    }



 

						}	

					}	//Add New Para Assignments - End
				*/

				} 

				/*===================================================================*

				} // Mandate not exists - End	        
		 
				/*=========================*/

			 	
			 
			} // m_rec - end



		} // while - end	


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result3)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result));
			        }


	$connection->disconnect();
 
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

?>