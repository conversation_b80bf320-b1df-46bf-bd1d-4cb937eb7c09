<?php 

require_once("db_GetSetData.php");

	$conn = getCon();

	$DistrictId = $_GET['DistrictId'];

				if(!is_numeric($DistrictId)) {

                        $query = "SELECT 	a.Id as id, 
											a.Id, 
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ',a.ExtId ) as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId,
											SesisSchoolId
											 
								FROM        SchSchools a,
											SchDistricts b
								WHERE       a.DistrictId = b.Id	
							    
							    ORDER BY    TRIM(SchoolName)  ";

				} else {

                        $query = "SELECT 	a.Id as id, 
											a.Id, 
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ',a.ExtId ) as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId,
											SesisSchoolId 
											 
								FROM        SchSchools a,
											SchDistricts b
								WHERE       b.Id = '{$DistrictId}'
								AND         a.DistrictId = b.Id	
							    
							    ORDER BY    TRIM(SchoolName)  ";

				}									
					 	
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

	// require "ewDataHandler.php";  
	  
	// $rcr_transaction = new dataHandler(); 

	// $DistrictId = $_GET['DistrictId'];

	// $result = $rcr_transaction->getSchSchools($DistrictId);

	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";


?>
