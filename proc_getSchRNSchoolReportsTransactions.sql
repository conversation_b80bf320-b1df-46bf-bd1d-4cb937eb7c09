/*=========================================*/

        DELIMITER $$

        DROP PROCEDURE IF EXISTS proc_getSchRNSchoolReportsTransactions$$

        CREATE PROCEDURE proc_getSchRNSchoolReportsTransactions (IN    
                                                                p_from_date DATE, 
                                                                p_to_date DATE,
                                                                p_service_types varchar(96), 
                                                                
                                                                p_district_id INT,
                                                                p_school_id INT, 
                                                                p_serv_type_id INT,
                                                                p_rn_id INT,
                                                                p_liaison_id INT,
                                                                p_posting_status_id char(1),
                                                                p_conf_number VARCHAR(512) 
                                                                 
                                                            )  
                                                                 

                                                          
                                                           
                                                            
        BEGIN

        DECLARE v_LongTermNurseId INT ; 

        

                create temporary table tmp_trans engine=memory
                

                                  SELECT 
                            a.Id,
                            a.ServiceTypeId,
                            a.AssignmentId,
                            a.ServiceDate,
                            a.StartTime,
                            a.EndTime,
                            a.TotalHours,
                            a.StudentId,
                            a.RegistrantId,
                            a.SchoolId,
                            a.ConfirmationNumber,
                             case a.ServiceTypeId
                              when '39' then 1
                              when '40' then 2
                              when '41' then 3
                              when '42' then 4
                              when '43' then 5
                              when '44' then 6
                              when '45' then 6
                            end as ServiceTypeGroupList,
                            TRIM(c.SchoolName) as SchoolName,
                            c.Extid as SchoolDBN,
                            c.DistrictId,
                            e.RNLiaisonId,
                            CASE 
                              when e.RNLiaisonId is not null then CONCAT( trim( g.LastName) , '  ', trim( g.FirstName))
                              else 'Undefined...'
                            end as LiaisonName,
                            TRIM (DistrictName) as DistrictName,
                            CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as ScheduledRNName,
                            c.RNSchoolTypeId,
                            COALESCE(h.PostingStatusId, '0') as PostingStatusId,
                            case h.PostingStatusId
                              when '1' then 'Posted Verified'
                              when '2' then 'Posted Un-Verified'
                              else 'Unposted'
                            end as PostingStatusDesc,
                            COALESCE(h.PostingStatusComments,'') as PostingStatusComments   
 

                        FROM
                            WeeklyServices a  
                                JOIN
                            Registrants b ON a.RegistrantId = b.Id
                                JOIN
                            SchSchools c ON a.SchoolId = c.Id
                                JOIN
                            SchDistricts d ON c.DistrictId = d.Id
                                JOIN
                            RegistrantTypes f ON b.TypeId = f.Id    
                                LEFT JOIN 
                            SchRNDistrictServiceLiaisons e ON e.DistrictId = c.DistrictId
                                                         and   e.RNSchoolTypeId = c.RNSchoolTypeId 
                                                         and   e.ServiceTypeId =  a.ServiceTypeId       
                                LEFT JOIN  
                            SchRNSchoolLiaisons g ON e.RNLiaisonId = g.Id   
                                LEFT JOIN
                            SchRNTransactionPostings h on a.Id = h.ScheduleId  


                        WHERE
                            a.ServiceDate BETWEEN p_from_date AND p_to_date
                                 
                                AND FIND_IN_SET(a.ServiceTypeId, p_service_types)
                                AND b.TypeId = 12   
    
              ;

 



        /*============*/            
                create temporary table tmp engine=memory
                
            select   
                    a.ServiceTypeId,
                    a.SchoolId,
                    a.AssignmentId,
                    case  
                     when a.AssignmentId !=  0 Then 'Long'
                     else 'Short' 
                    end as AssignmentType, 
                    CAST('' AS CHAR(10)) AS LongTermRnId, 
                    CAST('' AS CHAR(512)) AS LongTermRnName, 

                    a.Id as ScheduleId,
                    CONCAT(DATE_FORMAT( a.StartTime, '%l:%i %p' ),'-',(DATE_FORMAT( a.EndTime, '%l:%i %p' )),' (',a.TotalHours,' Hrs)') as ScheduleDesc,   
                    a.ServiceDate, 
                    a.RegistrantId as ScheduledRNId,  
                    a.ScheduledRNName,
                    COALESCE(CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)),'All') as StudentName,
                    a.StudentId,
                    COALESCE(c.ExtId,'') as StudentOsisNumber,
                    a.DistrictId,
                    a.DistrictName,
                    a.RNLiaisonId,
                    a.LiaisonName,
                    a.SchoolName,
                    a.SchoolDBN,
                    CASE 
                      when CallInStatus is null then 'Unconfirmed'
                      else CallInStatus
                    end as CallInStatus,

                    d.Id as CallInTimeId,
                    COALESCE(DATE_FORMAT( d.CallInTime, '%l:%i %p' ),'') as CallInTime,
                    COALESCE(CAST(d.CallInComments as char(512)),'') as CallInComments,
                    h.ServiceTypeDesc,
                    a.ConfirmationNumber,
                    a.ServiceTypeGroupList,
                    a.RNSchoolTypeId,
                    a.PostingStatusId,
                    a.PostingStatusDesc,
                    a.PostingStatusComments     

            from tmp_trans a
                    
                   LEFT JOIN
                 SchStudents c on a.StudentId = c.Id 
                   JOIN
                 SchServiceTypes h ON a.ServiceTypeId = h.Id
             
                   LEFT JOIN
                SchRNDailyCallInTimes d on a.RegistrantId = d.RegistrantId
                                       and a.ServiceDate = d.ServiceDate   

            -- where a.DistrictId in (p_district_id_list)   
            -- where  FIND_IN_SET(a.ServiceTypeGroupList, p_service_type_group_list)                          
                ;
         

        /* Get long Term Nurse Id - School*/
        /*=================================*/
        update tmp a
           set a.LongTermRnId = (
           
                Select b.RegistrantId
                  FROM SchSchoolAssignmentDetails b
                  WHERE a.AssignmentId = b.AssignmentId
                  LIMIT 1   
                   
           )
        WHERE a.AssignmentId != 0
        AND a.ServiceTypeId between 39 and 42;       

     

        /* Get long Term Nurse Id - Student*/
        /*=================================*/

        update tmp a
           set a.LongTermRnId = (
           
                Select b.RegistrantId
                  FROM SchStudentAssignmentDetails b
                  WHERE a.AssignmentId = b.AssignmentId
                  LIMIT 1   
                   
           )
        WHERE a.AssignmentId != 0
        AND a.ServiceTypeId > 42;    
     

        /* Get long Term Nurse Name*/
        /*=================================*/

        update tmp a,
               Registrants b
         set a.LongTermRnName = CONCAT( trim( b.LastName) , ', ', trim( b.FirstName))      
           
        WHERE a.AssignmentId != 0
        AND   a.LongTermRnId = b.Id
         ;       


 
        SET @q = CONCAT("select   
                ServiceTypeGroupList,
                ServiceTypeId,
                ScheduleId,
                ServiceDate,
                DistrictId,
                DistrictName,
                RNLiaisonId,
                LiaisonName,
                ScheduledRNId,
                SchoolId,
                SchoolName,
                SchoolDBN,
                CallInTime,
                CallInStatus,
                LongTermRnName,
                StudentName,
                StudentOsisNumber,
                group_concat( ScheduleDesc SEPARATOR ', ' ) as 'ScheduleDesc',
                group_concat( ScheduleId SEPARATOR ', ' ) as 'ScheduleIdList',
                ConfirmationNumber, 
                group_concat( ServiceTypeDesc SEPARATOR ', ' ) as 'Placement',
                AssignmentType,
                CallInComments,
                PostingStatusId,
                PostingStatusDesc,
                PostingStatusComments                    

     
        from tmp 
 
         ");


            SET @g = CONCAT(" GROUP BY ScheduledRNId, ServiceDate,   ServiceTypeGroupList
                                ORDER BY LiaisonName,ServiceDate
            ");

     
            /* Filter By District*/ 
            /*=====================*/
            IF (p_district_id is not null)    THEN 

                SET @s = CONCAT(@q, ' Where DistrictId = ', p_district_id, @g);                      

            END IF;

            /* Filter By School*/ 
            /*=====================*/
            IF (p_school_id is not null )    THEN 

                SET @s = CONCAT(@q, ' Where SchoolId = ', p_school_id, @g);                      

            END IF;

            /* Filter By Service Type*/ 
            /*=====================*/
            IF (p_serv_type_id is not null)    THEN 

                SET @s = CONCAT(@q, ' Where ServiceTypeId = ', p_serv_type_id, @g);                      

            END IF;

            /* Filter By RN*/ 
            /*=====================*/
            IF (p_rn_id is not null)    THEN 

                SET @s = CONCAT(@q, ' Where ScheduledRNId = ', p_rn_id, @g);                         

            END IF;

            /* Filter By Conf #*/ 
            /*=====================*/
            IF (p_conf_number is not null)    THEN 

                SET @s = CONCAT(@q, ' Where ConfirmationNumber like ','"%', p_conf_number,'%"',@g);                      

            END IF;

            /* Filter By Liaison #*/ 
            /*=====================*/
            IF (p_filter_type = 6)    THEN 

                SET @s = CONCAT(@q, ' Where RNLiaisonId = ', p_filter_value_int, @g);                        

            END IF;


            /* Filter By Posting Status*/ 
            /*=====================*/
            IF (p_filter_type = 7)    THEN 

                SET @s = CONCAT(@q, ' Where PostingStatusId = ', p_filter_value_int, @g);                        

            END IF;


            PREPARE stmt FROM @s;
            EXECUTE stmt ;
            DEALLOCATE PREPARE stmt;

            -- select @s;

        drop temporary table if exists tmp;
        drop temporary table if exists tmp_trans;


        END $$

        DELIMITER ;      
        
         