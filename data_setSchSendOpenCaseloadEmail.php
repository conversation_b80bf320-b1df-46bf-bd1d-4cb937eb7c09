<?php 


  require_once("db_GetSetData.php");


	  // error_reporting(E_ALL);
	  // ini_set('display_errors', TRUE);
	  // ini_set('display_startup_errors', TRUE);

	  $Subject = $_POST['Subject'];
	  $Message = $_POST['Message'];
	  $RecruiterEmail = $_POST['RecruiterEmail'];



	  $SchoolId = $_POST['SchoolId'];
	  $ServiceTypeId = $_POST['ServiceTypeId'];
	  $LastEmailId = $_POST['LastEmailId'];
	  
	  $TransmitDate = $_POST['TransmitDate'];
	  $UserId = $_POST['UserId'];
	  $UserGroup = $_POST['UserGroup'];
	  $UserEmail = $_POST['UserEmail'];


	  $Data = $_POST['Data'];
	  $Data=json_decode($Data,true);


	 
	 
  //==========================


  $search_id =  generateRandomString();
 	

  // Set Caseload Text Header
  //==========================

	$conn = getCon();


	if ($LastEmailId) {

		$query ="UPDATE SchOpenCaseloadsEmailHeader
					SET UserId = '{$UserId}',
					    TransDate = NOW()  		
 				WHERE Id = '{$LastEmailId}'
						";

	} else {

		$query ="INSERT INTO SchOpenCaseloadsEmailHeader
						( SearchId,
						  SchoolId,
						  ServiceTypeId,
						  TransmitDate,
						  UserId,
						  TransDate)
						VALUES
						( 
						'{$search_id}',
						'{$SchoolId}',
						'{$ServiceTypeId}',
						'{$TransmitDate}',
						'{$UserId}',
						now()
						) 
						";


	}

						

	echo ' query  '.$query.'<br><br>';						

	$ret =  setData ($conn, $query);   			
 
	setDisConn($conn);
 
  // Set Caseload Email Details
  //==========================


  	// foreach ($Data as $EmailContactId) {
 	foreach ($Data as $Candidates) {

 			 $EmailContactId =  $Candidates['EmailContactId'];
			 $EmailAddress =  $Candidates['EmailAddress'];
  			 	

				 




				$conn = getCon();

				if ($LastEmailId) {

				$query1 ="INSERT INTO  SchOpenCaseloadsEmailDetails
								( 
									EmailHdrId,
									EmailContactId 
								)
									
								SELECT 	  '{$LastEmailId}',
								   		    '{$EmailContactId}' 

								";


				} else {

				$query1 ="INSERT INTO  SchOpenCaseloadsEmailDetails
								( 
									EmailHdrId,
									EmailContactId
								)
									
								SELECT 	  Id,
								   		 '{$EmailContactId}'	
								FROM SchOpenCaseloadsEmailHeader 
								WHERE SearchId =  '{$search_id}'	   		 

								";


				}	

					echo ' query1  '.$query1.'<br><br>';						


				
				$ret1 =  setData ($conn, $query1);   			
			 
				setDisConn($conn);

					$to      = $EmailAddress;
	
					$subject = $Subject;
					$message = $Message;
					$headers = 'From: <EMAIL>' . "\r\n" .
					    'Reply-To: <EMAIL>' . "\r\n" .
					    'X-Mailer: PHP/' . phpversion();

					mail($to, $subject, $message, $headers);

					sleep(3);

    }



 


	function generateRandomString($length = 15) {
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}	


?>



 