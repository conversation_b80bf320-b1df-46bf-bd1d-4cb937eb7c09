<?php
    
    require_once("db_GetSetData.php");


    // Correct Assignemnt IDs
    //=========================
    $conn = getCon();

  
    $sql = "UPDATE    WeeklyServices a,
          SchStudentAssignmentHeader b
           set a.AssignmentId = b.Id
        where a.ScheduleStatusId >= 7
        and a.ServiceTypeId < 39  
        and a.MandateId = 0
        and a.AssignmentId = 0
        and a.ServiceDate >= '2024-08-01'  
        and a.StudentId = b.StudentId
    ";
    
    if ($conn->query($sql) === TRUE) {
        echo "Query1 executed successfully";
    } else {
        echo "Error: " . $sql . "<br>" . $conn->error;
    }

   setDisConn($conn);


   // Correct Assignemnt IDs
    //=========================
    $conn = getCon();

  
    $sql = "UPDATE   SchStudents a
           set a.SchoolId  =  
            
            (
            select b.SchoolId from SchStudentMandates b 
                                     join
                                   Registrants c On b.RegistrantId = c.Id  
               where a.Id = b.StudentId 
                 and c.TypeId = 23
                  and b.StatusId = '1'
                 and curdate() between b.StartDate and b.EndDate 
                 limit 1
         
         ) 

         where exists (
         select 1 from SchStudentMandates d 
                 where a.Id = d.StudentId 
                 and d.StatusId = '1'
                 and   curdate() between d.StartDate and d.EndDate
         )
         
         
           and a.SchoolId !=  
            
            (
            select b.SchoolId from SchStudentMandates b 
                                     join
                                   Registrants c On b.RegistrantId = c.Id  
               where a.Id = b.StudentId 
                 and c.TypeId = 23
                  and b.StatusId = '1'
                 and curdate() between b.StartDate and b.EndDate 
                 limit 1
         
         ) 
    ";
    
    if ($conn->query($sql) === TRUE) {
        echo "Query2 executed successfully";
    } else {
        echo "Error: " . $sql . "<br>" . $conn->error;
    }

   setDisConn($conn);


 

?>