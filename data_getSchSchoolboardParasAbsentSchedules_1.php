<?php 
	

    require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
	$SchoolId = $_GET['SchoolId'];
	$RegistrantId = $_GET['RegistrantId'];
	$StudentId = $_GET['StudentId'];

	if ($SchoolId) {

				   $query = "	SELECT  
								 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
								 		ServiceDate as ServiceDateSort,
								 		StartTime as StartTimeNum,
						 		  		EndTime as EndTimeNum,
								 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
								 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										a.TotalHours, 
										a.WeekDay, 
										ServiceTypeDesc,
				                         DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
											FROM SchSchools f, SchDistricts g
											WHERE a.SchoolId = f.Id
											AND   f.DistrictId = g.Id),'') as SchoolName,   

				 						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName,  
				 
				  
										a.RegistrantId, 
										CONCAT( trim( d.LastName) , ', ', trim( d.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,   
				  						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
										a.TransDate


						FROM 	WeeklyServices a, 
								SchServiceTypes b,
								SchStudents c,
								Users e,
							    Registrants d, 
							    RegistrantTypes f
						WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
						    AND a.ScheduleStatusId = '7'
							AND a.UserId = e.UserId
				 			AND a.ServiceTypeId = b.Id	
							AND b.ServiceCategoryId = 1
							AND a.StudentId = c.Id
							AND d.TypeId = 23  	
				            AND a.Registrantid = d.Id
							AND b.RegistrantTypeId = f.Id	
							AND a.SchoolId = '{$SchoolId}'

						ORDER BY StudentName, a.ServiceDate, a.StartTime	
							  ";
	}

	if ($RegistrantId) {

   $query = "	SELECT  
				 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
				 		ServiceDate as ServiceDateSort,
				 		StartTime as StartTimeNum,
		 		  		EndTime as EndTimeNum,
				 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
				 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						a.TotalHours, 
						a.WeekDay, 
						ServiceTypeDesc,
                         DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
						COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
							FROM SchSchools f, SchDistricts g
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = g.Id),'') as SchoolName,   

 						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName,  
 
  
						a.RegistrantId, 
						CONCAT( trim( d.LastName) , ', ', trim( d.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,   
  						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						a.TransDate


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
				Users e,
			    Registrants d, 
			    RegistrantTypes f
		WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
		    AND a.ScheduleStatusId = '7'
			AND a.UserId = e.UserId
 			AND a.ServiceTypeId = b.Id	
			AND b.ServiceCategoryId = 1
			AND a.StudentId = c.Id
			AND d.TypeId = 23  	
            AND a.Registrantid = d.Id
			AND b.RegistrantTypeId = f.Id	
			 
			AND a.RegistrantId = '{$RegistrantId}'
			 

		ORDER BY StudentName, a.ServiceDate, a.StartTime	
			  ";	}

	if ($StudentId) {


				    $query = "	SELECT  
								 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
								 		ServiceDate as ServiceDateSort,
								 		StartTime as StartTimeNum,
						 		  		EndTime as EndTimeNum,
								 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
								 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										a.TotalHours, 
										a.WeekDay, 
										ServiceTypeDesc,
				                         DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
											FROM SchSchools f, SchDistricts g
											WHERE a.SchoolId = f.Id
											AND   f.DistrictId = g.Id),'') as SchoolName,   

				 						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName,  
				 
				  
										a.RegistrantId, 
										CONCAT( trim( d.LastName) , ', ', trim( d.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,   
				  						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
										a.TransDate


						FROM 	WeeklyServices a, 
								SchServiceTypes b,
								SchStudents c,
								Users e,
							    Registrants d, 
							    RegistrantTypes f
						WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
						    AND a.ScheduleStatusId = '7'
							AND a.UserId = e.UserId
				 			AND a.ServiceTypeId = b.Id	
							AND b.ServiceCategoryId = 1
							AND a.StudentId = c.Id
							AND d.TypeId = 23  	
				            AND a.Registrantid = d.Id
							AND b.RegistrantTypeId = f.Id	
							 
							AND a.StudentId = '{$StudentId}'

						ORDER BY StudentName, a.ServiceDate, a.StartTime	
							  ";	
	}

    

	if (!$query)


    $query = "	SELECT  
				 		DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
				 		ServiceDate as ServiceDateSort,
				 		StartTime as StartTimeNum,
		 		  		EndTime as EndTimeNum,
				 		DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
				 		DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						a.TotalHours, 
						a.WeekDay, 
						ServiceTypeDesc,
                         DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
						COALESCE((SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
							FROM SchSchools f, SchDistricts g
							WHERE a.SchoolId = f.Id
							AND   f.DistrictId = g.Id),'') as SchoolName,   

 						CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName,  
 
  
						a.RegistrantId, 
						CONCAT( trim( d.LastName) , ', ', trim( d.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,   
  						CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
						a.TransDate


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
				Users e,
			    Registrants d, 
			    RegistrantTypes f
		WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
		    AND a.ScheduleStatusId = '7'
			AND a.UserId = e.UserId
 			AND a.ServiceTypeId = b.Id	
			AND b.ServiceCategoryId = 1
			AND a.StudentId = c.Id
			AND d.TypeId = 23  	
            AND a.Registrantid = d.Id
			AND b.RegistrantTypeId = f.Id	
		/*	
			AND a.SchoolId like '{$SchoolId}'
			AND a.RegistrantId like '{$RegistrantId}'
			AND a.StudentId like '{$StudentId}' */

		ORDER BY StudentName, a.ServiceDate, a.StartTime	
			  ";

	//$ret = getData ($conn, $query);
	
	setDisConn($conn);

	//echo $ret;
    echo $query;




?>
