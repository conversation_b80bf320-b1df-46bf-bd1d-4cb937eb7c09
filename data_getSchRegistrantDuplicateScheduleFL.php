<?php 

	require "ewDataHandler.php"; 
	  
	$rcr_transaction = new dataHandler(); 


	$RegistrantId = $_GET['RegistrantId'];
	$ServiceDate = $_GET['ServiceDate'];
	$StartTime = $_GET['StartTime'];
	$ServiceTypeId = $_GET['ServiceTypeId'];
	

	$result = $rcr_transaction->getSchRegistrantDuplicateScheduleFL($RegistrantId, 
																	$ServiceDate,
																	$StartTime,
																	$ServiceTypeId);

	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";

?>
