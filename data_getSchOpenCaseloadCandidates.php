	<?php 

   require_once("db_GetSetData.php");

	$conn = getCon();

	$ServiceTypeId = $_GET['ServiceTypeId'];  
	$SchoolZipCode = $_GET['SchoolZipCode'];  
	$LastEmailId = $_GET['LastEmailId'];  
	$UserId = $_GET['UserId'];  
	$UserGroup = $_GET['UserGroup'];  


	$query =  "CALL proc_getSchOpenCaseloadCandidates ('{$SchoolZipCode}',
														'{$ServiceTypeId}',
														'{$LastEmailId}',
														'{$UserId}',
														'{$UserGroup}'
												 	  )";       

 	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	  
	  

?>
