<?php
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);

include 'db_login.php';

try {
    // Enable buffered queries by adding PDO::MYSQL_ATTR_USE_BUFFERED_QUERY
    $conn = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=utf8", $db_username, $db_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
    ]);

    $days = [
        1 => ['name' => 'Mon', 'WeekDayId' => 1],
        2 => ['name' => 'Tue', 'WeekDayId' => 2],
        3 => ['name' => 'Wed', 'WeekDayId' => 3],
        4 => ['name' => 'Thu', 'WeekDayId' => 4],
        5 => ['name' => 'Fri', 'WeekDayId' => 5]
    ];

    foreach ($days as $day) {
        // Fetch rows for the current day
        $readStmt = $conn->query("SELECT 
                                        b.Id,
                                        d.Id AS MandateId,
                                        ParaTransportPerc,
                                        b.StartTime,
                                        b.EndTime,
                                        b.TotalHours,
                                        FORMAT(b.TotalHours * (ParaTransportPerc / 100), 2) AS CalcTotalHours,
                                        ParaStartTime" . $day['name'] . "1,
                                        ParaEndTime" . $day['name'] . "1,
                                        ParaTotalHours" . $day['name'] . "1,
                                        ParaStartTime" . $day['name'] . "2,
                                        ParaEndTime" . $day['name'] . "2,
                                        ParaTotalHours" . $day['name'] . "2,
                                        (ParaTotalHours" . $day['name'] . "1 + ParaTotalHours" . $day['name'] . "2) AS TotalHours" . $day['name'] . "
                                   FROM
                                        SchSchoolHours b
                                        JOIN SchStudents c ON b.SchoolId = c.SchoolId
                                        AND b.SubSchoolTypeId = c.SubSchoolTypeId
                                        JOIN SchStudentMandates d ON c.Id = d.StudentId AND d.StatusId = '1'
                                        AND CURDATE() BETWEEN d.StartDate AND d.EndDate
                                        AND d.ServiceTypeId BETWEEN 17 AND 21
                                   WHERE
                                           curdate() BETWEEN d.StartDate and d.EndDate 
                                    and    d.StatusId = '1'  
                                    and    c.Id = 24406
                                    and    b.WeekDayId = " . $day['WeekDayId']

                                );

        $rows = $readStmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($rows as $row) {
            


            $MandateId = $row['MandateId'];
            $morningStartTime = $row['StartTime'];
            // $afternoonEndTime = $row['EndTime'];
            $totalHours = (float)($row['CalcTotalHours']);

            // Convert total hours to seconds
            $total_seconds = $totalHours * 3600;

            // $afternoonEndTime = date('H:i:s', strtotime($morningStartTime . " + {$totalHours} hours"));
            $afternoonEndTime = date('H:i:s', strtotime($morningStartTime) + $total_seconds);

            

            echo "Start time: $morningStartTime | End time: $afternoonEndTime | Total hours: $totalHours for {$day['name']}<br>";

            // Call stored procedure and fetch the result
            $procStmt = $conn->prepare("CALL proc_setSchParaCalcSplitHours(:MorningStartTime, :AfternoonEndTime, :TotalHours)");
            $procStmt->bindParam(':MorningStartTime', $morningStartTime);
            $procStmt->bindParam(':AfternoonEndTime', $afternoonEndTime);
            $procStmt->bindParam(':TotalHours', $totalHours);
            $procStmt->execute();
            $result = $procStmt->fetch(PDO::FETCH_ASSOC);
            $procStmt->closeCursor(); // Close cursor to release server resources

            // Update table with returned values
            $updateStmt = $conn->prepare("UPDATE SchStudentMandates 
                                          SET ParaStartTime" . $day['name'] . "1 = :StartTimeAM,
                                              ParaEndTime" . $day['name'] . "1 = :EndTimeAM,
                                              ParaTotalHours" . $day['name'] . "1 = :TotalHoursAM,
                                              ParaStartTime" . $day['name'] . "2 = :StartTimePM,
                                              ParaEndTime" . $day['name'] . "2 = :EndTimePM,
                                              ParaTotalHours" . $day['name'] . "2 = :TotalHoursPM
                                          WHERE Id = :MandateId");

            $updateStmt->bindParam(':StartTimeAM', $result['StartTimeAM']);
            $updateStmt->bindParam(':EndTimeAM', $result['EndTimeAM']);
            $updateStmt->bindParam(':TotalHoursAM', $result['TotalHoursAM']);
            $updateStmt->bindParam(':StartTimePM', $result['StartTimePM']);
            $updateStmt->bindParam(':EndTimePM', $result['EndTimePM']);
            $updateStmt->bindParam(':TotalHoursPM', $result['TotalHoursPM']);
            $updateStmt->bindParam(':MandateId', $MandateId);
            $updateStmt->execute();
        }
        echo "Processed " . $day['name'] . "<br>";
    }

    echo "Processing completed for all days.";

} catch (PDOException $e) {
    echo "Connection error: " . $e->getMessage();
}

// Close connection
$conn = null;
?>
