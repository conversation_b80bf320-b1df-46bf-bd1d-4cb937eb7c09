	<?php

	// error_reporting(E_ALL);
	// ini_set('display_errors', TRUE);
	// ini_set('display_startup_errors', TRUE);

	require_once("db_GetSetData.php");


	$clientId = "35dc1fe2-02d4-432f-a5d4-71e876f1c6b2";
	$responseType = "code";
	$action = "Login";
	$clientSecret = "qnCZuTw0ZCxGGSgVuOjzgI59";
	$username = "rcmhealthcare.api";
	$password = "Welcome123!";
	$redirectUri = "https://www.ewebstaffing.com/rcm/data/RedirectlBullhornAPI.php";

	$code = getCode($clientId, $responseType, $action, $username, $password, $redirectUri);
	$access_token = getToken($code, $clientId, $clientSecret, $redirectUri);
	list($BhRestToken, $rest_url) = getRestUrl($access_token);



	$data = getRecentPlacements($rest_url, $BhRestToken);

 

	processPlacements($data, $rest_url, $BhRestToken);

	  	
	// echo 'After processPlacements<br>';


	function getCode($clientId, $responseType, $action, $username, $password, $redirectUri) {
		$url_code = "https://auth-east.bullhornstaffing.com/oauth/authorize?client_id=" . urlencode($clientId) . "&response_type=" . urlencode($responseType) . "&action=" . urlencode($action) . "&username=" . urlencode($username) . "&password=" . urlencode($password) . "&redirect_uri=" . urlencode($redirectUri);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url_code);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		$response = curl_exec($ch);
		curl_close($ch);
		return $response;
	}

	function getToken($code, $clientId, $clientSecret, $redirectUri) {
		$grantType = "authorization_code";
		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://auth.bullhornstaffing.com/oauth/token?grant_type='. $grantType. '&code=' . $code . '&client_id=' . $clientId .'&client_secret=' . $clientSecret .'&redirect_uri=' . $redirectUri,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		));
		$response1 = curl_exec($curl);
		$result1 = json_decode($response1, true);
		return $result1["access_token"];	
	}

	function getRestUrl($access_token) {
		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://rest.bullhornstaffing.com/rest-services/login?version=2.0&access_token=' . $access_token,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		));
		$response2 = curl_exec($curl);
		$result2 = json_decode($response2, true);
		return array($result2["BhRestToken"], $result2["restUrl"]);
	}

	function getRecentPlacements($rest_url, $BhRestToken) {
		$curl = curl_init();
		curl_setopt_array($curl, array(
		  // CURLOPT_URL =>   $rest_url.'search/Placement?BhRestToken='.$BhRestToken . '&fields=id,candidate,jobOrder&query=dateAdded:[20230825%20TO%20*]&count=1000',
		  CURLOPT_URL =>   $rest_url.'search/Placement?BhRestToken='.$BhRestToken . '&fields=id,candidate,status,jobOrder&query=dateLastModified:[20240301%20TO%20*]&count=2000',
		  
		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));
		$response4 = curl_exec($curl);
		curl_close($curl);
		return json_decode($response4, true);
	}

	function processPlacements($data, $rest_url, $BhRestToken) {
		foreach ($data['data'] as $item) {
		    $jobOrderId = $item['jobOrder']['id'];
		    $candidateId = $item['candidate']['id'] ?? null;
		    $candidate_first_name = $item['candidate']['firstName']; 
		    $candidate_last_name = $item['candidate']['lastName']; 
		    $placement_status = $item['status']; 
		    $placement_job_order = $item['jobOrder']['id']; 

		    
	 	    
		    $clientCorpID = getOneJobOrder($rest_url, $BhRestToken, $jobOrderId);
		    
		    $x = 0;

		    // if (($clientCorpID == 26624) ||  ($clientCorpID == 26583) ||  ($clientCorpID == 15502)) {
		    $clientCorpIDs = array(24864, 26599, 24848, 26576, 26621, 24858, 26624, 24881, 25382, 26583, 25399, 15502);
			if (in_array($clientCorpID, $clientCorpIDs)) {


			    // echo " placement_job_order: " . $placement_job_order['id'] . "<br>";
			    // print_r($placement_job_order);
			    // echo "<br>";


		    	if (($placement_status == 'Approved') && ($placement_job_order == '220728')) { 
			        ++$x;
			        echo  $x.' First Name: '.$candidate_first_name.' Last Name: '. $candidate_last_name.'<br> CientCorpId: '.$clientCorpID.' JobOrder:'.$jobOrderId.' Candidate: '.$candidateId.'<br>';	
			    	getCandidate($rest_url, $BhRestToken, $candidateId);
		    	}
		    } 
		}
	}

	function getOneJobOrder($rest_url, $BhRestToken, $jobOrderId) {
		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL =>   $rest_url.'query/JobOrder?BhRestToken='.$BhRestToken . '&fields=id,clientCorporation&where=id='.$jobOrderId,
		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));
		$response5 = curl_exec($curl);
		curl_close($curl);
		$data = json_decode($response5, true);

		 

		return $data['data'][0]['clientCorporation']['id'] ; 
	}

	function getCandidate($rest_url, $BhRestToken, $candidateId) {
		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL =>   $rest_url.'search/Candidate?BhRestToken='.$BhRestToken .'&fields=dateLastModified,dateAdded,id,ssn,occupation,gender,firstName,lastName,address,phone,mobile,email,customInt10,employeeType,owner,comments,specialties&query='.$candidateId,
		  CURLOPT_RETURNTRANSFER => true,	
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		));
		$response3 = curl_exec($curl);
		curl_close($curl);
		$data = json_decode($response3, true);
		foreach ($data['data'] as $row) {
			    


		$conn = getCon();		

		$first_name = mysqli_real_escape_string($conn, $row['firstName']); 
		$last_name = mysqli_real_escape_string($conn, $row['lastName']); 

		$recruitor_first_name = mysqli_real_escape_string($conn, $row['owner']['firstName']); 
		$recruitor_last_name = mysqli_real_escape_string($conn, $row['owner']['lastName']); 

		$recruitor_first_name = TRIM(strtoupper($recruitor_first_name));
		$recruitor_last_name = TRIM(strtoupper($recruitor_last_name));
		
		// Get Recruiter ID
		//====================
		
        $recruitor_id = '';
		$conn1 = getCon();		

		$query1 = " SELECT UserId as recruitor_id 
						FROM Users
					WHERE UserStatusId =1
					AND UserGroupId = 2 
					AND TRIM(UPPER(FirstName)) like '%{$recruitor_first_name}%' 
					AND TRIM(UPPER(LastName)) like '%{$recruitor_first_name}%' ";

		$result1 =  mysqli_query($conn1, $query1);
		
		while ($row1 = mysqli_fetch_row($result1)) {
   			$recruitor_id = $row['recruitor_id'];
  		}
			

    	mysqli_free_result($result1);
		mysqli_close($conn1);

		// Date Added
		//====================
		
		$timestamp1 = $row['dateAdded'];
		$timestamp = $timestamp1 / 1000; // divide by 1000 to convert from milliseconds to seconds
		$bullhorn_added_date = date('Y-m-d H:i:s', $timestamp);

		// echo 'added_date: '.$bullhorn_added_date.'<br>';

		// Get Last Date Modified
		//====================
		
		$timestamp1 = $row['dateLastModified'];
		$timestamp = $timestamp1 / 1000; // divide by 1000 to convert from milliseconds to seconds
		$bullhorn_change_date = date('Y-m-d H:i:s', $timestamp);

		echo 'change_date: '.$bullhorn_change_date.'<br>';

		// Get Candidate State ID
		//====================
		
        $candiade_state_name = $row['address']['state'];
        $provider_state_id = '';
		$conn2 = getCon();		

		$query2 = " SELECT State as ProviderStateId 
						FROM States
					WHERE StateName =  '{$candiade_state_name}'  
					  ";

		// echo 'state query2: '.$query2.'<br>';			  

		$result2 =  mysqli_query($conn2, $query2);
		
		while ($row2 = mysqli_fetch_row($result2)) {
			// echo 'state row2: '.($row2[0]).'<br>';

   			$provider_state_id = $row2[0];
  		}
			

    	mysqli_free_result($result2);
		mysqli_close($conn2);

		// Get Candidate Type ID
		//====================
		
        $specialty_name = $row['specialties']['data'][0]['name'];
        
        if ($specialty_name) {

	        $provider_type_id = 0;
			$conn2 = getCon();		

			$query2 = " SELECT Id from RegistrantTypes
	   					where  find_in_set('{$specialty_name}',BullhornSpecialty)
						  ";

	 
			$result2 =  mysqli_query($conn2, $query2);

			echo 'provider type query: '.$query2.'<br>';

			while ($row2 = mysqli_fetch_row($result2)) {
				echo 'provider type id: '.print_r($row2[0]).'<br>';

	   			$provider_type_id = $row2[0];
	  		}
				

	    	mysqli_free_result($result2);
			mysqli_close($conn2);
 

        } else {

        	$provider_type_id = 0;

        } 


		//===================
		// Employee Type ID 

		if (($row['employeeType'] != 'W2') && (trim($row['employeeType'] != '')) ) {

			$hr_type_id = '2';

		} else {

			$hr_type_id = '1';

		}



		//===================
		// Ext ID (SSN) 

		$ext_id = preg_replace("/[^0-9]/", "", $row['ssn']);


		//===================
		// Mobile/Home Phone 

		$mobile_phone = str_replace("+1", "", $row['mobile']);
		$home_phone = str_replace("+1", "", $row['phone']);


		//===================
		// Search String 

		$length = 20;
		$randomString = bin2hex(random_bytes($length));

		// Add New Provider
		//=====================

		
		if ($provider_type_id != 0) {   

			$query = "INSERT into Registrants_1  
									( 
	                                SearchId, /* 1*/		
	                                ExtId,	  /* 2*/							
									TypeId,   /* 3*/
									FirstName, /* 4*/
									LastName,  /* 5*/
									StreetAddress1, /* 6*/
									StreetAddress2, /* 7*/
									City, /* 8*/
									State, /* 9*/
									ZipCode, /* 10*/
									MobilePhone, /* 11*/
									HomePhone, /* 12*/
									Email, /* 13*/
									HrId,  /* 14*/
									HrTypeId, /* 15*/
									RecruitorId, /* 16*/
	                                BullhornId, /* 17*/
	                                BullhornChangeDate, /* 18*/
									UserId, /* 19*/
									TransDate  /* 20*/

									)
					 
					SELECT 		    '{$randomString}',  /* 1*/
									'{$ext_id}',  /* 2*/
									'{$provider_type_id}', /* 3*/
									'{$first_name}',  /* 4*/
									'{$last_name}',	  /* 5*/	
									'{$row['address']['address1']}', /* 6*/
									'{$row['address']['address2']}', /* 7*/
									'{$row['address']['city']}',    /* 8*/
									'{$provider_state_id}',   /* 9*/
									'{$row['address']['zip']}',    /* 10*/
									'{$mobile_phone}',           /* 11*/
									'{$home_phone}',            /* 12*/
									'{$row['email']}',            /* 13*/
									'{$row['customInt10']}',     /* 14*/
									'{$hr_type_id}',              /* 15*/
									'{$recruitor_id}',  /* 16*/
									'{$row['id']}', /* 17*/
									'{$bullhorn_change_date}',
									1,
									now()
						where not exists (
								SELECT 1 from Registrants_1
							where BullhornId = '{$row['id']}'	

						) 			

								";
						   
		// echo 'query: '.$query.'<br>';						
	   			 
		$result =  mysqli_query($conn, $query);

		mysqli_close($conn);

		// Update Exising Provider
		//=======================

		if ($row['ssn']) {

			$conn1  = getCon();		

			$query1 = " UPDATE  Registrants_1
					SET
					BullhornId = '{$row['id']}', 
					TypeId = '{$provider_type_id}',
					FirstName = '{$first_name}',
					LastName =  '{$last_name}',
					StreetAddress1 = '{$row['address']['address1']}',
					StreetAddress2 = '{$row['address']['address2']}',
					City = '{$row['address']['city']}',
					State = '{$provider_state_id}', 
					ZipCode = '{$row['address']['zip']}', 
					MobilePhone = '{$row['mobile']}', 
					HomePhone = '{$row['phone']}',
					Email = '{$row['email']}',
					HrId = '{$row['customInt10']}',
					HrTypeId = '{$hr_type_id}',
					RecruitorId = '{$recruitor_id}',
					BullhornId ='{$row['id']}',
					BullhornChangeDate = '{$bullhorn_change_date}',
					UserId = 1,
					TransDate = now()
					WHERE ExtId = '{$row['ssn']}' 
					AND ((BullhornChangeDate is null) || (BullhornChangeDate < '{$bullhorn_change_date}'))

					";

			echo 'update query: '.$query1.'<br>';						

				$result1 =  mysqli_query($conn1, $query1);

		 		mysqli_close($conn1);


			}
		}	

		//=======================

			    echo " specialties: " . print_r($row['specialties']['data']) . "<br>";
			    // echo " employee type name : " . $row['specialties']['data'][0]['name']  . "<br>";
			    
			    echo " last date modified: " . print_r($row['dateLastModified']) . "<br>";

			    // echo " BullhornID: " . $row['id'] . "<br>";
			    echo " ssn: " . $row['ssn'] . "<br>";
			    echo " occupation: " . $row['occupation'] . "<br>";
			    // echo " gender: " . print_r($row['gender']) . "<br>";
			    // echo " firstName: " . $row['firstName'] . "<br>";
			    // echo " lastName: " . $row['lastName'] . "<br>";
			    // echo " phone: " . $row['phone'] . "<br>";
			    // echo " mobile: " . $row['mobile'] . "<br>";
			    // echo " email: " . $row['email'] . "<br>";
			    // echo " HRID: " . $row['customInt10'] . "<br>";
			    // echo " owner: " . print_r($row['owner']) . "<br>";
			    // echo " employeeType: " . $row['employeeType'] . "<br>";
			    // echo " placement status: " . $placement_status . "<br>";
			    
			    echo " specialties: " . print_r($row['specialties']) . "<br>";

			    echo " address: " . print_r($row['address']) . "<br>";
			    echo " address1: " . $row['address']['address1']  . "<br>";
			    echo " address2: " . $row['address']['address2']  . "<br>";
			    echo " city: " . $row['address']['city']  . "<br>";
			    echo " state: " . $provider_state_id  . "<br>";
			    echo " zip: " . $row['address']['zip']  . "<br>";
			     
			    
			    echo " ====================================<br>";

		}
	}
?>


 