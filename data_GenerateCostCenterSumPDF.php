<?php

	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php');


	$InvoiceNumber = $_GET['InvoiceNumber'];
	
	/* Get Company Information
	==============================*/
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
	
 
	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "SELECT 	c.Id as InvoiceNumber,
						DATE_FORMAT( c.InvoiceDate, '%m/%d/%Y' ) as InvoiceDate,
						DATE_FORMAT( c.PeriodStartDate, '%m/%d/%Y' ) as PeriodStartDate,
						DATE_FORMAT( c.PeriodEndDate, '%m/%d/%Y' ) as PeriodEndDate,
						CompanyName, 
						CONCAT( a.StreetAddress1, ' - ', a.StreetAddress2 ) AS CompanyStreetAddress, 
						CONCAT( a.City, ', ', a.State, ' ', a.ZipCode ) AS CompanyCityStateZip,
						a.PhoneNumber as CompanyPhoneNumber, 
						TIN,
                        ClientName,
                        BillingPersonName,
                        CONCAT( b.StreetAddress1, ' ', b.StreetAddress2 ) AS BillingStreetAddress, 
                        CONCAT( b.City, ', ', b.State, ' ', b.ZipCode ) AS BillingCityStateZip
				FROM Company a, Clients b, InvoiceHeader c
                    Where c.Id = '{$InvoiceNumber}'
					AND c.ClientId = b.Id  ";
				
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['InvoiceNumber'] = $row['InvoiceNumber'];
		$GLOBALS['InvoiceDate'] = $row['InvoiceDate'];
		$GLOBALS['PeriodStartDate'] = $row['PeriodStartDate'];
		$GLOBALS['PeriodEndDate'] = $row['PeriodEndDate'];
		$GLOBALS['Company'] = $row['CompanyName'];
		$GLOBALS['CompanyStreetAddress'] = $row['CompanyStreetAddress'];
		$GLOBALS['CompanyCityStateZip'] = $row['CompanyCityStateZip'];
		$GLOBALS['CompanyPhoneNumber'] = $row['CompanyPhoneNumber'];
		$GLOBALS['TIN'] = $row['TIN'];
		$GLOBALS['ClientName'] = $row['ClientName'];
		$GLOBALS['BillingPersonName'] = $row['BillingPersonName'];
		$GLOBALS['BillingStreetAddress'] = $row['BillingStreetAddress'];
		$GLOBALS['BillingCityStateZip'] = $row['BillingCityStateZip'];
		
	}	

	
	class PDF extends FPDF
	{
	//Page header
	function Header()
	{	
		$this->SetLeftMargin(5);
		$this->SetRightMargin(5);

		//Logo
		//$this->Image('logo.jpg',5,5,30);
		//Arial bold 15
		$this->SetFont('Arial','B',10);
		//Move to the right
		//Line break
		$this->Ln(4);
		//$this->Cell(80);
		//Title
		$this->Cell(30,5,'',0,0,'L');
		$this->Cell(30);
		
		$this->Cell(40,5,$GLOBALS['Company'],0,0,'L');
	
		$this->Cell(30);
		$this->Cell(40,5,'Invoice',1,1,'C');	
		
		/*=====================================================*/
		//$this->Ln(5);
		$this->Cell(50);
		$this->SetFont('Arial','B',14);
		$this->Cell(50,5,'Cost Center Summary List',0,0,'L');
		$this->SetFont('Arial','B',10);
		$this->Cell(30);
		$this->Cell(20,5,'Date',1,0,'C');	
		$this->Cell(20,5,'Number',1,1,'C');	
		
		/*=====================================================*/
		
		//$this->Ln(6);
		$this->Cell(70);
		$this->Cell(30,5,'',0,0,'L');
		$this->Cell(30);
		$this->Cell(20,5,$GLOBALS['InvoiceDate'],1,0,'C');	
		$this->Cell(20,5,$GLOBALS['InvoiceNumber'],1,1,'C');	
		
		/*=====================================================*/

		//$this->Ln(7);
		$this->Cell(70);
		$this->Cell(30,5,'',0,0,'L');
		$this->Cell(30);
		$this->Cell(40,5,'Period',1,1,'C');	
		
		//$this->Ln(8);
		$this->Cell(70);
		$this->Cell(30,5,'',0,0,'L');
		$this->Cell(30);
		$this->Cell(20,5,$GLOBALS['PeriodStartDate'],1,0,'C');	
		$this->Cell(20,5,$GLOBALS['PeriodEndDate'],1,1,'C');	

		/*=====================================================*/
		
		//Line break
		$this->Ln(10);
		$this->Cell(30);
		$this->Cell(50,5,$GLOBALS['ClientName'],0,1,'L');
		$this->Cell(30);
		$this->Cell(50,5,$GLOBALS['BillingPersonName'],0,1,'L');
		$this->Cell(30);
		$this->Cell(50,5,$GLOBALS['BillingStreetAddress'],0,1,'L');
		$this->Cell(30);
		$this->Cell(50,5,$GLOBALS['BillingCityStateZip'],0,1,'L');

		/*=====================================================*/

		//Line break
		$this->Ln(15);
	/*
		//Set Table Header
		$this->SetFont('Arial','B',10);
		$this->Cell(10,4,'LN #',1,0,'C');
		$this->Cell(20,4,'Date',1,0,'C');
		$this->Cell(15,4,'Shift',1,0,'C');
		$this->Cell(15,4,'WD/WE',1,0,'C');
		$this->Cell(20,4,'Area',1,0,'C');
		$this->Cell(40,4,'Service Provider',1,0,'C');
		$this->Cell(18,4,'License #',1,0,'C');
		$this->Cell(12,4,'Status',1,0,'C');
		$this->Cell(12,4,'Rate',1,0,'C');
		$this->Cell(17,4,'Units',1,0,'C');
		$this->Cell(18,4,'Amount',1,1,'C');

	*/	
		$this->SetFont('Arial','B',10);
		$this->Cell(30,4,'Nurse Type',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'Area Worked',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'Hours Worked',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'# of RNs Worked',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(20,4,'Amount',0,1,'C');
		
		$this->Cell(30,4,'',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(30,4,'',0,0,'C');
		$this->Cell(10,4,'',0,0,'C');
		$this->Cell(20,4,'',0,1,'C');
		
		//Line break
		//$this->Ln(20);
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Arial','I',8);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
	}




	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	//$pdf->SetFont('Times','',12);
	$pdf->SetFont('Arial','',10);

	//++++++++++++++++++++++++++++++++++++++++++++++
    
	 
	$query1 = "SELECT 		UnitName,
			ServiceTypeDesc,
			sum(a.Units) as TotUnits,
			sum(a.Amount) as TotAmtBilled,
			count(*) as TotalEmpl 
	FROM 	InvoiceDetails a, 
			Registrants b, 
			WeeklyServices c, 
			ClientUnits d, 
			ServiceTypes f
	WHERE	 a.InvoiceNumber = '{$InvoiceNumber}'
	AND 	a.ScheduleId = c.Id	
	AND c.RegistrantId = b.Id
	AND c.ClientUnitId = d.Id
	AND c.ServiceTypeId = f.Id
	GROUP BY ServiceTypeDesc,
	         UnitName
	ORDER BY ServiceTypeDesc, (UnitName+0) ASC"; 
    
	
	$result1 = $connection->query($query1);
	 
	$j = $result1->numRows();
	 
	//++++++++++++++++++++++++++++++++++++++++++++++
	
	
	$i = 0; 
	
	while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {

		if (!$saved_service_desc) {
			
			$saved_service_desc = $row['ServiceTypeDesc'];  
		
		}
		
		$i++;
		
		if ($saved_service_desc != $row['ServiceTypeDesc']) { 
			
			$saved_service_desc = $row['ServiceTypeDesc'];
			
			$pdf->Cell(30,4,'',3,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(30,4,'',0,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,'=========',0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,'==========',0,0,'R');
			 
			$pdf->Cell(15,4,'',0,0,'C');
			$pdf->Cell(20,4,'==========',0,1,'R');
			 
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(80,4,'Nurse Cost Center Total:',0,0,'C');
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,(sprintf("%1\$.2f",$tot_units)),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,(sprintf("%1\$.2f",$tot_empl)),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,(sprintf("%1\$.2f",$tot_amount)),0,1,'R');
			
			$pdf->Ln(5);

			$tot_units = 0;
			$tot_empl = 0;
			$tot_amount = 0;
			
			
		}
			
		$pdf->Cell(30,4,$row['ServiceTypeDesc'],3,0,'C');
		$pdf->Cell(10,4,'',0,0,'C');
		$pdf->Cell(30,4,$row['UnitName'],0,0,'C');
		$pdf->Cell(10,4,'',0,0,'C');
		$pdf->Cell(25,4,number_format($row['TotUnits'],2),0,0,'R');
		$pdf->Cell(10,4,'',0,0,'C');
		$pdf->Cell(27,4,number_format($row['TotalEmpl'],2),0,0,'R');
		$pdf->Cell(10,4,'',0,0,'C');
		$pdf->Cell(25,4,number_format($row['TotAmtBilled'],2),0,1,'R');
		
		$pdf->Ln(1);

		$tot_units = $tot_units + $row['TotUnits'];
		$tot_empl = $tot_empl + $row['TotalEmpl'];
		$tot_amount = $tot_amount + $row['TotAmtBilled'];

		$grd_tot_units = $grd_tot_units + $row['TotUnits'];
		$grd_tot_empl = $grd_tot_empl + $row['TotalEmpl'];
		$grd_tot_amount = $grd_tot_amount + $row['TotAmtBilled'];


	} 

			$tot_units = $tot_units + $row['TotUnits'];
			$tot_empl = $tot_empl + $row['TotalEmpl'];
			$tot_amount = $tot_amount + $row['TotAmtBilled'];

			$grd_tot_units = $grd_tot_units + $row['TotUnits'];
			$grd_tot_empl = $grd_tot_empl + $row['TotalEmpl'];
			$grd_tot_amount = $grd_tot_amount + $row['TotAmtBilled'];

	
			$pdf->Cell(30,4,'',3,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(30,4,'',0,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,'=========',0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,'==========',0,0,'R');
			 
			$pdf->Cell(15,4,'',0,0,'C');
			$pdf->Cell(20,4,'==========',0,1,'R');
			 
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(80,4,'Nurse Cost Center Total:',0,0,'C');
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,number_format($tot_units,2),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,number_format($tot_empl,2),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,number_format($tot_amount,2),0,1,'R');
			
			$pdf->Ln(2);

			$pdf->Cell(30,4,'',3,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(30,4,'',0,0,'C');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,'=========',0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,'==========',0,0,'R');
			 
			$pdf->Cell(15,4,'',0,0,'C');
			$pdf->Cell(20,4,'==========',0,1,'R');
			 
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(80,4,'F I N A L  T O T A L:',0,0,'C');
			//$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,(sprintf("%1\$.2f",$grd_tot_units)),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(27,4,(sprintf("%1\$.2f",$grd_tot_empl)),0,0,'R');
			$pdf->Cell(10,4,'',0,0,'C');
			$pdf->Cell(25,4,(sprintf("%1\$.2f",$grd_tot_amount)),0,1,'R');
			
		/*
		// Extra Row 1 //
		$pdf->Cell(10,4,'',1,0,'C');
		$pdf->Cell(20,4,'',1,0,'C');
		$pdf->Cell(15,4,'',1,0,'C');
		$pdf->Cell(15,4,'',1,0,'C');
		$pdf->Cell(20,4,'',1,0,'C');
		$pdf->Cell(40,4,'',1,0,'C');
		$pdf->Cell(18,4,'',1,0,'C');
		$pdf->Cell(12,4,'',1,0,'C');
		$pdf->Cell(12,4,'',1,0,'R');
		$pdf->Cell(17,4,'',1,0,'R');
		$pdf->Cell(18,4,'',1,1,'R');	
		
		// Extra Row 2 //
		$pdf->Cell(10,4,'',1,0,'C');
		$pdf->Cell(20,4,'',1,0,'C');
		$pdf->Cell(15,4,'',1,0,'C');
		$pdf->Cell(15,4,'',1,0,'C');
		$pdf->Cell(20,4,'',1,0,'C');
		$pdf->Cell(40,4,'*** Net 30 Days ***',1,0,'C');
		$pdf->Cell(18,4,'Total:',1,0,'C');
		$pdf->Cell(12,4,'',1,0,'C');
		$pdf->Cell(12,4,'',1,0,'R');
		$pdf->Cell(17,4,$tot_units,1,0,'R');
		$pdf->Cell(18,4,$tot_amount,1,1,'R');	
		*/
		
	$pdf->Output();
	
	$connection->disconnect();
 	
?>
