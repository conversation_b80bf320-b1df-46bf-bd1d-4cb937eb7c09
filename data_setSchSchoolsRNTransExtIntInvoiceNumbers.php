<?php 


	require "ewDataHandler.php";   
	  
	$rcr_transaction = new dataHandler();  

	$InvoiceNumber = $_POST['InvoiceNumber'];
	$InvoiceNumberExt = $_POST['InvoiceNumberExt'];
	$ExtInvData = $_POST['ExtInvData'];
	$IntInvData = $_POST['IntInvData'];

	$UserId = $_POST['UserId'];


	$ExtInvData = $_POST['ExtInvData'];
	$ExtInvData=json_decode($ExtInvData,true);
	$InclExtInvoiceTrans =  implode(",",$ExtInvData);

	$IntInvData = $_POST['IntInvData'];
	$IntInvData=json_decode($IntInvData,true);
	$InclIntInvoiceTrans =  implode(",",$IntInvData);

	// Mark Trasactions with External (DOE) Inv #
	//===========================================================
	$result = $rcr_transaction->setSchSchoolsRNTransExtInvoiceNumber($InvoiceNumberExt,
																	 $InclExtInvoiceTrans,
																	 $UserId
																	 ) ;

	// Mark Trasactions with Internal (GOTHAM) Inv #
	//===========================================================
	$result1 = $rcr_transaction->setSchSchoolsRNTransIntInvoiceNumber( $InvoiceNumber,
																	   $InclIntInvoiceTrans,
																	   $UserId
																	  ) ;



	$rcr_transaction->disconnectDB (); 


	echo $result;



?>
