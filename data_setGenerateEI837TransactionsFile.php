  
<?php
/*    
error_reporting(E_ALL);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
*/

	require_once("db_login.php");

	//Portal Out Input File  
	//==========================================================




    $user_id = $_POST['UserId'];
    if (!$user_id) {
        $user_id = '1';
    }   

    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);
    $InclIds =  implode(",",$Data);

   	


	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	  // Check connection
	if (mysqli_connect_errno())
	    {
	    //echo "Could not query the database: " . mysqli_connect_error();
	    }


	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "SELECT 	CompanyName, 
						StreetAddress1,
						City,
						State,
						ZipCode,
						ContactPerson,
						PhoneNumber,
						Fax,
						Email,
						MunicipalityName,
						MunicipalityCode,
						ETIN,
						Agency_NPI,
						TIN,
						LastInvoiceNumber,
						LastReferenceNumber
				FROM Company ";
	
	  $result =  mysqli_query($conn, $query) or die
	  ("Error in Selecting " . mysqli_error($conn));


	  while ($row = $result->fetch_assoc()) {
			$GLOBALS['CompanyName'] = $row['CompanyName'];
			$GLOBALS['StreetAddress1'] = $row['StreetAddress1'];
			$GLOBALS['City'] = $row['City'];
			$GLOBALS['State'] = $row['State'];			
			$GLOBALS['ZipCode'] = $row['ZipCode'];

			$GLOBALS['ContactPerson'] = $row['ContactPerson'];
			$GLOBALS['PhoneNumber'] = $row['PhoneNumber'];
			$GLOBALS['Fax'] = $row['Fax'];
			$GLOBALS['Email'] = $row['Email'];
			$GLOBALS['MunicipalityName'] = $row['MunicipalityName'];
			$GLOBALS['MunicipalityCode'] = $row['MunicipalityCode'];
			$GLOBALS['ETIN'] = $row['ETIN'];
			$GLOBALS['Agency_NPI'] = $row['Agency_NPI'];
			$GLOBALS['TIN'] = $row['TIN'];
			$GLOBALS['LastInvoiceNumber'] = $row['LastInvoiceNumber'];
			$GLOBALS['LastReferenceNumber'] = $row['LastReferenceNumber'];

		}

	  mysqli_free_result($result);
	  mysqli_close($conn);

	$seg_num = 0;  


	$InvNumber = ++$GLOBALS['LastInvoiceNumber']; 


  
	$out_File = "../uploads/837_Invoice_.".$InvNumber."_.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");


	// ISA LOOP
	//====================================

	$InterProviderId = $GLOBALS['ETIN']; // ISA06
	$InterChangeDate6 = date('ymd'); 
	$InterChangeTime = date('Hi'); 
	$TenSpaces = str_pad('*',11);
	
	$ISA_Loop = 'ISA*00'.$TenSpaces.'*00'.$TenSpaces.'*ZZ*'.str_pad($InterProviderId,15).'*ZZ*'.str_pad('NYEIS',15);
	//$ISA_Loop = $ISA_Loop.'*'.$InterChangeDate6.'*'.$InterChangeTime.'*^*00501*'.str_pad($InvNumber,9,"0",STR_PAD_LEFT).'*0*T*:~';
	$ISA_Loop = $ISA_Loop.'*'.$InterChangeDate6.'*'.$InterChangeTime.'*^*00501*'.str_pad($InvNumber,9,"0",STR_PAD_LEFT).'*0*P*:~';

	//echo ' '.$ISA_Loop.'</br>';

	fwrite($fh, $ISA_Loop);	
	$seg_num++; 

	// GS LOOP
	//====================================

	$InterChangeDate8 = date('Ymd'); 

	$GS_Loop = 'GS*HC*'.$InterProviderId.'*NYEIS*'.$InterChangeDate8.'*'.$InterChangeTime.'*1*X*005010X222A1~';


	//echo ' '.$GS_Loop.'</br>';

	fwrite($fh, $GS_Loop);	
	$seg_num++; 

	// ST LOOP
	//====================================

 

	$ST_Loop = 'ST*837*'.$InvNumber.'*005010X222A1~';

	//echo ' '.$ST_Loop.'</br>';

	fwrite($fh, $ST_Loop);	
	$seg_num++; 

	// BHT LOOP 
	//====================================

	$InterChangeDate12 = date('YmdHisB'); 
	$InterChangeTime6 = date('His'); 

	$BHT_Loop = 'BHT*0019*00*'.$InterChangeDate12.'*'.$InterChangeDate8.'*'.$InterChangeTime6.'*CH~';
	//echo ' '.$BHT_Loop.'</br>';

	fwrite($fh, $BHT_Loop);	
	$seg_num++; 

	// NM1 LOOP (Loop 1000A)
	//====================================

	$Agency_Name = $GLOBALS['CompanyName'];

	$Loop_1000A_NM1 = 'NM1*41*2*'.$Agency_Name.'*****46*'.$InterProviderId.'~';
	//echo '====>Loop 1000A</br>';
	//echo ' '.$Loop_1000A_NM1.'</br>';

	fwrite($fh, $Loop_1000A_NM1);	
	$seg_num++; 

	// PER LOOP (Loop 1000A)
	//====================================
 
	$Agency_Rep_Name = $GLOBALS['ContactPerson'];
	$Agency_Phone_Number = $GLOBALS['PhoneNumber'];
	$Agency_Fax_Number = $GLOBALS['Fax'];
	$Agency_Email = $GLOBALS['Email'];

	$Agency_Phone_Number = preg_replace('/[^0-9]/', '', $Agency_Phone_Number);
	$Agency_Fax_Number = preg_replace('/[^0-9]/', '', $Agency_Fax_Number);

	$Loop_1000A_PER = 'PER*IC*'.$Agency_Rep_Name.'*TE*'.$Agency_Phone_Number.'*EM*'.$Agency_Email.'*FX*'.$Agency_Fax_Number.'~';
	//echo ' '.$Loop_1000A_PER.'</br>';

	fwrite($fh, $Loop_1000A_PER);	
	$seg_num++; 

	// NM1 LOOP (Loop 1000B)
	//====================================

	$County_Name = $GLOBALS['MunicipalityName'];

	$Loop_1000B_NM1 = 'NM1*40*2*'.$County_Name.'- Early Intervention*****46*70~';
	//echo '====>Loop 1000B</br>';

	//echo ' '.$Loop_1000B_NM1.'</br>';

	fwrite($fh, $Loop_1000B_NM1);	
	$seg_num++; 


	// HL LOOP (Loop 2000A)
	//====================================

	$Trans_Number++; 

	$Loop_2000A_HL = 'HL*'.$Trans_Number.'**20*1~';
	//echo '====>Loop 2000A</br>';

	//echo ' '.$Loop_2000A_HL.'</br>';
 
	fwrite($fh, $Loop_2000A_HL);	
	$seg_num++; 


	// NM1 LOOP Loop 2010A)
	//====================================

	$Agency_NPI = $GLOBALS['Agency_NPI'];
	$Loop_2010A_NM1 = 'NM1*85*2*'.$Agency_Name.'*****XX*'.$Agency_NPI.'~';

	//echo '====>Loop 2010A</br>';
	//echo ' '.$Loop_2010A_NM1.'</br>';

	fwrite($fh, $Loop_2010A_NM1);	
	$seg_num++; 

	// N3 LOOP Loop 2010A)
	//====================================

	$Provider_Address1 = $GLOBALS['StreetAddress1'];
	$Loop_2010A_N3 = 'N3*'.$Provider_Address1.'~';

	//echo ' '.$Loop_2010A_N3.'</br>';

	fwrite($fh, $Loop_2010A_N3);	
	$seg_num++; 


	// N4 LOOP Loop 2010A)
	//====================================

	$Provider_City = $GLOBALS['City'];
	$Provider_State = $GLOBALS['State'];
	$Provider_Zip = $GLOBALS['ZipCode'];
	
	$Loop_2010A_N4 = 'N4*'.$Provider_City.'*'.$Provider_State.'*'.$Provider_Zip.'~';

	//echo ' '.$Loop_2010A_N4.'</br>';

	fwrite($fh, $Loop_2010A_N4);	
	$seg_num++; 

	// REF LOOP Loop 2010A)
	//====================================

	$Provider_Tax_Id = $GLOBALS['TIN'];
	$Loop_2010A_REF = 'REF*EI*'.$Provider_Tax_Id.'~';

	//echo ' '.$Loop_2010A_REF.'</br>';

	fwrite($fh, $Loop_2010A_REF);	
	$seg_num++; 

	// Read Billing Transactions
	// ============================

	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	  // Check connection
	if (mysqli_connect_errno())
	    {
	    //echo "Could not query the database: " . mysqli_connect_error();
	    }


	//==================================
	// Get Company Name/User's Email
	//==================================
	
	$query = "  SELECT 	a.RefNumberNYEIS, 
						a.ServiceAuthNumber,
			            b.ChildFirstName,
			            b.ChildLastName,
			            /*b.AddressStreet as ChildAddressStreet, */
			            SUBSTRING(b.AddressStreet, 1, 40) AS ChildAddressStreet,
			            b.City as ChildCity,
			            b.State as ChildState,
			            b.ZipCode  as ChildZipCode,
			            DATE_FORMAT( b.DOB, '%Y%m%d' ) AS ChildDOB,
			            substr(b.Gender,1,1) as ChildGender,
			            a.Id as ClaimId,
			            case a.SessionTypeId
                          when '5' then 'CV2'
                          else 'CV1'
                        end as SessionTypeDesc,   
                        FORMAT((a.SessionLength * 60), 0) AS ClaimAmount, 
						
					
						(select group_concat( ICDCode SEPARATOR ', ' ) from EIChildrenDiagnoses c 
			                     where a.RefNumberNYEIS = c.RefNumberNYEIS
			                     ORDER BY c.Id
			                   
			            ) as ChildDiagCodesList,
			        /*    
						(select c.ICDCode from EIChildrenDiagnoses c 
			                     where a.RefNumberNYEIS = c.RefNumberNYEIS
			                     ORDER BY c.DiagnosisDate DESC
			                LIMIT 1  
			            ) as ChildDiagCode,
			        */    
			            d.FirstName as ProviderFirstName,
			            d.LastName as ProviderLastName,
			            d.NPINumber as ProviderNPINumber,
                        e.BillRate,
                        DATE_FORMAT( a.SessionDate, '%Y%m%d' ) AS SessionDate,
                        DATE_FORMAT( a.SessionStartTime, '%H%i' ) AS SessionStartTime,
                        DATE_FORMAT( a.SessionEndTime, '%H%i' ) AS SessionEndTime ,
                        f.CPTCode1 as CPTCode,
                        f.CPTCode2 as CPTCode2,
                        e.ServiceAuthLocation 


            from    EIChildrenSessions a,
                    EIChildrenProfiles b,
                    EIChildrenServiceProviders d,
                    EIChildrenServiceAuthorizations e,
                    EIChildrenSessionNotes f
                    
            where a.InvoiceNumber = ''
            AND   a.RefNumberNYEIS = b.RefNumberNYEIS 
			AND   a.RenderingProviderNPINumber = d.NPINumber  
            AND   a.ServiceAuthNumber = e.ServiceAuthNumber 
            AND   a.StatusId = '1' 
            AND   a.SessionTypeId in (1,2,5)
            AND   a.Id = f.SessionId 
            AND   FIND_IN_SET(a.Id, '{$InclIds}') 
         /*   AND   b.RefNumberNYEIS = 598618  */
            
 ";
	
	  $result =  mysqli_query($conn, $query) or die
	  ("Error in Selecting " . mysqli_error($conn));


	  while ($row = $result->fetch_assoc()) {
			$GLOBALS['RefNumberNYEIS'] = $row['RefNumberNYEIS'];
			$GLOBALS['ServiceAuthNumber'] = $row['ServiceAuthNumber'];
			$GLOBALS['ChildFirstName'] = $row['ChildFirstName'];
			$GLOBALS['ChildLastName'] = $row['ChildLastName'];
			$GLOBALS['ChildAddressStreet'] = $row['ChildAddressStreet'];
			$GLOBALS['ChildCity'] = $row['ChildCity'];
			$GLOBALS['ChildState'] = $row['ChildState'];
			$GLOBALS['ChildZipCode'] = $row['ChildZipCode'];
			$GLOBALS['ChildDOB'] = $row['ChildDOB'];
			$GLOBALS['ChildGender'] = $row['ChildGender'];
			$GLOBALS['ClaimId'] = $row['ClaimId'];
			$GLOBALS['ClaimAmount'] = $row['ClaimAmount'];
			
			$GLOBALS['ChildDiagCodesList'] = $row['ChildDiagCodesList'];

			$GLOBALS['ServiceAuthLocation'] = $row['ServiceAuthLocation'];


			// ICD10 Codes 
			//===================== 

			$GLOBALS['ChildDiagCode_1'] = '';
			$GLOBALS['ChildDiagCode_2'] = '';
			$GLOBALS['ChildDiagCode_3'] = '';
			$GLOBALS['ChildDiagCode_4'] = '';



 			$icd10_codes_arr = explode(",",($row['ChildDiagCodesList'])); 
			
			if ($icd10_codes_arr[0]) {

				$GLOBALS['ChildDiagCode_1'] = preg_replace('/[^A-Za-z0-9\-]/', '', $icd10_codes_arr[0]);

			}   

			if ($icd10_codes_arr[1]) {

				$GLOBALS['ChildDiagCode_2'] = preg_replace('/[^A-Za-z0-9\-]/', '', $icd10_codes_arr[1]);

			}   

			if ($icd10_codes_arr[2]) {

				$GLOBALS['ChildDiagCode_3'] = preg_replace('/[^A-Za-z0-9\-]/', '', $icd10_codes_arr[2]);

			}   


			if ($icd10_codes_arr[3]) {

				$GLOBALS['ChildDiagCode_4'] = preg_replace('/[^A-Za-z0-9\-]/', '', $icd10_codes_arr[3]);

			}   
			//====================
		/*	
			$GLOBALS['ChildDiagCode'] = $row['ChildDiagCode'];
			$GLOBALS['ChildDiagCode'] = preg_replace('/[^A-Za-z0-9\-]/', '', $GLOBALS['ChildDiagCode']);
		*/	
			$GLOBALS['SessionTypeDesc'] = $row['SessionTypeDesc'];


			$GLOBALS['ProviderFirstName'] = $row['ProviderFirstName'];
			$GLOBALS['ProviderLastName'] = $row['ProviderLastName'];
			$GLOBALS['ProviderNPINumber'] = $row['ProviderNPINumber'];
			$GLOBALS['BillRate'] = $row['BillRate'];

			$GLOBALS['SessionDate'] = $row['SessionDate'];
			$GLOBALS['SessionStartTime'] = $row['SessionStartTime'];
			$GLOBALS['SessionEndTime'] = $row['SessionEndTime'];

			$GLOBALS['CPTCode'] = $row['CPTCode'];
			$GLOBALS['CPTCode2'] = $row['CPTCode2'];
 

			$Service_Line_Number = 0;

			//=============================

			// HL LOOP (Loop 2000B)
			//====================================

			//$Trans_Number_Prev = $Trans_Number;
			$Trans_Number_Prev = '1';

			$Trans_Number++; 

			$Loop_2000B_HL = 'HL*'.$Trans_Number.'*'.$Trans_Number_Prev.'*22*0~';
			//echo '====>Loop 2000B</br>';

			//echo ' '.$Loop_2000B_HL.'</br>';

			fwrite($fh, $Loop_2000B_HL);	
			$seg_num++; 

			// SBR LOOP (Loop 2000B)
			//====================================

			$Loop_2000B_SBR = 'SBR*P*18*******OF~';

			//echo ' '.$Loop_2000B_SBR.'</br>';

			fwrite($fh, $Loop_2000B_SBR);	
			$seg_num++; 

			// NM1 LOOP Loop 2010BA)
			//====================================
		 
			$Child_Ref_Number = $GLOBALS['RefNumberNYEIS'];
			$Child_First_Name = $GLOBALS['ChildFirstName'];
			$Child_Last_Name = $GLOBALS['ChildLastName'];

			$Loop_2010BA_NM1 = 'NM1*IL*1*'.$Child_Last_Name.'*'.$Child_First_Name.'****MI*'.$Child_Ref_Number.'~';
		 
			//echo '====>Loop 2010BA</br>';
			//echo ' '.$Loop_2010BA_NM1.'</br>';

			fwrite($fh, $Loop_2010BA_NM1);	
			$seg_num++; 
		 
			// N3 LOOP Loop 2010BA)
			//====================================

			$Child_Address1 = $GLOBALS['ChildAddressStreet'];
			$Loop_2010BA_N3 = 'N3*'.$Child_Address1.'~';

			//echo ' '.$Loop_2010BA_N3.'</br>';

			fwrite($fh, $Loop_2010BA_N3);	
			$seg_num++; 

			// N4 LOOP (Loop 2010BA)
			//====================================

			$Child_City = $GLOBALS['ChildCity'];
			$Child_State = $GLOBALS['ChildState'];
			$Child_Zip = $GLOBALS['ChildZipCode'];
			
			$Loop_2010BA_N4 = 'N4*'.$Child_City.'*'.$Child_State.'*'.$Child_Zip.'~';

			//echo ' '.$Loop_2010BA_N4.'</br>';

			fwrite($fh, $Loop_2010BA_N4);	
			$seg_num++; 

			// DMG LOOP (Loop 2010BA)
			//====================================

			$Child_DOB = $GLOBALS['ChildDOB'];
			$Child_Gender = $GLOBALS['ChildGender'];
			
			$Loop_2010BA_DMG = 'DMG*D8*'.$Child_DOB.'*'.$Child_Gender.'~';

			//echo ' '.$Loop_2010BA_DMG.'</br>';

			fwrite($fh, $Loop_2010BA_DMG);	
			$seg_num++; 


			// NM1 LOOP Loop (2010BB)
			//====================================

			$Loop_2010BB_NM1 = 'NM1*PR*2*DOHMH*****PI*70~';

			//echo '====>Loop 2010BB</br>';
			//echo ' '.$Loop_2010BB_NM1.'</br>';

			fwrite($fh, $Loop_2010BB_NM1);	
			$seg_num++; 

			// CLM LOOP Loop (2300)
			//====================================

			$Claim_Id = $GLOBALS['ClaimId']; // ServiceId 
			$Bill_Rate = (int)$GLOBALS['BillRate'];

			if ($GLOBALS['ServiceAuthLocation'] == "Child's Home") {

				$facility_code = '12'; 
			} else {

				$facility_code = '11'; 

			}

			

			$Loop_2300_CLM = 'CLM*'.$Claim_Id.'*'.$Bill_Rate.'***'.$facility_code.':B:1*Y*C*N*Y*P~';

			//echo '====>Loop 2300</br>';
			//echo ' '.$Loop_2300_CLM.'</br>';

			fwrite($fh, $Loop_2300_CLM);	
			$seg_num++; 


			// REF LOOP Loop (2300) - Authorization
			//====================================

			$Child_Service_Auth = $GLOBALS['ServiceAuthNumber'];  
			

			$Loop_2300_SERV_AUTH = 'REF*G1*'.$Child_Service_Auth.'~';

			//echo ' '.$Loop_2300_SERV_AUTH.'</br>';

			fwrite($fh, $Loop_2300_SERV_AUTH);	
			$seg_num++; 
		 

			// REF LOOP Loop (2300) - Authorization
			//====================================

			$Child_Ref_No = $GLOBALS['RefNumberNYEIS'];  
			

			$Loop_2300__CHILD_REF_NO = 'REF*EA*'.$Child_Ref_No.'~';

			//echo ' '.$Loop_2300__CHILD_REF_NO.'</br>';

			fwrite($fh, $Loop_2300__CHILD_REF_NO);	
			$seg_num++; 

			// NTE LOOP Loop (2300)
			//====================================

			$Session_Type_Desc = $GLOBALS['SessionTypeDesc'];  	
			$Session_Start_Time = $GLOBALS['SessionStartTime'];  
			$Session_End_Time = $GLOBALS['SessionEndTime'];  
			

			$Loop_2300_NTE = 'NTE*ADD*'.$Session_Type_Desc.'-'.$Session_Start_Time.'-'.$Session_End_Time.'~';

			//echo ' '.$Loop_2300_NTE.'</br>';

			fwrite($fh, $Loop_2300_NTE);	
			$seg_num++; 




			// HI LOOP Loop (2300)
			//====================================

			// Child_Diag_Code_1
			//=======================
			$Child_Diag_Code_1 = $GLOBALS['ChildDiagCode_1'];  
			

			//$Loop_2300_HI = 'HI*ABK:'.$Child_Diag_Code.'~';
			$Loop_2300_HI = 'HI*ABK:'.$Child_Diag_Code_1;


			// Child_Diag_Code_2
			//=======================

			if ($GLOBALS['ChildDiagCode_2']) {

				$Child_Diag_Code_2 = $GLOBALS['ChildDiagCode_2'];  
 				$Loop_2300_HI = $Loop_2300_HI.'*ABF:'.$Child_Diag_Code_2;

			}


			// Child_Diag_Code_3
			//=======================

			if ($GLOBALS['ChildDiagCode_3']) {

				$Child_Diag_Code_3 = $GLOBALS['ChildDiagCode_3'];  
 				$Loop_2300_HI = $Loop_2300_HI.'*ABF:'.$Child_Diag_Code_3;

			}
	

			// Child_Diag_Code_4
			//=======================

			if ($GLOBALS['ChildDiagCode_4']) {

				$Child_Diag_Code_4 = $GLOBALS['ChildDiagCode_4'];  
 				$Loop_2300_HI = $Loop_2300_HI.'*ABF:'.$Child_Diag_Code_4;

			}
			
			
			$Loop_2300_HI = $Loop_2300_HI.'~';
			fwrite($fh, $Loop_2300_HI);	
			$seg_num++; 


			// NM1 LOOP Loop (2310A) - Rendereing Agency
			//====================================

			$Loop_2310A_NM1 = 'NM1*DN*1*'.$Agency_Name.'*****XX*'.$Agency_NPI.'~';

			//echo '====>Loop 2310A</br>';
			//echo ' '.$Loop_2310A_NM1.'</br>';

			fwrite($fh, $Loop_2310A_NM1);	
			$seg_num++; 

			// NM1 LOOP Loop (2310B) - Rendering Provider
			//===============================================

			$Provider_Last_Name = $GLOBALS['ProviderFirstName'];
			$Provider_First_Name = $GLOBALS['ProviderLastName'];
			$Provider_Id = $GLOBALS['ProviderNPINumber'];


			$Loop_2310B_NM1 = 'NM1*82*1*'.$Provider_Last_Name.'*'.$Provider_First_Name.'****XX*'.$Provider_Id.'~';

			//echo '====>Loop 2310B</br>';
			//echo ' '.$Loop_2310B_NM1.'</br>';

			fwrite($fh, $Loop_2310B_NM1);	
			$seg_num++; 



		 
			// LX LOOP Loop (2400)
			//====================================

			$Service_Line_Number++;  
			

			$Loop_2400_LX = 'LX*'.$Service_Line_Number.'~';

			//echo '====>Loop 2400</br>';
			//echo ' '.$Loop_2400_LX.'</br>';

			fwrite($fh, $Loop_2400_LX);	
			$seg_num++; 

			// SV1 LOOP Loop (2400)
			//====================================

		// CPT Code 1 - Start	
		//==========================	
			//$Loop_2400_SV1 = 'SV1*HC:98966*36*UN*1***1~';
			//====================================

			$CPT_Code = $GLOBALS['CPTCode'];  
			$Bill_Rate = (int)$GLOBALS['BillRate'];

			$Loop_2400_SV1 = 'SV1*HC:'.$CPT_Code.'*'.$Bill_Rate.'*UN*1***1~';
			
			//echo ' '.$Loop_2400_SV1.'</br>';

			fwrite($fh, $Loop_2400_SV1);	
			$seg_num++; 

			//$Loop_2400_SV1 = 'SV1*HC:98966*36*UN*1***1~';
			//====================================

			$Session_Date = $GLOBALS['SessionDate'];  


			$Loop_2400_DPT = 'DTP*472*D8*'.$Session_Date.'~';
			
			//echo ' '.$Loop_2400_DPT.'</br>';

			fwrite($fh, $Loop_2400_DPT);	
			$seg_num++; 

			//$Loop_2400_REF = 'SV1*HC:98966*36*UN*1***1~';
			//====================================

			//$Line_Ref_Num = '2312334';  
			$Line_Ref_Num = ++$GLOBALS['LastReferenceNumber']; 


			$Loop_2400_REF = 'REF*6R*'.$Line_Ref_Num.'~';
			
			//echo ' '.$Loop_2400_REF.'</br>';

		// CPT Code 1 - End	
		//==========================	


		// CPT Code 2 - Start	
		//==========================	

		if ($GLOBALS['CPTCode2'] != '')	 { 
			
			//$Loop_2400_SV1 = 'SV1*HC:98966*36*UN*1***1~';
			//====================================


			$Service_Line_Number++;  
			

			$Loop_2400_LX = 'LX*'.$Service_Line_Number.'~';

			//echo '====>Loop 2400</br>';
			//echo ' '.$Loop_2400_LX.'</br>';

			fwrite($fh, $Loop_2400_LX);	
			$seg_num++; 


			$CPT_Code2 = $GLOBALS['CPTCode2'];  
			$Bill_Rate2 = 0;

			$Loop_2400_SV1 = 'SV1*HC:'.$CPT_Code2.'*'.$Bill_Rate2.'*UN*1***1~';
			
			//echo ' '.$Loop_2400_SV1.'</br>';

			fwrite($fh, $Loop_2400_SV1);	
			$seg_num++; 

			//$Loop_2400_SV1 = 'SV1*HC:98966*36*UN*1***1~';
			//====================================

			$Session_Date = $GLOBALS['SessionDate'];  


			$Loop_2400_DPT = 'DTP*472*D8*'.$Session_Date.'~';
			
			//echo ' '.$Loop_2400_DPT.'</br>';

			fwrite($fh, $Loop_2400_DPT);	
			$seg_num++; 

			//$Loop_2400_REF = 'SV1*HC:98966*36*UN*1***1~';
			//====================================

			//$Line_Ref_Num = '2312334';  
			$Line_Ref_Num = ++$GLOBALS['LastReferenceNumber']; 


			$Loop_2400_REF = 'REF*6R*'.$Line_Ref_Num.'~';
			
			//echo ' '.$Loop_2400_REF.'</br>';
		}	
		// CPT Code 2 - End	
		//==========================	



			fwrite($fh, $Loop_2400_REF);	
			$seg_num++; 



		}


		//SE Segment';
		//====================================

	 
		//$seg_num = $seg_num - 4;  
		$seg_num = $seg_num - 1;  


		$SE_Seg = 'SE*'.$seg_num.'*'.$InvNumber.'~';
		
		//echo ' '.$SE_Seg.'</br>';
		fwrite($fh, $SE_Seg);	
	 	

		//GE Segment';
		//====================================

		$GE_Seg ='GE*1*1~';

		//echo ' '.$GE_Seg.'</br>';
		fwrite($fh, $GE_Seg);	

		//IEA Segment';
		//====================================

		$IEA_Seg ='IEA*1*'.str_pad($InvNumber,9,"0",STR_PAD_LEFT).'~'; 

		//echo ' '.$IEA_Seg.'</br>';
		fwrite($fh, $IEA_Seg);	


		/*str_pad($InvNumber,9,"0",STR_PAD_LEFT)*/


	  mysqli_free_result($result);
	  mysqli_close($conn);



	// Update Last Invoice/Ref Numbers #/
	//======================================

 	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
 

	$query = "UPDATE  	Company  
						 set LastInvoiceNumber = '{$InvNumber}',
							 LastReferenceNumber = '{$Line_Ref_Num}'
					  ";
	



	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));

	mysqli_free_result($result);
	mysqli_close($conn);


	// Update Sessions with InvoiceNum, Billing Status as "Billed"
	//======================================

 	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
 

	$query = "UPDATE  	EIChildrenSessions  
				set InvoiceNumber = '{$InvNumber}',
					BillingStatusId = '1'
			WHERE   FIND_IN_SET(Id, '{$InclIds}') 

					  ";
	



	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));

	mysqli_free_result($result);
	mysqli_close($conn);

     
	fclose($fh);		 
  			  
	echo  "{ success: true, filename: '{$out_File}'}";
	


?>