<?php
	
	require_once("db_login.php");
	require_once('DB.php');


	//echo 'Step 01';

 


	// Get Company Information
	//==============================
	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
 	

	$ReportDate =  $_POST['ReportDate'];

	$Email =  $_POST['Email'];

 	$query1 = "SELECT 	Id
								
							FROM WeeklyServices
							WHERE  ServiceTypeId = '2'
							AND ServiceDate = '{$ReportDate}'";

	
	$result1 = $connection->query ($query1);

	$reports_count =  $result1->numRows();


	if ($reports_count == 0) {

		echo  '0';
		return;

	}




		$DistrictId = '0';



		$exec_string = 'php -f data_setSchGenerateStudentServicesLongTermDailyReportPDF.php '.$ReportDate.' '.$DistrictId.' '.$Email; 

	 	

		$output = exec($exec_string);	

	 

	$connection->disconnect();

	$reports_count = '1';

	echo  $reports_count;
	 

?>