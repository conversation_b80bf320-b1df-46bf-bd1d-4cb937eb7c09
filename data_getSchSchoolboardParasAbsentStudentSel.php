<?php 
	

    require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];

    $query = "	SELECT   distinct   a.StudentId as id,   
    								a.StudentId, 
 						 			CONCAT( trim( c.LastName) , ', ', trim( c.FirstName)  ) as StudentName 
						 


		FROM 	WeeklyServices a, 
				SchServiceTypes b,
				SchStudents c,
 			    Registrants d 
		WHERE   a.ServiceDate between  '{$FromDate}' and '{$ToDate}' 
		    AND a.ScheduleStatusId = '7'
 			AND a.ServiceTypeId = b.Id	
			AND a.AssignmentTypeId = 1
			AND a.StudentId = c.Id
		/*	AND d.TypeId = 23  	*/
            AND a.Registrantid = d.Id
 		ORDER BY StudentName, a.ServiceDate, a.StartTime	
			  ";

	$ret = getData ($conn, $query);
	
	setDisConn($conn);

	echo $ret;


 
?>
