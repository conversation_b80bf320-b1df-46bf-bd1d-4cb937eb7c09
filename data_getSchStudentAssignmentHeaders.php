<?php 
	

     require_once("db_GetSetData.php");

	 $conn = getCon();

	$StudentId = $_GET['StudentId'];


                        $query =" SELECT 	a.Id as Id ,
							                a.Id,
							                a.StudentId,
							                a.StatusId,
							                a.ServiceTypeId as OrigServiceTypeId,
							                a.ServiceTypeId,
							                CONCAT(ServiceTypeDesc,' (',ParaTransportPerc,'%)' ) as ServiceTypeDesc,
							                RegistrantTypeId,
							                RegistrantTypeDesc,
							                AssignmentTypeId,
							                CASE AssignmentTypeId 
							                  WHEN '1' THEN 'Long Term'
							                    ELSE 'Per Diem'
							                END AS AssignmentTypeDesc,
							                a.ConfirmationNumber,
							                IncludeSundayFL,
							                DATE_FORMAT( a.StartDate, '%m-%d-%Y' ) AS StartDate, 
							                DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) AS EndDate, 
							                /*==*/

							                (SELECT  CASE count(*) 
							                WHEN 0 THEN ''
							                ELSE group_concat( WeekDayDesc SEPARATOR ',' )
							                END  
							                FROM SchSchoolAssignmentSelDays f
							                where a.Id = f.AssignmentId) as SelectedDaysDesc,

							                (SELECT  CASE count(*) 
							                WHEN 0 THEN ''
							                ELSE group_concat( WeekDayId SEPARATOR ',' )
							                END  
							                FROM SchSchoolAssignmentSelDays f
							                where a.Id = f.AssignmentId) as SelectedDaysId,

							                /*==*/

							                CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
							                DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							              FROM  SchStudentAssignmentHeader a 
							                       LEFT JOIN 
							                    Users b on a.UserId = b.UserId 
							                       LEFT JOIN
							                    SchStudentMandates c on a.MandateId = c.Id 
							                       LEFT JOIN
							                    SchServiceTypes d on a.ServiceTypeId = d.Id
							                       LEFT JOIN
							                    RegistrantTypes f on d.RegistrantTypeId  = f.Id 
							                WHERE a.StudentId = '{$StudentId}'
							                AND d.ServiceCategoryId = 1 /* Category: Student */
							                -- AND YEAR(a.EndDate) >= YEAR(CURDATE())
							                 
							                 
							                ORDER BY  a.StartDate DESC  ";
	
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  

 
?>
