<?php


    require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
    $ServiceDate = $_GET['ServiceDate'];
    $date = strtotime($ServiceDate);;
    $service_date_frm = date('m/d/Y',$date);;
 
    $service_date_frm = date('m-d-Y',strtotime($ServiceDate));
    
    $charset = 'utf8mb4';

    // Create a new connection
    $pdo = new PDO("mysql:host=$db_hostname;dbname=$db_database;charset=$charset", $db_username, $db_password);

    // Execute the stored procedure
    $stmt = $pdo->prepare("CALL proc_getSchRNDailyCallInReportData(?)");
    $stmt->execute(['2023-09-18']);  // replace 'your_date_here' with your date

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $objPHPExcel = new PHPExcel();
    $objPHPExcel->setActiveSheetIndex(0);


    $objPHPExcel->getActiveSheet()->getStyle("A1:K1")->getFont()->setSize(24);
    $objPHPExcel->getActiveSheet()->getStyle("A1:K1")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 1, 'RCM Health Services'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 1, 'Date:'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(7, 1, $service_date_frm); 

   $objPHPExcel->getActiveSheet()->getStyle("A2:K2")->getFont()->setSize(22);
    $objPHPExcel->getActiveSheet()->getStyle("A2:K2")->getFont()->setBold( true );

    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 2, 'District Name:'); 
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(5, 2, 'District 1'); 


    // Set header - assuming $results is not empty
    $column = 0;
    foreach ($results[0] as $header => $value) {
        // $sheet->setCellValueByColumnAndRow($column, 1, $header);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, 4, $header); 
    
        $column++;
    }

    // Set results
    $row = 5;
    foreach ($results as $result) {
        $column = 0;
        foreach ($result as $value) {
            // $sheet->setCellValueByColumnAndRow($column, $row, $value);
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column, $row, $value); 

            $column++;
        }
        $row++;
    }  
 
 $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

 $out_File = "../rn_reports/rn_daily_report.xlsx";

 $objWriter->save($out_File);

 echo "Data written to output.xlsx\n";

?>
