<?php 

 
 	$NotificationEmails = $_POST['NotificationEmails'];
	$Subject = $_POST['Subject'];
	$Message = $_POST['Message'];
	$UserId = $_POST['UserId'];   

    use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
    use P<PERSON>Mailer\PHPMailer\Exception;

    require 'PHPMailer/src/Exception.php';
    require 'PHPMailer/src/PHPMailer.php';
    require 'PHPMailer/src/SMTP.php';

	$mail  = new PHPMailer();  

	$mail->AddReplyTo("<EMAIL>","RCM Healthcare Services");

    $mail->SetFrom('<EMAIL>', 'RCM Healthcare Services');

    $mail->AddReplyTo("<EMAIL>","RCM Healthcare Services");


	// Set the subject
    $mail->Subject = $Subject;

    // Set the body of the email
    $mail->Body = $Message;


   // Replace semicolons with commas in the email addresses
    $NotificationEmails = str_replace(";", ",", $NotificationEmails);

    // Split the email addresses into an array
    $emailAddresses = explode(",", $NotificationEmails);


    // Loop through each email address and send the email
    foreach($emailAddresses as $email) {
        $mail->addAddress(trim($email)); // Add a recipient
    }


    if(!$mail->send()) {
        echo 'Message could not be sent.';
        echo 'Mailer Error: ' . $mail->ErrorInfo;
    } else {
        echo 'Message has been sent';
    }


	 


?>
