<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$ParaTherapyTypeId = $_GET['ParaTherapyTypeId'];
	$HrTypeId = $_GET['HrTypeId'];
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
	$FromName = $_GET['FromName'];
	$ToName = $_GET['ToName'];
	$SchoolId = $_GET['SchoolId'];
	$BillingContractId = $_GET['BillingContractId'];

 
  $query = "call proc_getSchPayrollOpenTransactions ( '{$HrTypeId}',
  	                                        '{$FromDate}',
  	                                        '{$ToDate}',
  	                                        '{$FromName}',
  	                                        '{$ToName}',
  	                                        '{$SchoolId}',
  	                                        '{$BillingContractId}' 
     
 																						)";


	// $ret = getData ($conn, $query);
	setDisConn($conn);

	// echo $ret;  
	echo $query;

?>

 