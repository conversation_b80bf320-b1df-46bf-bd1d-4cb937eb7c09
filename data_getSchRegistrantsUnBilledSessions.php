<?php 
	

  require_once("db_GetSetData.php");

  $conn = getCon();

  $Month = $_GET['Month'];
  $Year = $_GET['Year'];
  $SchoolId = $_GET['SchoolId'];
  $RegistrantId = $_GET['RegistrantId'];


  $query = "call proc_getSchRegistrantsUnBilledSessions ( '{$Month}', 
                                                          '{$Year}', 
                                                          '{$SchoolId}',  
                                                          '{$RegistrantId}' ) "; 


  $ret = getData ($conn, $query);
  setDisConn($conn);

  echo $ret;


/*  

  require "ewDataHandler.php";  
    
  $rcr_transaction = new dataHandler(); 

 
  //$PayrollWeek = $_GET['PayrollWeek'];

  $Month = $_GET['Month'];
  $Year = $_GET['Year'];
  $SchoolId = $_GET['SchoolId'];
  $RegistrantId = $_GET['RegistrantId'];

 
  $result = $rcr_transaction->getSchRegistrantsUnBilledSessions($Month, $Year, $SchoolId, $RegistrantId);
 

  $rcr_transaction->disconnectDB (); 

  echo  "{ success: true,  data: ".json_encode($result)."}";
*/
?>

