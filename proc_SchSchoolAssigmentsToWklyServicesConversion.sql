

	/*=========================================*/

	DELIMITER $$

	DROP PROCEDURE IF EXISTS proc_SchSchoolAssigmentsToWklyServicesConversion$$

	CREATE PROCEDURE proc_SchSchoolAssigmentsToWklyServicesConversion ()  

	BEGIN

			DECLARE v_MonDate, v_TueDate, v_WedDate, v_ThuDate, v_FriDate, v_SunDate,  v_PayrollWeek DATE ; 
			DECLARE v_SchoolClientId INT ; 

			SELECT Id INTO v_SchoolClientId
				FROM Clients
			WHERE SchoolFL = '1' LIMIT 1;	

			/* Mon */
		 SELECT DATE_ADD(curdate(), INTERVAL (9 - IF(DAYOFWEEK(curdate())=1, 8, DAYOFWEEK(curdate()))) DAY) INTO v_MonDate; 
		/*  	SELECT DATE_ADD('2017-09-04', INTERVAL (9 - IF(DAYOFWEEK('2017-09-04')=1, 8, DAYOFWEEK('2017-09-04'))) DAY) INTO v_MonDate;  */

			/* Tue */
			SELECT DATE_ADD(v_MonDate, INTERVAL 1 DAY) INTO v_TueDate;

			/* Wed */
			SELECT DATE_ADD(v_MonDate, INTERVAL 2 DAY) INTO v_WedDate;

			/* Thu */
			SELECT DATE_ADD(v_MonDate, INTERVAL 3 DAY) INTO v_ThuDate;

			/* Fri */
			SELECT DATE_ADD(v_MonDate, INTERVAL 4 DAY) INTO v_FriDate;

			/* Sun */
			SELECT DATE_ADD(v_MonDate, INTERVAL -1 DAY) INTO v_SunDate;


			/* Sat - Payroll Week */
			SELECT DATE_ADD(v_MonDate, INTERVAL 5 DAY) INTO v_PayrollWeek;


	/* Student Assignments
	 ============================*/		
	 		create temporary table tmp1 engine=memory
			
			SELECT 	a.Id as AssignmentId,
					a.StudentId,
					a.ServiceTypeId,
					a.ConfirmationNumber,
					a.AssignmentTypeId,
					b.RegistrantId,
					b.WeekDayId,
					d.WeekDay,
					CASE b.WeekDayId
						WHEN 1 THEN v_MonDate	
						WHEN 2 THEN v_TueDate	
						WHEN 3 THEN v_WedDate	
						WHEN 4 THEN v_ThuDate	
						WHEN 5 THEN v_FriDate	
					END as ServiceDate,	
					b.StartTime,
					b.EndTime,
					b.TotalHours,
					e.SchoolId
			FROM  	SchStudentAssignmentHeader a,
					SchStudentAssignmentDetails b,
					Registrants c,
					DaysOfWeek5 d,
					SchStudents e
			WHERE a.StatusId = '1'
			AND  v_MonDate between a.StartDate and a.EndDate 
			AND  a.Id = b.AssignmentId 
			AND  b.RegistrantId = c.Id 
			AND  b.WeekDayId = d.WeekDayId
			AND  a.StudentId = e.Id
			AND  a.IncludeSundayFL = '0' 
			AND  a.ServiceTypeId < 39

		UNION		

			SELECT 	a.Id as AssignmentId,
					a.StudentId,
					a.ServiceTypeId,
					a.ConfirmationNumber,
					a.AssignmentTypeId,
					b.RegistrantId,
					b.WeekDayId,
					d.WeekDay,
					CASE b.WeekDayId
						WHEN 1 THEN v_MonDate	
						WHEN 2 THEN v_TueDate	
						WHEN 3 THEN v_WedDate	
						WHEN 4 THEN v_ThuDate	
						WHEN 5 THEN v_FriDate	
						WHEN 0 THEN v_SunDate	

					END as ServiceDate,	
					b.StartTime,
					b.EndTime,
					b.TotalHours,
					e.SchoolId
			FROM  	SchStudentAssignmentHeader a,
					SchStudentAssignmentDetails b,
					Registrants c,
					DaysOfWeek d,
					SchStudents e
			WHERE a.StatusId = '1'
			AND  v_MonDate between a.StartDate and a.EndDate 
			AND  a.Id = b.AssignmentId 
			AND  b.RegistrantId = c.Id 
			AND  b.WeekDayId = d.WeekDayId
			AND  a.StudentId = e.Id
			AND  a.IncludeSundayFL = '1' 
			AND  a.ServiceTypeId < 39



			  ;
	 

			/* Delete Duplicates
			 =======================*/	  

			 DELETE FROM tmp1 
			 WHERE EXISTS (SELECT 1 FROM WeeklyServices a 
			 				WHERE tmp1.StudentId = a.StudentId
			 				AND   tmp1.ServiceDate = a.ServiceDate
			 				AND   tmp1.ServiceTypeId = a.ServiceTypeId
			 				AND   a.CLientId = v_SchoolClientId 
							AND   a.ServiceTypeId < 39
	
			 	);


			INSERT IGNORE INTO WeeklyServices
		                (	
							AssignmentId,
							CLientId,
							PayrollWeek,
							StudentId,
							ScheduleStatusId,
							ServiceTypeId,
							AssignmentTypeId,	
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours , 
							WeekDay,
							SchoolId,
							UserId,
							TransDate 
						)	
											
					SELECT  AssignmentId,
							v_SchoolClientId,
							v_PayrollWeek,
							StudentId,
							'7',
							ServiceTypeId,
							AssignmentTypeId,
							ConfirmationNumber,
							RegistrantId,
							ServiceDate,	 
							StartTime, 
							EndTime, 		
							TotalHours, 
							WeekDay,
							SchoolId, 
							'0',
							NOW()
					FROM tmp1	;

		

			drop temporary table if exists tmp1;




	END $$

	DELIMITER ;	