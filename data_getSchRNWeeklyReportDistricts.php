<?php 
	
   require ("db_login.php");
    include('../../phpexcel-1-8/Classes/PHPExcel.php');
    include('../../phpexcel-1-8/Classes/PHPExcel//Writer/Excel2007.php');
 
 

  $ServiceDate = $_GET['ServiceDate'];

  $date = new DateTime($ServiceDate);

  $date_mon = $date->modify('-4 days');
  $date_mon_unf =   $date_mon->format('Y-m-d');
  $date_mon_frm =   $date_mon->format('m/d/Y');


  $date_tue = $date->modify('+1 days');
  $date_tue_unf =   $date_tue->format('Y-m-d');
  $date_tue_frm =   $date_tue->format('m/d/Y');

  $date_wed = $date->modify('+1 days');
  $date_wed_unf =   $date_wed->format('Y-m-d');
  $date_wed_frm =   $date_wed->format('m/d/Y');

  $date_thu = $date->modify('+1 days');
  $date_thu_unf =   $date_thu->format('Y-m-d');
  $date_thu_frm =   $date_thu->format('m/d/Y');

 $date_fri = $date->modify('+1 days');
  $date_fri_unf =   $date_fri->format('Y-m-d');
  $date_fri_frm =   $date_fri->format('m/d/Y');


  require_once("db_GetSetData.php");

  $conn = getCon();

   
  $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setSchRNWeeklyReportEmail.php";


  $query = "SELECT DISTINCT
              i.DistrictId, 
              TRIM(j.DistrictName) AS DistrictName,
              concat(j.LiaisonFirstName,' ',j.LiaisonLastName) as LiaisonName,
              j.Email as LiaisonEmail
          FROM
              WeeklyServices a
                  JOIN
              SchSchools i ON a.SchoolId = i.Id
                  JOIN
              SchDistricts j ON i.DistrictId = j.Id
          WHERE
              a.ServiceDate between '{$date_mon_unf}' and '{$date_fri_unf}'
                  AND a.ServiceTypeId BETWEEN 43 AND 45 
                  AND j.Email != ''
                  LIMIT 1  
  "; 
  
//   // echo 'query: '.$query;


  $result =  mysqli_query($conn, $query);
     
  while ($row = $result->fetch_assoc()) {
   
    
      $DistrictId = $row['DistrictId'];
      $DistrictName = $row['DistrictName'];
      $LiaisonName = $row['LiaisonName'];
      $LiaisonEmail = $row['LiaisonEmail'];

      $fields = array('ServiceDateFri' => $date_fri_unf, 
                      'DistrictId' => $DistrictId,
                      'DistrictName' => $DistrictName, 
                      'LiaisonName' => $LiaisonName,
                      'LiaisonEmail' => $LiaisonEmail  
                   );


     
     // print_r($fields);

     $curl = curl_init();
       
     curl_setopt($curl, CURLOPT_URL, $url);
     curl_setopt($curl, CURLOPT_POSTFIELDS, $fields); 
     curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false

     $str = curl_exec($curl);

     echo '$str :'.$str;
        
     curl_close($curl);

    }



  setDisConn($conn);

  echo $ret;


  
?>

