<?php

	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php');

 

 	$RegistrantId = $_GET['RegistrantId'];
 	//$SchoolId = $_GET['SchoolId'];
	$Month = $_GET['Month'];
	$Year = $_GET['Year'];
	
	$GLOBALS['MonthYear'] = $Month.'/'.$Year;

    $Data = $_GET['Data'];
    $Data=json_decode($Data,true);


  	
   $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 
 
   foreach ($Data as $SchoolId) {


  	
	//==================================
	// Get Company Name/User's Email
	//==================================
	

 
	$query = "SELECT DISTINCT 	CompanyName,
								CONCAT(a.FirstName,' ',a.LastName) as RegistrantName, 		
						        LEFT(SchoolName, 40) as SchoolName,
						        MID(DistrictName,10,2) as DistrictName,
						        BoroughName,
						        ServiceTypeDesc 
						                 
						FROM Registrants a,
						     RegistrantTypes b,
						     SchSchools e,
						     SchDistricts f,
						     SchBoroughs g,
						     SchServiceTypes h,
						     Company  
						  
						WHERE a.Id = '{$RegistrantId}' 
						AND a.TypeId = b.Id
						AND e.Id = '{$SchoolId}'
						AND e.DistrictId = f.Id
						AND f.BoroughId = g.Id
						AND b.DefaultServiceTypeId = h.Id ";
				
	
	$result = $connection->query ($query);
	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$GLOBALS['Company'] = $row['CompanyName'];
		$GLOBALS['RegistrantName'] = $row['RegistrantName'];
		$GLOBALS['SchoolName'] = $row['SchoolName'];
		$GLOBALS['DistrictName'] = $row['DistrictName'];
		$GLOBALS['BoroughName'] = $row['BoroughName'];
		$GLOBALS['ServiceTypeDesc'] = $row['ServiceTypeDesc'];
		
	}	
 
	
	class PDF extends FPDF
	{

	 
		function PDF($orientation='L',$unit='mm',$format='A4')
		
		{
			//Call parent constructor
			$this->FPDF($orientation,$unit,$format);
		}
	
	 
	//Page header
	function Header()
	{	
		$this->SetLeftMargin(5);
		$this->SetRightMargin(5);

		//Logo
		//$this->Image('logo.jpg',5,5,30);
		//Arial bold 15
		$this->SetFont('Arial','B',10);
	 	
		
  
		$this->Cell(0,5,'New York City Department of Education',0,1,'C');
		$this->Cell(0,5,'Division for Special Education',0,1,'C');
		$this->Cell(0,5,'Office of Related and Contractual Services',0,1,'C');
		$this->Cell(0,5,'Monthly Survey of Related Services',0,1,'C');

		$this->Ln(4);

		$this->Cell(30,5,'',0,0,'L');
		$this->SetFont('Arial','B',9);
		$this->Cell(20,5,'Agency: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(100,5,$GLOBALS['Company'],0,0,'L');
		$this->SetFont('Arial','B',9);
		$this->Cell(30,5,'Agency Code: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(20,5,'#0120',0,1,'L');

		$this->Ln(4);

		$this->Cell(10,5,'',0,0,'L');
		$this->SetFont('Arial','B',9);
		$this->Cell(15,5,'Month: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(20,5,$GLOBALS['MonthYear'],0,l,'L');

		$this->SetFont('Arial','B',9);
		$this->Cell(15,5,'Borough: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(20,5,$GLOBALS['BoroughName'],0,l,'L');

		$this->SetFont('Arial','B',9);
		$this->Cell(15,5,'District: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(20,5,$GLOBALS['DistrictName'],0,l,'L');

		$this->SetFont('Arial','B',9);
		$this->Cell(15,5,'School: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(70,5,$GLOBALS['SchoolName'],l,0,'L');

		$this->SetFont('Arial','B',9);
		$this->Cell(15,5,'Service: ',0,0,'L');
		$this->SetFont('Arial','U',9);
		$this->Cell(30,5,$GLOBALS['ServiceTypeDesc'],l,0,'L');

		
		/*=====================================================*/





		//Line break
		$this->Ln(15);
 		$this->SetFont('Arial','B',8);
		$this->Cell(35,4,'Student First Name',1,0,'C');
		$this->Cell(35,4,'Student Last Name',1,0,'C');
		$this->Cell(20,4,'NYCID #',1,0,'C');
		$this->Cell(15,4,'DOB',1,0,'C');
		$this->Cell(20,4,'Mand. Freq.',1,0,'C');
		$this->Cell(20,4,'Mand. Dur.',1,0,'C');
		$this->Cell(20,4,'Mand. Grp.',1,0,'C');
		$this->Cell(15,4,'Start Date',1,0,'C');
		$this->Cell(20,4,'Sess. Miss.',1,0,'C');
		$this->Cell(20,4,'Sess. Rcv.',1,0,'C');
		$this->Cell(15,4,'Stop Date',1,0,'C');
		$this->Cell(40,4,'Comments',1,1,'C');



		//=============================


 	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		$this->SetFont('Arial','I',8);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
	}




	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	//$pdf->AddPage();
	//$pdf->SetFont('Times','',12);
	$pdf->SetFont('Arial','',10);

	//++++++++++++++++++++++++++++++++++++++++++++++
   
 $query1 = "SELECT distinct c.Id,
 							b.FirstName,
                			b.LastName, 
			                CONCAT(
			                  SUBSTR(b.ExtId,1,3), 
			                  '-',
			                  SUBSTR(b.ExtId,4,2),
			                  '-',
			                  SUBSTR(b.ExtId,6,4)
			                 ) AS NYCID,
			                 DATE_FORMAT( b.DateOfBirth, '%m/%d/%y' ) AS DOB,
			                 c.SessionFrequency,
			                 c.SessionLength,
			                 c.SessionGrpSize,

			                 DATE_FORMAT( c.StartDate, '%m/%d/%y' ) AS StartDate,
			                 DATE_FORMAT( c.EndDate, '%m/%d/%y' ) AS EndDate,
			                 ( SELECT Count(*) from WeeklyServices d
			                      where d.RegistrantId = '{$RegistrantId}' 
										and d.ScheduleStatusId > 6    
										and b.Id = d.StudentId 
										and month(d.ServiceDate) = '{$Month}'
										and year(d.ServiceDate) = '{$Year}'  
										and d.MandateId = c.Id   
			                    ) as SessionsReceived 
		                 
		FROM WeeklyServices a, 
		     SchStudents b,
		     SchStudentMandates c 
	WHERE a.RegistrantId = '{$RegistrantId}' 
		AND   a.ScheduleStatusId > 6    
		AND month(a.ServiceDate) = '{$Month}'
		AND year(a.ServiceDate) = '{$Year}'
		AND a.SchoolId = '{$SchoolId}'
		AND b.Id = a.StudentId
		and c.BillingContractId = '1'
		AND a.MandateId = c.Id
	ORDER BY b.LastName, b.FirstName 	
		"; 
    
	
	$result1 = $connection->query($query1);
	 
	$j = $result1->numRows();
	 
	//++++++++++++++++++++++++++++++++++++++++++++++
	
	
	$i = 0; 
	
	while ($row =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {
			
		$i++;

		if ($i == 1) {

			$pdf->AddPage();

		}

		$pdf->SetFont('Arial','',8);
		$pdf->Cell(35,4,$row['FirstName'],1,0,'C');
		$pdf->Cell(35,4,$row['LastName'],1,0,'C');
		$pdf->Cell(20,4,$row['NYCID'],1,0,'C');
		$pdf->Cell(15,4,$row['DOB'],1,0,'C');
		$pdf->Cell(20,4,$row['SessionFrequency'],1,0,'C');
		$pdf->Cell(20,4,$row['SessionLength'],1,0,'C');
		$pdf->Cell(20,4,$row['SessionGrpSize'],1,0,'C');
		$pdf->Cell(15,4,$row['StartDate'],1,0,'C');
		$pdf->Cell(20,4,'',1,0,'C');
		$pdf->Cell(20,4,$row['SessionsReceived'],1,0,'C');
		$pdf->Cell(15,4,$row['EndDate'],1,0,'C');
		$pdf->Cell(40,4,'',1,1,'C');

		if ($i > 22) {
			$pdf->Ln(4);
	 		
			$pdf->Cell(30,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(32,5,"Provider's Name: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(40,5,$GLOBALS['RegistrantName'],0,0,'L');
			$pdf->Cell(60,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(50,5,"School's Representative Name: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(70,5,'                                                ' ,0,1,'L');

			$pdf->SetFont('Arial','',6);
			$pdf->Cell(210,5,'',0,0,'L');
			$pdf->Cell(35,4,"(Please Print Name)",0,1,'C');

			$pdf->Ln(4);

			$pdf->Cell(26,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(32,5,"Provider's Signature: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(50,5,'                                                 ' ,0,0,'L');
			$pdf->Cell(48,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(55,5,"School's Representative Signature: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(70,5,'                                                ' ,0,1,'L');

			$pdf->Ln(4);


			$pdf->Cell(30,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(28,5,"SS#: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(40,5,'On File W/Agency' ,0,0,'L');
	 		$pdf->Cell(60,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(53,5,"Date: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(30,5,'                             ' ,0,1,'L');

	        //$pdf->SetY(50);

	        $i = 0;

		}
	
	}	

	//==================================
	// Signatures 


	/*
        $cur_y =  $pdf->GetY();
    	 
        if ($cur_y > 150) {

	    	$pdf->AddPage();
	        $pdf->SetY(50);


        }
    */     

        if ($i > 0) {

			$pdf->Ln(4);
	 		
			$pdf->Cell(30,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(32,5,"Provider's Name: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(40,5,$GLOBALS['RegistrantName'],0,0,'L');
			$pdf->Cell(60,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(50,5,"School's Representative Name: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(70,5,'                                                ' ,0,1,'L');

			$pdf->SetFont('Arial','',6);
			$pdf->Cell(210,5,'',0,0,'L');
			$pdf->Cell(35,4,"(Please Print Name)",0,1,'C');

			$pdf->Ln(4);

			$pdf->Cell(26,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(32,5,"Provider's Signature: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(50,5,'                                                 ' ,0,0,'L');
			$pdf->Cell(48,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(55,5,"School's Representative Signature: ",0,0,'L');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(70,5,'                                                ' ,0,1,'L');

			$pdf->Ln(4);


			$pdf->Cell(30,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(28,5,"SS#: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(40,5,'On File W/Agency' ,0,0,'L');
	 		$pdf->Cell(60,5,'',0,0,'L');
			$pdf->SetFont('Arial','B',9);
			$pdf->Cell(53,5,"Date: ",0,0,'R');
			$pdf->SetFont('Arial','U',9);
			$pdf->Cell(30,5,'                             ' ,0,1,'L');




        }
 
 

	//==================================

	
	}	

	$pdf->Output();
	
	$connection->disconnect();
 	
?>
