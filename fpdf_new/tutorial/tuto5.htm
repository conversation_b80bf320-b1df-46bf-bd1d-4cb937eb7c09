<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Tables</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Tables</h1>
This tutorial shows different ways to make tables.
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

class </span>PDF <span class="kw">extends </span>FPDF
<span class="kw">{
</span><span class="cmt">// Load data
</span><span class="kw">function </span>LoadData<span class="kw">(</span>$file<span class="kw">)
{
    </span><span class="cmt">// Read file lines
    </span>$lines <span class="kw">= </span>file<span class="kw">(</span>$file<span class="kw">);
    </span>$data <span class="kw">= array();
    foreach(</span>$lines <span class="kw">as </span>$line<span class="kw">)
        </span>$data<span class="kw">[] = </span>explode<span class="kw">(</span><span class="str">';'</span><span class="kw">,</span>trim<span class="kw">(</span>$line<span class="kw">));
    return </span>$data<span class="kw">;
}

</span><span class="cmt">// Simple table
</span><span class="kw">function </span>BasicTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// Header
    </span><span class="kw">foreach(</span>$header <span class="kw">as </span>$col<span class="kw">)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>7<span class="kw">,</span>$col<span class="kw">,</span>1<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Data
    </span><span class="kw">foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        foreach(</span>$row <span class="kw">as </span>$col<span class="kw">)
            </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>6<span class="kw">,</span>$col<span class="kw">,</span>1<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    }
}

</span><span class="cmt">// Better table
</span><span class="kw">function </span>ImprovedTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// Column widths
    </span>$w <span class="kw">= array(</span>40<span class="kw">, </span>35<span class="kw">, </span>40<span class="kw">, </span>45<span class="kw">);
    </span><span class="cmt">// Header
    </span><span class="kw">for(</span>$i<span class="kw">=</span>0<span class="kw">;</span>$i<span class="kw">&lt;</span>count<span class="kw">(</span>$header<span class="kw">);</span>$i<span class="kw">++)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>$i<span class="kw">],</span>7<span class="kw">,</span>$header<span class="kw">[</span>$i<span class="kw">],</span>1<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Data
    </span><span class="kw">foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>0<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>0<span class="kw">],</span><span class="str">'LR'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>1<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>1<span class="kw">],</span><span class="str">'LR'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>2<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>2<span class="kw">]),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>3<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>3<span class="kw">]),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    }
    </span><span class="cmt">// Closing line
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>array_sum<span class="kw">(</span>$w<span class="kw">),</span>0<span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'T'</span><span class="kw">);
}

</span><span class="cmt">// Colored table
</span><span class="kw">function </span>FancyTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// Colors, line width and bold font
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>255<span class="kw">,</span>0<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>255<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetDrawColor<span class="kw">(</span>128<span class="kw">,</span>0<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetLineWidth<span class="kw">(</span>.3<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">);
    </span><span class="cmt">// Header
    </span>$w <span class="kw">= array(</span>40<span class="kw">, </span>35<span class="kw">, </span>40<span class="kw">, </span>45<span class="kw">);
    for(</span>$i<span class="kw">=</span>0<span class="kw">;</span>$i<span class="kw">&lt;</span>count<span class="kw">(</span>$header<span class="kw">);</span>$i<span class="kw">++)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>$i<span class="kw">],</span>7<span class="kw">,</span>$header<span class="kw">[</span>$i<span class="kw">],</span>1<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">,</span>true<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Color and font restoration
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>224<span class="kw">,</span>235<span class="kw">,</span>255<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">);
    </span><span class="cmt">// Data
    </span>$fill <span class="kw">= </span>false<span class="kw">;
    foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>0<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>0<span class="kw">],</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>1<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>1<span class="kw">],</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>2<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>2<span class="kw">]),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>3<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>3<span class="kw">]),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
        </span>$fill <span class="kw">= !</span>$fill<span class="kw">;
    }
    </span><span class="cmt">// Closing line
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>array_sum<span class="kw">(</span>$w<span class="kw">),</span>0<span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'T'</span><span class="kw">);
}
}

</span>$pdf <span class="kw">= new </span>PDF<span class="kw">();
</span><span class="cmt">// Column headings
</span>$header <span class="kw">= array(</span><span class="str">'Country'</span><span class="kw">, </span><span class="str">'Capital'</span><span class="kw">, </span><span class="str">'Area (sq km)'</span><span class="kw">, </span><span class="str">'Pop. (thousands)'</span><span class="kw">);
</span><span class="cmt">// Data loading
</span>$data <span class="kw">= </span>$pdf<span class="kw">-&gt;</span>LoadData<span class="kw">(</span><span class="str">'countries.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>14<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>BasicTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>ImprovedTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>FancyTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto5.php' target='_blank' class='demo'>[Demo]</a></p>
A table being just a collection of cells, it's natural to build one from them. The first
example is achieved in the most basic way possible: simple framed cells, all of the same size
and left aligned. The result is rudimentary but very quick to obtain.
<br>
<br>
The second table brings some improvements: each column has its own width, headings are centered,
and numbers right aligned. Moreover, horizontal lines have been removed. This is done by means
of the <code>border</code> parameter of the <a href='../doc/cell.htm'>Cell()</a> method, which specifies which sides of the
cell must be drawn. Here we want the left (<code>L</code>) and right (<code>R</code>) ones. It remains
the problem of the horizontal line to finish the table. There are two possibilities: either
check for the last line in the loop, in which case we use <code>LRB</code> for the <code>border</code>
parameter; or, as done here, add the line once the loop is over.
<br>
<br>
The third table is similar to the second one but uses colors. Fill, text and line colors are
simply specified. Alternate coloring for rows is obtained by using alternatively transparent
and filled cells.
</body>
</html>
