<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$ParaTherapyTypeId = $_GET['ParaTherapyTypeId'];
	$HrTypeId = $_GET['HrTypeId'];
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];
	$FromName = $_GET['FromName'];
	$ToName = $_GET['ToName'];
	$SchoolId = $_GET['SchoolId'];
	$BillingContractId = $_GET['BillingContractId'];



    if ($ParaTherapyTypeId == '23')  {

$query = "  

    

    SELECT  a.Id AS id, 
						a.Id AS ScheduleId,
						ScheduleStatusId, 
						ScheduleStatusDesc,
						a.ServiceTypeId,
						ServiceTypeDesc,
						ServiceDate as ServiceDateSort,
						DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
						StartTime as StartTimeNum,
						EndTime as EndTimeNum,
						DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						PayrollWeek,   
						LunchHour,
						TotalHours, 
						WeekDay, 
						a.RegistrantId, 
						b.LastName,
						b.FirstName,
						CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
						RegistrantTypeDesc, 
						a.SchoolId,
						CONCAT(TRIM(m.SchoolName),' (',DistrictName,') ') as SchoolName,
						CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolNameDBN,

						COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName) ) 
							FROM SchStudents c 
							WHERE a.Studentid = c.Id
						),'') as  StudentName,

						b.HrId,
						n.BoroughName,
						CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
						DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
	
						FROM
					    WeeklyServices a
					        JOIN
					    Registrants b ON a.RegistrantId = b.Id
					        JOIN
					    RegistrantTypes f ON b.TypeId = f.Id
					        LEFT JOIN
					    Users e ON a.UserId = e.UserId
					        JOIN
					    SchServiceTypes h ON a.ServiceTypeId = h.Id
					        JOIN
					    ScheduleStatuses g ON ScheduleStatusId = g.Id
					        JOIN
					    SchSchools i ON a.SchoolId = i.Id
					        JOIN
					    SchDistricts j ON i.DistrictId = j.Id
					       LEFT JOIN
					    SchStudents l ON a.StudentId = l.Id
					        JOIN
					    SchSubSchools m ON a.SchoolId = m.SchoolId
					                       AND l.SubSchoolTypeId = m.SubSchoolTypeId
					        JOIN
					    SchBoroughs n ON j.BoroughId = n.Id


					WHERE
					    ScheduleStatusId = 8
					        AND a.PayrollBatchNumber = ''
					        AND a.SchoolId != 0
					        AND a.AssignmentId != 0 
									AND a.ServiceDate between '{$FromDate}'and '{$ToDate}'  
					        AND b.TypeId = '{$ParaTherapyTypeId}'
					        AND b.HrTypeId = '{$HrTypeId}'
					        AND a.PaidFL = '0'
 					    		AND SUBSTR(UPPER(TRIM(b.LastName)),1,1) between '{$FromName}'and '{$ToName}' 
					    		AND a.SchoolId like '{$SchoolId}'
								AND EXISTS( SELECT 
					            1
					        FROM
          		  SchStudentMandates o, SchStudentAssignmentHeader p
        						WHERE
              					  a.AssignmentId = p.Id
               						and p.MandateId = o.Id
					                AND o.ParaTransportPerc != 0
					                -- AND a.ServiceDate BETWEEN o.StartDate AND o.EndDate
					                AND o.BillingContractId = '{$BillingContractId}')	 								                           
						
						 		

						ORDER BY  LastName,  FirstName, ServiceDateSort, StartTimeNum  

						 ";





    } elseif ($ParaTherapyTypeId == '12') {

    $query = "  SELECT  a.Id AS id, 
						a.Id AS ScheduleId,
						ScheduleStatusId, 
						ScheduleStatusDesc,
						a.ServiceTypeId,
						ServiceTypeDesc,
						ServiceDate as ServiceDateSort,
						DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
						StartTime as StartTimeNum,
						EndTime as EndTimeNum,
						DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						PayrollWeek,   
						LunchHour,
						TotalHours, 
						WeekDay, 
						a.RegistrantId, 
						CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
						RegistrantTypeDesc, 
						a.SchoolId,
						CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolName,
						CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ') as SchoolNameDBN,

						COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName) ) 
							FROM SchStudents c 
							WHERE a.Studentid = c.Id
						),'') as  StudentName,

						b.HrId,
						n.BoroughName,
						CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
						DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
	
						FROM
					    WeeklyServices a
					        JOIN
					    Registrants b ON a.RegistrantId = b.Id
					        JOIN
					    RegistrantTypes f ON b.TypeId = f.Id
					        LEFT JOIN
					    Users e ON a.UserId = e.UserId
					        JOIN
					    SchServiceTypes h ON a.ServiceTypeId = h.Id
					        JOIN
					    ScheduleStatuses g ON ScheduleStatusId = g.Id
					        JOIN
					    SchSchools i ON a.SchoolId = i.Id
					        JOIN
					    SchDistricts j ON i.DistrictId = j.Id
					       LEFT JOIN
					    SchStudents l ON a.StudentId = l.Id
					        LEFT JOIN
					    SchSubSchools m ON a.SchoolId = m.SchoolId
					        AND l.SubSchoolTypeId = m.SubSchoolTypeId
					        JOIN
					    SchBoroughs n ON j.BoroughId = n.Id

					WHERE
					    ScheduleStatusId = 8
					        AND a.PayrollBatchNumber = ''
					        AND a.SchoolId != 0
									AND a.ServiceDate between '{$FromDate}'and '{$ToDate}'  
					        AND b.TypeId = '{$ParaTherapyTypeId}'
					        AND b.HrTypeId = '{$HrTypeId}'
					        AND a.PaidFL = '0'
 					    		AND SUBSTR(UPPER(TRIM(b.LastName)),1,1) between '{$FromName}'and '{$ToName}' 
					    		AND a.SchoolId like '{$SchoolId}'
									
 
                                
						ORDER BY b.LastName, b.FirstName, ServiceDateSort, StartTimeNum   ";
     


   } else {

    $query = "  SELECT  a.Id AS id, 
						a.Id AS ScheduleId,
						ScheduleStatusId, 
						ScheduleStatusDesc,
						a.ServiceTypeId,
						ServiceTypeDesc,
						ServiceDate as ServiceDateSort,
						DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
						StartTime as StartTimeNum,
						EndTime as EndTimeNum,
						DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						PayrollWeek,   
						LunchHour,
						TotalHours, 
						WeekDay, 
						a.RegistrantId, 
						CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
						RegistrantTypeDesc, 
						a.SchoolId,
						/*CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolName, */

                        case BillingContractId
                          when '1' then CONCAT(TRIM(m.SchoolName),' (',DistrictName,') ')
                          else COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName) ) 
							FROM SchStudents c 
							WHERE a.Studentid = c.Id
						    ),'')
                          end as SchoolName, 

                        case BillingContractId
                          when '1' then CONCAT(TRIM(i.SchoolName),' (',DistrictName,') ')
                          else COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName) ) 
							FROM SchStudents c 
							WHERE a.Studentid = c.Id
						    ),'')
                          end as SchoolNameDBN, 

						COALESCE(( SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName) ) 
							FROM SchStudents c 
							WHERE a.Studentid = c.Id
						),'') as  StudentName,


						b.HrId,
						n.BoroughName,
						CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
						DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
					FROM 	WeeklyServices a, 
							Registrants b, 
							RegistrantTypes f,
							Users e,
							SchServiceTypes h,
							ScheduleStatuses g,
							SchSchools i,
							SchDistricts j,
              SchStudentMandates k,
              SchStudents l,
              SchSubSchools m,
              SchBoroughs n																				
						WHERE 	ScheduleStatusId = 8
								AND a.PayrollBatchNumber = ''
								AND a.SchoolId != 0
								AND a.RegistrantId = b.Id
								AND b.TypeId = f.Id
								AND b.TypeId != '23'
								AND b.HrTypeId = '{$HrTypeId}'
								AND a.ServiceDate between '{$FromDate}'and '{$ToDate}'  
								AND SUBSTR(UPPER(TRIM(b.LastName)),1,1) between '{$FromName}'and '{$ToName}' 						 
								AND a.UserId = e.UserId
								AND ScheduleStatusId = g.Id
								AND a.ServiceTypeId = h.Id
								AND a.SchoolId = i.Id
								AND i.DistrictId = j.Id
								AND a.PaidFL = '0' 
								AND a.SchoolId like '{$SchoolId}'
								AND a.MandateId = k.Id
								AND k.BillingContractId = '{$BillingContractId}' 
								AND a.StudentId = l.Id
                AND a.SchoolId = m.SchoolId
                AND l.SubSchoolTypeId = m.SubSchoolTypeId
                AND j.BoroughId = n.Id
						ORDER BY b.LastName, b.FirstName, ServiceDateSort, StartTimeNum  ";


    }


	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;  
	// echo $query;

?>

 