<?php 

   require_once("db_GetSetData.php");

	$conn = getCon();

 

     // $query = " SELECT 
	// 		    Id AS id,
	// 		    Id AS ServiceTypeId,
	// 		    ServiceTypeDesc,
	// 		    RegistrantTypeIdMult
	// 		FROM
	// 		    SchServiceTypes
	// 		WHERE RegistrantTypeId = 12
     //        and ServiceCategoryId  != 99  
			 
    $query = " SELECT 
			    Id AS id,
			    Id AS ServiceTypeId,
			    case Id 
                  when 44 then 'Trasportation' 
                  else ServiceTypeDesc
                end as ServiceTypeDesc,  
                 
			    RegistrantTypeIdMult
			FROM
			    SchServiceTypes
			WHERE RegistrantTypeId = 12
            and ServiceCategoryId  != 99 
            and Id != 45  
									";
									
					 	
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	  
	  

?>
