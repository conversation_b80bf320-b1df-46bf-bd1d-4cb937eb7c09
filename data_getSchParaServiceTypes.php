<?php 

 
  	require_once("db_GetSetDataPDO.php");

	$conn = getCon();



 
	    $query = "	SELECT  
					    REPLACE(ServiceTypeDesc, '1:1', '') AS ServiceTypeDesc,
					    CONCAT(Para1to1AMId, ',', Para1to1PMId) AS id,
					    CONCAT(Para1to1AMId, ',', Para1to1PMId) AS ServiceTypeIdList

					FROM
					    SchServiceTypes
					WHERE
					    RegistrantTypeId = 23
					        AND DOEServiceTypeIndId != ''
					        AND Para1to1AMId != ''
               ";

 

	$ret = getData ($conn, $query);

	echo $ret;
 

?>
